"""
异步持仓监控和强平价计算模块
实现实时持仓监控、强平价计算、预警价格计算
严格遵守异步编程原则
"""
import asyncio
import time
from typing import Optional, Dict, List, Callable, Any
from dataclasses import dataclass
from enum import Enum
import logging

from exchanges import BaseExchange, PositionInfo, PositionSide

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级"""
    SAFE = "safe"           # 安全
    WARNING = "warning"     # 警告
    DANGER = "danger"       # 危险
    CRITICAL = "critical"   # 极危险

@dataclass
class PositionRisk:
    """持仓风险信息"""
    symbol: str
    position_side: PositionSide
    current_price: float
    liquidation_price: float
    distance_to_liquidation: float  # 距离强平价的距离
    distance_percentage: float      # 距离强平价的百分比
    risk_level: RiskLevel
    alert_price: float             # 预警价格
    unrealized_pnl: float
    margin_ratio: float            # 保证金比率
    timestamp: int

@dataclass
class PositionAlert:
    """持仓预警"""
    symbol: str
    alert_type: str        # 'liquidation_warning', 'add_position_trigger'
    message: str
    risk_info: PositionRisk
    timestamp: int
    should_add_position: bool = False

class AsyncPositionMonitor:
    """
    异步持仓监控器
    实时监控持仓状态、计算强平价、风险评估
    """
    
    def __init__(self, exchange: BaseExchange):
        """
        初始化持仓监控器
        
        Args:
            exchange: 交易所实例
        """
        self.exchange = exchange
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        self._position_cache: Dict[str, PositionInfo] = {}
        self._risk_cache: Dict[str, PositionRisk] = {}
        self._alert_callbacks: List[Callable] = []
        self._is_monitoring = False

        # 并发安全锁
        self._cache_lock = asyncio.Lock()  # 保护缓存访问
        self._task_lock = asyncio.Lock()   # 保护任务管理
        
        # 风险阈值配置
        self.risk_thresholds = {
            RiskLevel.SAFE: 0.5,        # 距离强平价50%以上
            RiskLevel.WARNING: 0.3,     # 距离强平价30%-50%
            RiskLevel.DANGER: 0.1,      # 距离强平价10%-30%
            RiskLevel.CRITICAL: 0.05    # 距离强平价5%以内
        }
        
        logger.info("异步持仓监控器初始化完成")
    
    async def calculate_liquidation_distance(self, position: PositionInfo, current_price: float) -> tuple[float, float]:
        """
        计算距离强平价的距离
        
        Args:
            position: 持仓信息
            current_price: 当前价格
            
        Returns:
            tuple[float, float]: (绝对距离, 百分比距离)
        """
        try:
            if position.liquidation_price <= 0:
                logger.warning(f"强平价无效: {position.liquidation_price}")
                return 0.0, 0.0
            
            if position.side == PositionSide.LONG:
                # 多仓：当前价格 > 强平价为安全
                distance = current_price - position.liquidation_price
                percentage = distance / current_price if current_price > 0 else 0.0
            else:  # SHORT
                # 空仓：当前价格 < 强平价为安全
                distance = position.liquidation_price - current_price
                percentage = distance / current_price if current_price > 0 else 0.0
            
            return distance, percentage
            
        except Exception as e:
            logger.error(f"计算强平距离异常: {e}")
            return 0.0, 0.0
    
    async def calculate_alert_price(self, position: PositionInfo, alert_points: float) -> float:
        """
        计算预警价格
        
        Args:
            position: 持仓信息
            alert_points: 预警点数
            
        Returns:
            float: 预警价格
        """
        try:
            if position.liquidation_price <= 0:
                return 0.0
            
            if position.side == PositionSide.LONG:
                # 多仓：预警价格 = 强平价 + 预警点数
                alert_price = position.liquidation_price + alert_points
            else:  # SHORT
                # 空仓：预警价格 = 强平价 - 预警点数
                alert_price = position.liquidation_price - alert_points
            
            return max(alert_price, 0.0)
            
        except Exception as e:
            logger.error(f"计算预警价格异常: {e}")
            return 0.0
    
    async def assess_position_risk(self, position: PositionInfo, current_price: float, alert_points: float) -> PositionRisk:
        """
        评估持仓风险
        
        Args:
            position: 持仓信息
            current_price: 当前价格
            alert_points: 预警点数
            
        Returns:
            PositionRisk: 风险评估结果
        """
        try:
            # 计算距离强平价的距离
            distance, percentage = await self.calculate_liquidation_distance(position, current_price)
            
            # 计算预警价格
            alert_price = await self.calculate_alert_price(position, alert_points)
            
            # 评估风险等级
            risk_level = RiskLevel.SAFE
            abs_percentage = abs(percentage)
            
            if abs_percentage <= self.risk_thresholds[RiskLevel.CRITICAL]:
                risk_level = RiskLevel.CRITICAL
            elif abs_percentage <= self.risk_thresholds[RiskLevel.DANGER]:
                risk_level = RiskLevel.DANGER
            elif abs_percentage <= self.risk_thresholds[RiskLevel.WARNING]:
                risk_level = RiskLevel.WARNING
            
            # 计算保证金比率
            margin_ratio = position.margin / (position.size * current_price) if position.size > 0 and current_price > 0 else 0.0
            
            return PositionRisk(
                symbol=position.symbol,
                position_side=position.side,
                current_price=current_price,
                liquidation_price=position.liquidation_price,
                distance_to_liquidation=distance,
                distance_percentage=percentage,
                risk_level=risk_level,
                alert_price=alert_price,
                unrealized_pnl=position.unrealized_pnl,
                margin_ratio=margin_ratio,
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            logger.error(f"评估持仓风险异常: {e}")
            return PositionRisk(
                symbol=position.symbol,
                position_side=position.side,
                current_price=current_price,
                liquidation_price=0.0,
                distance_to_liquidation=0.0,
                distance_percentage=0.0,
                risk_level=RiskLevel.CRITICAL,
                alert_price=0.0,
                unrealized_pnl=position.unrealized_pnl,
                margin_ratio=0.0,
                timestamp=int(time.time() * 1000)
            )
    
    async def monitor_single_position(self, symbol: str, alert_points: float, check_interval: int = 10) -> None:
        """
        监控单个持仓
        
        Args:
            symbol: 交易对符号
            alert_points: 预警点数
            check_interval: 检查间隔（秒）
        """
        logger.info(f"开始监控持仓: {symbol}, 预警点数: {alert_points}")
        
        while self._is_monitoring:
            try:
                # 获取持仓信息
                positions = await self.exchange.get_positions(symbol)
                if not positions:
                    logger.debug(f"没有找到{symbol}的持仓")
                    await asyncio.sleep(check_interval)
                    continue
                
                # 获取当前价格
                current_price = await self.exchange.get_current_price(symbol)
                if current_price <= 0:
                    logger.warning(f"无法获取{symbol}的当前价格")
                    await asyncio.sleep(check_interval)
                    continue
                
                for position in positions:
                    # 评估风险
                    risk = await self.assess_position_risk(position, current_price, alert_points)
                    
                    # 更新缓存
                    await self.update_position_cache(symbol, position)
                    await self.update_risk_cache(symbol, risk)
                    
                    # 检查是否需要发出预警
                    await self._check_and_send_alerts(risk, current_price, alert_points)
                
                await asyncio.sleep(check_interval)
                
            except asyncio.CancelledError:
                logger.info(f"停止监控持仓: {symbol}")
                break
            except Exception as e:
                logger.error(f"监控持仓{symbol}异常: {e}")
                await asyncio.sleep(check_interval)
    
    async def _check_and_send_alerts(self, risk: PositionRisk, current_price: float, alert_points: float) -> None:
        """
        检查并发送预警
        
        Args:
            risk: 风险信息
            current_price: 当前价格
            alert_points: 预警点数
        """
        try:
            alerts = []
            
            # 强平风险预警
            if risk.risk_level == RiskLevel.CRITICAL:
                alerts.append(PositionAlert(
                    symbol=risk.symbol,
                    alert_type='liquidation_warning',
                    message=f'极危险！{risk.symbol}距离强平价仅{abs(risk.distance_percentage)*100:.2f}%',
                    risk_info=risk,
                    timestamp=risk.timestamp
                ))
            elif risk.risk_level == RiskLevel.DANGER:
                alerts.append(PositionAlert(
                    symbol=risk.symbol,
                    alert_type='liquidation_warning',
                    message=f'危险！{risk.symbol}距离强平价{abs(risk.distance_percentage)*100:.2f}%',
                    risk_info=risk,
                    timestamp=risk.timestamp
                ))
            
            # 加仓触发预警
            should_add = await self._should_trigger_add_position(risk, current_price, alert_points)
            if should_add:
                alerts.append(PositionAlert(
                    symbol=risk.symbol,
                    alert_type='add_position_trigger',
                    message=f'触发加仓条件！{risk.symbol}当前价格{current_price}接近预警价格{risk.alert_price}',
                    risk_info=risk,
                    timestamp=risk.timestamp,
                    should_add_position=True
                ))
            
            # 发送预警
            for alert in alerts:
                await self._send_alert(alert)
                
        except Exception as e:
            logger.error(f"检查预警异常: {e}")
    
    async def _should_trigger_add_position(self, risk: PositionRisk, current_price: float, alert_points: float) -> bool:
        """
        判断是否应该触发加仓
        
        Args:
            risk: 风险信息
            current_price: 当前价格
            alert_points: 预警点数
            
        Returns:
            bool: 是否应该加仓
        """
        try:
            if risk.alert_price <= 0:
                return False
            
            if risk.position_side == PositionSide.LONG:
                # 多仓：当前价格下跌到预警价格时触发加仓
                return current_price <= risk.alert_price
            else:  # SHORT
                # 空仓：当前价格上涨到预警价格时触发加仓
                return current_price >= risk.alert_price
                
        except Exception as e:
            logger.error(f"判断加仓触发异常: {e}")
            return False
    
    async def _send_alert(self, alert: PositionAlert) -> None:
        """
        发送预警
        
        Args:
            alert: 预警信息
        """
        try:
            logger.warning(f"持仓预警: {alert.message}")
            
            # 调用所有注册的回调函数
            for callback in self._alert_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(alert)
                    else:
                        callback(alert)
                except Exception as e:
                    logger.error(f"预警回调异常: {e}")
                    
        except Exception as e:
            logger.error(f"发送预警异常: {e}")
    
    async def start_monitoring(self, symbols: List[str], alert_points: float, check_interval: int = 10) -> None:
        """
        开始监控多个持仓
        
        Args:
            symbols: 交易对列表
            alert_points: 预警点数
            check_interval: 检查间隔（秒）
        """
        if self._is_monitoring:
            logger.warning("监控已在运行中")
            return
        
        self._is_monitoring = True
        logger.info(f"开始监控持仓: {symbols}")
        
        # 为每个交易对创建监控任务
        for symbol in symbols:
            task = asyncio.create_task(
                self.monitor_single_position(symbol, alert_points, check_interval)
            )
            self._monitoring_tasks[symbol] = task
    
    async def stop_monitoring(self) -> None:
        """停止监控"""
        self._is_monitoring = False
        
        # 取消所有监控任务
        for symbol, task in self._monitoring_tasks.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self._monitoring_tasks.clear()
        logger.info("已停止所有持仓监控")
    
    def add_alert_callback(self, callback: Callable) -> None:
        """添加预警回调函数"""
        self._alert_callbacks.append(callback)
        logger.info("添加预警回调函数")
    
    def remove_alert_callback(self, callback: Callable) -> None:
        """移除预警回调函数"""
        if callback in self._alert_callbacks:
            self._alert_callbacks.remove(callback)
            logger.info("移除预警回调函数")
    
    def get_position_risk(self, symbol: str) -> Optional[PositionRisk]:
        """获取持仓风险信息"""
        return self._risk_cache.get(symbol)
    
    def get_all_risks(self) -> Dict[str, PositionRisk]:
        """获取所有持仓风险信息"""
        return self._risk_cache.copy()
    
    def get_position_info(self, symbol: str) -> Optional[PositionInfo]:
        """获取持仓信息"""
        return self._position_cache.get(symbol)
    
    async def force_update_position(self, symbol: str, alert_points: float) -> Optional[PositionRisk]:
        """
        强制更新持仓信息
        
        Args:
            symbol: 交易对符号
            alert_points: 预警点数
            
        Returns:
            Optional[PositionRisk]: 更新后的风险信息
        """
        try:
            positions = await self.exchange.get_positions(symbol)
            if not positions:
                return None

            current_price = await self.exchange.get_current_price(symbol)
            if current_price <= 0:
                return None

            position = positions[0]  # 假设只有一个持仓
            risk = await self.assess_position_risk(position, current_price, alert_points)

            # 更新缓存
            await self.update_position_cache(symbol, position)
            await self.update_risk_cache(symbol, risk)

            return risk
        except Exception as e:
            logger.error(f"强制更新持仓信息时发生异常: {e}")
            return None

    # ==================== 线程安全的缓存管理方法 ====================

    async def update_position_cache(self, symbol: str, position: PositionInfo) -> None:
        """线程安全地更新持仓缓存"""
        async with self._cache_lock:
            self._position_cache[symbol] = position

    async def update_risk_cache(self, symbol: str, risk: PositionRisk) -> None:
        """线程安全地更新风险缓存"""
        async with self._cache_lock:
            self._risk_cache[symbol] = risk

    async def get_position_cache(self, symbol: str) -> Optional[PositionInfo]:
        """线程安全地获取持仓缓存"""
        async with self._cache_lock:
            return self._position_cache.get(symbol)

    async def get_risk_cache(self, symbol: str) -> Optional[PositionRisk]:
        """线程安全地获取风险缓存"""
        async with self._cache_lock:
            return self._risk_cache.get(symbol)

    async def clear_cache(self, symbol: str) -> None:
        """线程安全地清理缓存"""
        async with self._cache_lock:
            self._position_cache.pop(symbol, None)
            self._risk_cache.pop(symbol, None)

    async def add_monitoring_task(self, symbol: str, task: asyncio.Task) -> None:
        """线程安全地添加监控任务"""
        async with self._task_lock:
            # 如果已有任务，先取消
            if symbol in self._monitoring_tasks:
                old_task = self._monitoring_tasks[symbol]
                if not old_task.done():
                    old_task.cancel()

            self._monitoring_tasks[symbol] = task

    async def remove_monitoring_task(self, symbol: str) -> None:
        """线程安全地移除监控任务"""
        async with self._task_lock:
            task = self._monitoring_tasks.pop(symbol, None)
            if task and not task.done():
                task.cancel()

        except Exception as e:
            logger.error(f"强制更新持仓信息异常: {e}")
            return None
