"""
异步交易所抽象基类
严格遵守交易所完全分离原则
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"

class PositionSide(Enum):
    """持仓方向"""
    LONG = "long"
    SHORT = "short"

@dataclass
class KlineData:
    """K线数据结构"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float

@dataclass
class PositionInfo:
    """持仓信息结构"""
    symbol: str
    side: PositionSide
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    liquidation_price: float
    margin: float
    leverage: int

@dataclass
class OrderInfo:
    """订单信息结构"""
    order_id: str
    symbol: str
    side: OrderSide
    type: OrderType
    amount: float
    price: Optional[float]
    status: str
    filled: float
    remaining: float
    timestamp: int

class BaseExchange(ABC):
    """
    异步交易所抽象基类
    所有交易所适配器必须继承此类并实现所有抽象方法
    """
    
    def __init__(self, api_key: str, api_secret: str, passphrase: Optional[str] = None, sandbox: bool = False):
        """
        初始化交易所连接

        Args:
            api_key: API密钥
            api_secret: API密钥
            passphrase: API密码短语（OKX需要）
            sandbox: 是否使用沙盒环境
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.passphrase = passphrase
        self.sandbox = sandbox
        self._session = None
        self._ws_connection = None
        self._is_connected = False
        self._background_tasks = set()  # 跟踪后台任务
        self._cleanup_timeout = 10.0  # 清理超时时间
        
    @abstractmethod
    async def connect(self) -> bool:
        """
        建立异步连接
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """断开连接"""
        pass
    
    @abstractmethod
    async def get_klines(self, symbol: str, timeframe: str, limit: int = 100) -> List[KlineData]:
        """
        获取K线数据

        Args:
            symbol: 交易对符号
            timeframe: 时间周期 (支持: 1m, 5m, 10m, 15m, 30m, 1h, 4h, 1d, 15d, 1M)
            limit: 数据条数

        Returns:
            List[KlineData]: K线数据列表
        """
        pass

    def get_supported_timeframes(self) -> List[str]:
        """
        获取支持的时间周期列表

        Returns:
            List[str]: 支持的时间周期列表
        """
        return ["1m", "5m", "10m", "15m", "30m", "1h", "4h", "1d", "15d", "1M"]

    def validate_timeframe(self, timeframe: str) -> bool:
        """
        验证时间周期是否支持

        Args:
            timeframe: 时间周期

        Returns:
            bool: 是否支持
        """
        return timeframe in self.get_supported_timeframes()
    
    @abstractmethod
    async def get_current_price(self, symbol: str) -> float:
        """
        获取当前价格
        
        Args:
            symbol: 交易对符号
            
        Returns:
            float: 当前价格
        """
        pass
    
    @abstractmethod
    async def get_positions(self, symbol: Optional[str] = None) -> List[PositionInfo]:
        """
        获取持仓信息
        
        Args:
            symbol: 交易对符号，None表示获取所有持仓
            
        Returns:
            List[PositionInfo]: 持仓信息列表
        """
        pass
    
    @abstractmethod
    async def place_market_order(self, symbol: str, side: OrderSide, amount: float,
                                position_side: Optional[PositionSide] = None,
                                reduce_only: bool = False) -> OrderInfo:
        """
        下市价单

        Args:
            symbol: 交易对符号
            side: 订单方向
            amount: 订单数量
            position_side: 持仓方向（期货需要）
            reduce_only: 是否为平仓单（期货需要）

        Returns:
            OrderInfo: 订单信息
        """
        pass
    
    @abstractmethod
    async def place_limit_order(self, symbol: str, side: OrderSide, amount: float,
                               price: float, position_side: Optional[PositionSide] = None,
                               reduce_only: bool = False) -> OrderInfo:
        """
        下限价单

        Args:
            symbol: 交易对符号
            side: 订单方向
            amount: 订单数量
            price: 订单价格
            position_side: 持仓方向（期货需要）
            reduce_only: 是否为平仓单（期货需要）

        Returns:
            OrderInfo: 订单信息
        """
        pass
    
    @abstractmethod
    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """
        取消订单
        
        Args:
            symbol: 交易对符号
            order_id: 订单ID
            
        Returns:
            bool: 是否取消成功
        """
        pass
    
    @abstractmethod
    async def get_order_status(self, symbol: str, order_id: str) -> OrderInfo:
        """
        获取订单状态
        
        Args:
            symbol: 交易对符号
            order_id: 订单ID
            
        Returns:
            OrderInfo: 订单信息
        """
        pass
    
    @abstractmethod
    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """
        设置杠杆
        
        Args:
            symbol: 交易对符号
            leverage: 杠杆倍数
            
        Returns:
            bool: 设置是否成功
        """
        pass
    
    @abstractmethod
    async def get_account_balance(self) -> Dict[str, float]:
        """
        获取账户余额

        Returns:
            Dict[str, float]: 各币种余额
        """
        pass

    async def get_account_info(self) -> Optional[Dict]:
        """
        获取账户信息（包含余额和其他信息）

        Returns:
            Optional[Dict]: 账户信息，失败时返回None
        """
        try:
            balance = await self.get_account_balance()
            if balance:
                # 计算总余额和可用余额
                total_balance = sum(balance.values())
                available_balance = balance.get('USDT', 0.0)  # 默认使用USDT作为可用余额

                return {
                    'total_balance': total_balance,
                    'available_balance': available_balance,
                    'currency': 'USDT',
                    'balances': balance
                }
            return None
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return None
    
    @abstractmethod
    async def subscribe_price_stream(self, symbol: str, callback) -> None:
        """
        订阅价格流
        
        Args:
            symbol: 交易对符号
            callback: 价格更新回调函数
        """
        pass
    
    @abstractmethod
    async def subscribe_position_stream(self, callback) -> None:
        """
        订阅持仓流
        
        Args:
            callback: 持仓更新回调函数
        """
        pass
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口 - 确保所有资源正确清理"""
        await self.cleanup_resources()

    def add_background_task(self, task: asyncio.Task) -> None:
        """添加需要跟踪的后台任务"""
        self._background_tasks.add(task)
        # 任务完成时自动从集合中移除
        task.add_done_callback(self._background_tasks.discard)

    async def cleanup_resources(self) -> None:
        """清理所有异步资源"""
        try:
            logger.info("开始清理交易所资源...")

            # 1. 取消所有后台任务
            if self._background_tasks:
                logger.info(f"取消 {len(self._background_tasks)} 个后台任务")
                for task in list(self._background_tasks):
                    if not task.done():
                        task.cancel()

                # 等待任务取消完成，但设置超时
                if self._background_tasks:
                    try:
                        await asyncio.wait_for(
                            asyncio.gather(*self._background_tasks, return_exceptions=True),
                            timeout=self._cleanup_timeout / 2
                        )
                    except asyncio.TimeoutError:
                        logger.warning("部分后台任务取消超时")
                    except Exception as e:
                        logger.warning(f"取消后台任务时发生异常: {e}")

                self._background_tasks.clear()

            # 2. 关闭WebSocket连接
            if self._ws_connection:
                try:
                    logger.info("关闭WebSocket连接")
                    await asyncio.wait_for(
                        self._ws_connection.close(),
                        timeout=self._cleanup_timeout / 4
                    )
                except asyncio.TimeoutError:
                    logger.warning("WebSocket关闭超时")
                except Exception as e:
                    logger.warning(f"关闭WebSocket时发生异常: {e}")
                finally:
                    self._ws_connection = None

            # 3. 关闭HTTP会话
            if self._session and not self._session.closed:
                try:
                    logger.info("关闭HTTP会话")
                    await asyncio.wait_for(
                        self._session.close(),
                        timeout=self._cleanup_timeout / 4
                    )
                    # 等待连接器完全关闭
                    await asyncio.sleep(0.1)
                except asyncio.TimeoutError:
                    logger.warning("HTTP会话关闭超时")
                except Exception as e:
                    logger.warning(f"关闭HTTP会话时发生异常: {e}")
                finally:
                    self._session = None

            # 4. 调用子类的断开连接方法
            try:
                await asyncio.wait_for(
                    self.disconnect(),
                    timeout=self._cleanup_timeout / 4
                )
            except asyncio.TimeoutError:
                logger.warning("断开连接超时")
            except Exception as e:
                logger.warning(f"断开连接时发生异常: {e}")

            # 5. 更新连接状态
            self._is_connected = False
            logger.info("交易所资源清理完成")

        except Exception as e:
            logger.error(f"清理交易所资源时发生异常: {e}")
            # 即使清理失败，也要确保状态正确
            self._is_connected = False

    @property
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._is_connected

    def normalize_symbol(self, symbol: str) -> str:
        """
        标准化交易对符号格式

        Args:
            symbol: 原始交易对符号

        Returns:
            str: 标准化后的交易对符号
        """
        # 基础实现，子类可以重写
        return symbol.upper().strip()

    def validate_symbol_format(self, symbol: str) -> bool:
        """
        验证交易对格式是否正确

        Args:
            symbol: 交易对符号

        Returns:
            bool: 格式是否正确
        """
        # 基础验证，子类可以重写
        if not symbol or not isinstance(symbol, str):
            return False

        symbol = symbol.strip()
        if len(symbol) < 3:
            return False

        return True

    async def get_position_stop_loss_price(self, symbol: str) -> dict:
        """
        获取持仓的止损价格

        Args:
            symbol: 交易对符号

        Returns:
            dict: {
                'success': bool,
                'stop_loss_price': float,
                'timestamp': datetime,
                'error': str
            }
        """
        # 基础实现，子类需要重写
        return {
            'success': False,
            'stop_loss_price': 0.0,
            'timestamp': None,
            'error': '基础交易所类不支持获取止损价格'
        }
