#!/usr/bin/env python3
"""
验证插针策略集成状态
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_pinbar_integration():
    """验证插针策略集成状态"""
    print("🔍 验证插针策略集成状态")
    print("=" * 60)
    
    verification_results = []
    
    # 1. 验证文件存在性
    print("\n📁 检查文件存在性:")
    
    required_files = [
        "indicators/pinbar_calculator.py",
        "strategies/pinbar_strategy.py",
        "gui/main_window.py"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
            verification_results.append(True)
        else:
            print(f"   ❌ {file_path} - 文件不存在")
            verification_results.append(False)
    
    # 2. 验证导入功能
    print("\n📦 检查导入功能:")
    
    try:
        from indicators.pinbar_calculator import PinBarCalculator, PinBarSignal, PinBarType, PinBarData
        print("   ✅ 插针计算器导入成功")
        verification_results.append(True)
    except Exception as e:
        print(f"   ❌ 插针计算器导入失败: {e}")
        verification_results.append(False)
    
    try:
        from strategies.pinbar_strategy import PinBarStrategy, PinBarConfig
        print("   ✅ 插针策略导入成功")
        verification_results.append(True)
    except Exception as e:
        print(f"   ❌ 插针策略导入失败: {e}")
        verification_results.append(False)
    
    # 3. 验证GUI集成
    print("\n🎨 检查GUI集成:")
    
    try:
        with open("gui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 检查标签页创建调用
        if "self.create_pinbar_tab()" in content:
            print("   ✅ 插针标签页创建调用已添加")
            verification_results.append(True)
        else:
            print("   ❌ 插针标签页创建调用未找到")
            verification_results.append(False)
        
        # 检查标签页创建方法
        if "def create_pinbar_tab(self):" in content:
            print("   ✅ 插针标签页创建方法已实现")
            verification_results.append(True)
        else:
            print("   ❌ 插针标签页创建方法未找到")
            verification_results.append(False)
        
        # 检查控制方法
        control_methods = [
            "def start_pinbar_strategy(self):",
            "def stop_pinbar_strategy(self):",
            "def analyze_pinbar_opportunity(self):"
        ]
        
        for method in control_methods:
            if method in content:
                print(f"   ✅ {method.split('(')[0].replace('def ', '')} 方法已实现")
                verification_results.append(True)
            else:
                print(f"   ❌ {method.split('(')[0].replace('def ', '')} 方法未找到")
                verification_results.append(False)
        
        # 检查变量初始化
        if "def init_pinbar_variables(self):" in content:
            print("   ✅ 插针变量初始化方法已实现")
            verification_results.append(True)
        else:
            print("   ❌ 插针变量初始化方法未找到")
            verification_results.append(False)
            
    except Exception as e:
        print(f"   ❌ GUI集成检查失败: {e}")
        verification_results.extend([False] * 6)
    
    # 4. 验证功能完整性
    print("\n🔧 检查功能完整性:")
    
    try:
        # 测试插针计算器
        calculator = PinBarCalculator()
        print(f"   ✅ 插针计算器创建成功，需要数据点: {calculator.get_required_data_count()}")
        verification_results.append(True)
        
        # 测试插针配置
        config = PinBarConfig()
        print(f"   ✅ 插针配置创建成功: {config.custom_symbol}")
        verification_results.append(True)
        
        # 测试插针策略
        strategy = PinBarStrategy(config)
        print("   ✅ 插针策略创建成功")
        verification_results.append(True)
        
    except Exception as e:
        print(f"   ❌ 功能完整性检查失败: {e}")
        verification_results.extend([False] * 3)
    
    # 5. 验证配置参数
    print("\n⚙️ 检查配置参数:")
    
    try:
        config = PinBarConfig()
        
        # 检查关键配置参数
        key_params = [
            'min_body_ratio', 'min_shadow_ratio', 'custom_symbol', 
            'custom_leverage', 'min_signal_strength', 'min_pin_quality'
        ]
        
        for param in key_params:
            if hasattr(config, param):
                value = getattr(config, param)
                print(f"   ✅ {param}: {value}")
                verification_results.append(True)
            else:
                print(f"   ❌ {param}: 参数不存在")
                verification_results.append(False)
                
    except Exception as e:
        print(f"   ❌ 配置参数检查失败: {e}")
        verification_results.extend([False] * 6)
    
    # 6. 验证插针类型
    print("\n📊 检查插针类型:")
    
    try:
        pin_types = [PinBarType.BULLISH_PIN, PinBarType.BEARISH_PIN, 
                    PinBarType.DOJI_PIN, PinBarType.NO_PIN]
        
        for pin_type in pin_types:
            print(f"   ✅ {pin_type.value}")
            verification_results.append(True)
            
    except Exception as e:
        print(f"   ❌ 插针类型检查失败: {e}")
        verification_results.extend([False] * 4)
    
    # 7. 验证信号类型
    print("\n🎯 检查信号类型:")
    
    try:
        signal_types = [PinBarSignal.STRONG_BULLISH, PinBarSignal.WEAK_BULLISH,
                       PinBarSignal.NEUTRAL, PinBarSignal.WEAK_BEARISH, 
                       PinBarSignal.STRONG_BEARISH]
        
        for signal_type in signal_types:
            print(f"   ✅ {signal_type.value}")
            verification_results.append(True)
            
    except Exception as e:
        print(f"   ❌ 信号类型检查失败: {e}")
        verification_results.extend([False] * 5)
    
    # 统计结果
    print("\n📊 验证结果统计:")
    total_checks = len(verification_results)
    passed_checks = sum(verification_results)
    failed_checks = total_checks - passed_checks
    success_rate = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"   总检查项: {total_checks}")
    print(f"   通过项: {passed_checks}")
    print(f"   失败项: {failed_checks}")
    print(f"   成功率: {success_rate:.1f}%")
    
    # 最终结论
    print("\n🏆 最终结论:")
    if success_rate >= 95:
        print("   🎉 插针策略集成完美！所有功能正常工作")
        status = "EXCELLENT"
    elif success_rate >= 85:
        print("   ✅ 插针策略集成良好！大部分功能正常工作")
        status = "GOOD"
    elif success_rate >= 70:
        print("   ⚠️ 插针策略集成基本完成，但有一些问题需要修复")
        status = "FAIR"
    else:
        print("   ❌ 插针策略集成存在严重问题，需要重新检查")
        status = "POOR"
    
    print(f"\n📋 集成状态: {status}")
    print(f"📅 验证时间: {sys.version}")
    
    return success_rate >= 85

def main():
    """主函数"""
    print("🚀 开始验证插针策略集成")
    
    try:
        result = verify_pinbar_integration()
        
        if result:
            print("\n✅ 验证完成：插针策略集成成功")
            return True
        else:
            print("\n❌ 验证完成：插针策略集成存在问题")
            return False
            
    except Exception as e:
        print(f"\n❌ 验证过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
