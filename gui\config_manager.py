"""
GUI配置管理器
用于保存和加载用户配置
"""
import json
import os
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "user_config.json"):
        self.config_file = config_file
        self.default_config = {
            # 交易所配置
            "exchange": "okx",
            "api_key": "",
            "api_secret": "",
            "passphrase": "",
            "sandbox": True,
            
            # 交易参数 (期货合约)
            "symbol": "BTC-USDT-SWAP",  # 默认使用OKX永续合约格式
            "leverage": 10,
            "initial_margin": 100.0,
            "timeframe": "30m",
            
            # MACD参数
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            
            # MACD策略参数
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            "min_signal_strength": 0.3,
            "macd_enabled": True,

            # 多时间周期趋势确认
            "multi_timeframe_confirmation": [],  # 默认不启用任何确认周期

            # 资金风险管理参数
            "max_open_positions": 10,      # 最大红开（最大开仓数量）
            "margin_ratio": 0.8,           # 保证金率（0.1-1.0）
            "max_position_ratio": 0.1,     # 最大合仓比例（0.01-1.0）
            "daily_pnl_limit": 0.2,        # 日盈亏限制（0.01-1.0）
            "max_open_amount": 4,           # 最大开仓数量
            "risk_control_enabled": True,   # 风险控制启用状态
            "emergency_stop": False,        # 紧急停止状态

            # 风险管理
            "alert_points": 0.5,
            "add_position_types": ["half"],  # 支持多选，默认半量加仓
            "max_add_times": 3,
            
            # 监控参数
            "price_check_interval": 5,
            "position_check_interval": 10,
            
            # 界面设置
            "auto_start": False,
            "show_advanced": False,

            # 布林带策略配置 (基于ETH/USDT案例优化)
            "bollinger_enabled": False,
            "bb_period": 20,
            "bb_std_dev": 2.0,
            "bb_trigger_distance_points": 50,
            "bb_trigger_distance_percent": 1.0,
            "bb_use_points_trigger": True,
            "bb_add_position_types": ["equal"],
            "bb_max_add_count": 3,
            "bb_add_intervals": [5.0, 8.0, 12.0, 20.0],
            "bb_max_total_loss_percent": 12.0,  # 基于案例调整：更保守的总亏损限制
            "bb_max_investment_ratio": 0.15,    # 基于案例调整：15%最大投入比例
            "bb_enable_emergency_stop": True,
            "bb_require_signal": True,
            "bb_signal_timeframe": "1h",        # 信号确认时间周期
            "bb_require_signal_for_opening": True,   # 开仓时需要信号确认
            "bb_require_signal_for_adding": False,   # 加仓时不需要信号确认
            "bb_enable_emergency_add": True,         # 启用紧急加仓
            "bb_emergency_distance_ratio": 0.8,     # 紧急加仓距离阈值(止损距离的80%)
            "bb_allowed_signals": ["three_lines_down", "price_touch_lower"],
            "bb_take_profit_percent": 1.5,      # 基于案例调整：更现实的止盈目标
            "bb_stop_loss_percent": 5.6,        # 基于案例调整：与ETH案例一致的止损
            "bb_trailing_stop": False,

            # 无持仓时立即开仓功能
            "bb_immediate_open": False,          # 启用无持仓时立即开仓
            "bb_check_interval": 30,             # 持仓检测间隔(秒)

            # 日志自动清理功能
            "log_auto_cleanup": True,            # 启用自动清理日志
            "log_cleanup_interval": 3600,       # 清理间隔(秒)，默认1小时
            "log_retention_hours": 1,            # 日志保留时间(小时)
            "log_min_keep_count": 100            # 最少保留日志条数
        }
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 迁移旧版本配置
                migrated_config = self.migrate_old_config(config)

                # 合并默认配置（确保所有字段都存在）
                merged_config = self.default_config.copy()
                merged_config.update(migrated_config)

                logger.info(f"配置已从 {self.config_file} 加载并迁移")
                return merged_config
            else:
                logger.info("配置文件不存在，使用默认配置")
                return self.default_config.copy()
                
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
    
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证配置"""
        try:
            # 检查必要字段
            if not config.get("api_key") or not config.get("api_secret"):
                return False, "API_KEY和API_SECRET不能为空"

            if config.get("exchange") == "okx" and not config.get("passphrase"):
                return False, "OKX交易所需要设置PASSPHRASE"

            if config.get("exchange") not in ["okx", "gateio"]:
                return False, "交易所必须是'okx'或'gateio'"

            if config.get("initial_margin", 0) <= 0:
                return False, "初始保证金必须大于0"

            if config.get("leverage", 0) <= 0:
                return False, "杠杆必须大于0"

            if config.get("alert_points", 0) <= 0:
                return False, "预警点数必须大于0"

            # 验证时间周期
            timeframe = config.get("timeframe", "30m")
            is_valid_tf, tf_message = self.validate_timeframe(timeframe)
            if not is_valid_tf:
                return False, tf_message

            # 验证交易所特定的时间周期支持
            exchange = config.get("exchange", "okx")
            if exchange == "gateio" and timeframe in ["15d", "1M"]:
                return False, f"Gate.io交易所不支持时间周期 {timeframe}，请选择其他周期"

            # 验证加仓类型（支持多选）
            add_position_types = config.get("add_position_types", ["half"])
            if not isinstance(add_position_types, list) or not add_position_types:
                return False, "加仓类型必须是非空列表"

            for add_type in add_position_types:
                is_valid_apt, apt_message = self.validate_add_position_type(add_type)
                if not is_valid_apt:
                    return False, f"加仓类型验证失败: {apt_message}"

            # 验证MACD参数
            macd_fast = config.get("macd_fast", 12)
            macd_slow = config.get("macd_slow", 26)
            macd_signal = config.get("macd_signal", 9)

            if not isinstance(macd_fast, int) or macd_fast <= 0:
                return False, "MACD快线周期必须是正整数"

            if not isinstance(macd_slow, int) or macd_slow <= 0:
                return False, "MACD慢线周期必须是正整数"

            if not isinstance(macd_signal, int) or macd_signal <= 0:
                return False, "MACD信号线周期必须是正整数"

            if macd_fast >= macd_slow:
                return False, "MACD快线周期必须小于慢线周期"

            min_signal_strength = config.get("min_signal_strength", 0.3)
            if not isinstance(min_signal_strength, (int, float)) or min_signal_strength <= 0 or min_signal_strength > 1:
                return False, "最小信号强度必须是0到1之间的数值"

            # 验证多时间周期确认
            main_timeframe = config.get("timeframe", "30m")
            confirmation_timeframes = config.get("multi_timeframe_confirmation", [])
            is_valid_mtf, mtf_message = self.validate_multi_timeframe_confirmation(main_timeframe, confirmation_timeframes)
            if not is_valid_mtf:
                return False, f"多时间周期确认验证失败: {mtf_message}"

            # 验证资金风险管理参数
            is_valid_risk, risk_message = self.validate_risk_management_params(config)
            if not is_valid_risk:
                return False, f"资金风险管理参数验证失败: {risk_message}"

            # 验证布林带策略参数(如果启用)
            if config.get("bollinger_enabled", False):
                is_valid_bb, bb_message = self.validate_bollinger_strategy_params(config)
                if not is_valid_bb:
                    return False, f"布林带策略参数验证失败: {bb_message}"

            return True, "配置验证通过"

        except Exception as e:
            return False, f"配置验证异常: {str(e)}"
    
    def get_exchange_list(self) -> list:
        """获取支持的交易所列表"""
        return ["okx", "gateio"]
    
    def get_symbol_list(self) -> list:
        """获取常用期货合约交易对列表 (OKX格式)"""
        return [
            "BTC-USDT-SWAP", "ETH-USDT-SWAP", "SOL-USDT-SWAP", "ADA-USDT-SWAP",
            "DOT-USDT-SWAP", "AVAX-USDT-SWAP", "MATIC-USDT-SWAP", "LINK-USDT-SWAP",
            "UNI-USDT-SWAP", "LTC-USDT-SWAP", "XRP-USDT-SWAP", "ATOM-USDT-SWAP"
        ]
    
    def get_timeframe_list(self) -> list:
        """获取时间周期列表"""
        return [
            "1m",   # 1分钟
            "5m",   # 5分钟
            "10m",  # 10分钟
            "15m",  # 15分钟
            "30m",  # 30分钟
            "1h",   # 1小时
            "4h",   # 4小时
            "1d",   # 1日
            "15d",  # 15日
            "1M"    # 1月
        ]

    def get_timeframe_display_names(self) -> dict:
        """获取时间周期显示名称映射"""
        return {
            "1m": "1分钟 (1m)",
            "5m": "5分钟 (5m)",
            "10m": "10分钟 (10m)",
            "15m": "15分钟 (15m)",
            "30m": "30分钟 (30m)",
            "1h": "1小时 (1h)",
            "4h": "4小时 (4h)",
            "1d": "1日 (1d)",
            "15d": "15日 (15d)",
            "1M": "1月 (1M)"
        }

    def validate_timeframe(self, timeframe: str) -> tuple[bool, str]:
        """验证时间周期有效性"""
        valid_timeframes = self.get_timeframe_list()
        if timeframe not in valid_timeframes:
            return False, f"无效的时间周期: {timeframe}，支持的周期: {', '.join(valid_timeframes)}"
        return True, "时间周期有效"
    
    def get_add_position_types(self) -> list:
        """获取加仓类型列表（英文代码）"""
        return ["equal", "half", "custom"]

    def get_add_position_type_display_names(self) -> dict:
        """获取加仓类型显示名称映射"""
        return {
            "equal": "等量加仓 (equal)",
            "half": "半量加仓 (half)",
            "custom": "自定义比例 (custom)"
        }

    def get_add_position_type_descriptions(self) -> dict:
        """获取加仓类型详细说明"""
        return {
            "equal": "使用与初始开仓相同的保证金金额进行加仓",
            "half": "使用初始保证金的一半金额进行加仓",
            "custom": "使用自定义比例的保证金进行加仓"
        }

    def validate_add_position_type(self, add_type: str) -> tuple[bool, str]:
        """验证加仓类型有效性"""
        valid_types = self.get_add_position_types()
        if add_type not in valid_types:
            return False, f"无效的加仓类型: {add_type}，支持的类型: {', '.join(valid_types)}"
        return True, "加仓类型有效"

    def get_multi_timeframe_options(self) -> list:
        """获取多时间周期确认选项"""
        return ["15m", "30m", "1h", "2h", "4h", "1d"]

    def get_multi_timeframe_display_names(self) -> dict:
        """获取多时间周期确认显示名称映射"""
        return {
            "15m": "15分钟趋势确认",
            "30m": "30分钟趋势确认",
            "1h": "1小时趋势确认",
            "2h": "2小时趋势确认",
            "4h": "4小时趋势确认",
            "1d": "1日趋势确认"
        }

    def validate_multi_timeframe_confirmation(self, main_timeframe: str, confirmation_timeframes: list) -> tuple[bool, str]:
        """验证多时间周期确认配置"""
        if not isinstance(confirmation_timeframes, list):
            return False, "多时间周期确认必须是列表格式"

        # 如果没有选择确认周期，则有效
        if not confirmation_timeframes:
            return True, "未启用多时间周期确认"

        # 验证所有确认周期都是有效的
        valid_timeframes = self.get_multi_timeframe_options()
        for tf in confirmation_timeframes:
            if tf not in valid_timeframes:
                return False, f"无效的确认时间周期: {tf}"

        # 验证确认周期必须大于或等于主时间周期
        timeframe_order = {
            "1m": 1, "5m": 5, "10m": 10, "15m": 15, "30m": 30,
            "1h": 60, "2h": 120, "4h": 240, "1d": 1440, "15d": 21600, "1M": 43200
        }

        main_minutes = timeframe_order.get(main_timeframe, 0)
        if main_minutes == 0:
            return False, f"无法识别主时间周期: {main_timeframe}"

        for tf in confirmation_timeframes:
            tf_minutes = timeframe_order.get(tf, 0)
            if tf_minutes <= main_minutes:
                return False, f"确认时间周期 {tf} 必须大于主时间周期 {main_timeframe}"

        return True, "多时间周期确认配置有效"

    def validate_risk_management_params(self, config: dict) -> tuple[bool, str]:
        """验证资金风险管理参数"""
        try:
            # 验证最大开仓数量
            max_open_positions = config.get("max_open_positions", 10)
            if not isinstance(max_open_positions, int) or max_open_positions < 1 or max_open_positions > 100:
                return False, "最大开仓数量必须是1到100之间的整数"

            # 验证保证金率
            margin_ratio = config.get("margin_ratio", 0.8)
            if not isinstance(margin_ratio, (int, float)) or margin_ratio < 0.1 or margin_ratio > 1.0:
                return False, "保证金率必须是0.1到1.0之间的数值"

            # 验证最大合仓比例
            max_position_ratio = config.get("max_position_ratio", 0.1)
            if not isinstance(max_position_ratio, (int, float)) or max_position_ratio < 0.01 or max_position_ratio > 1.0:
                return False, "最大合仓比例必须是0.01到1.0之间的数值"

            # 验证日盈亏限制
            daily_pnl_limit = config.get("daily_pnl_limit", 0.2)
            if not isinstance(daily_pnl_limit, (int, float)) or daily_pnl_limit < 0.01 or daily_pnl_limit > 1.0:
                return False, "日盈亏限制必须是0.01到1.0之间的数值"

            # 验证最大开仓数量
            max_open_amount = config.get("max_open_amount", 4)
            if not isinstance(max_open_amount, int) or max_open_amount < 1 or max_open_amount > 20:
                return False, "最大开仓数量必须是1到20之间的整数"

            return True, "资金风险管理参数验证通过"

        except Exception as e:
            return False, f"资金风险管理参数验证异常: {str(e)}"

    def validate_bollinger_strategy_params(self, config: dict) -> tuple[bool, str]:
        """验证布林带策略参数"""
        try:
            # 验证布林带周期
            bb_period = config.get("bb_period", 20)
            if not isinstance(bb_period, int) or bb_period < 5 or bb_period > 100:
                return False, "布林带周期必须是5到100之间的整数"

            # 验证标准差倍数
            bb_std_dev = config.get("bb_std_dev", 2.0)
            if not isinstance(bb_std_dev, (int, float)) or bb_std_dev < 0.5 or bb_std_dev > 5.0:
                return False, "标准差倍数必须是0.5到5.0之间的数值"

            # 验证触发距离
            trigger_points = config.get("bb_trigger_distance_points", 50)
            if not isinstance(trigger_points, (int, float)) or trigger_points < 1 or trigger_points > 1000:
                return False, "触发距离点数必须是1到1000之间的数值"

            trigger_percent = config.get("bb_trigger_distance_percent", 1.0)
            if not isinstance(trigger_percent, (int, float)) or trigger_percent < 0.1 or trigger_percent > 10.0:
                return False, "触发距离百分比必须是0.1到10.0之间的数值"

            # 验证最大加仓次数
            max_add_count = config.get("bb_max_add_count", 3)
            if not isinstance(max_add_count, int) or max_add_count < 1 or max_add_count > 10:
                return False, "最大加仓次数必须是1到10之间的整数"

            # 验证加仓间距
            add_intervals = config.get("bb_add_intervals", [5.0, 8.0, 12.0, 20.0])
            if not isinstance(add_intervals, list) or len(add_intervals) == 0:
                return False, "加仓间距必须是非空列表"

            for interval in add_intervals:
                if not isinstance(interval, (int, float)) or interval < 1.0 or interval > 50.0:
                    return False, "加仓间距必须是1.0到50.0之间的数值"

            # 验证最大总亏损百分比
            max_loss = config.get("bb_max_total_loss_percent", 15.0)
            if not isinstance(max_loss, (int, float)) or max_loss < 5.0 or max_loss > 50.0:
                return False, "最大总亏损百分比必须是5.0到50.0之间的数值"

            # 验证最大投入资金比例
            max_investment = config.get("bb_max_investment_ratio", 0.2)
            if not isinstance(max_investment, (int, float)) or max_investment < 0.05 or max_investment > 1.0:
                return False, "最大投入资金比例必须是0.05到1.0之间的数值"

            # 验证止盈止损百分比
            take_profit = config.get("bb_take_profit_percent", 2.0)
            if not isinstance(take_profit, (int, float)) or take_profit < 0.5 or take_profit > 20.0:
                return False, "止盈百分比必须是0.5到20.0之间的数值"

            stop_loss = config.get("bb_stop_loss_percent", 5.0)
            if not isinstance(stop_loss, (int, float)) or stop_loss < 1.0 or stop_loss > 30.0:
                return False, "止损百分比必须是1.0到30.0之间的数值"

            # 验证信号确认时间周期
            signal_timeframe = config.get("bb_signal_timeframe", "1h")
            if not isinstance(signal_timeframe, str) or not self.validate_timeframe(signal_timeframe):
                valid_timeframes = self.get_timeframe_list()
                return False, f"无效的信号确认时间周期: {signal_timeframe}，有效周期: {valid_timeframes}"

            # 验证紧急加仓距离阈值
            emergency_ratio = config.get("bb_emergency_distance_ratio", 0.8)
            if not isinstance(emergency_ratio, (int, float)) or emergency_ratio < 0.1 or emergency_ratio > 1.0:
                return False, "紧急加仓距离阈值必须是0.1到1.0之间的数值"

            return True, "布林带策略参数验证通过"

        except Exception as e:
            return False, f"布林带策略参数验证失败: {str(e)}"

    def convert_symbol_format(self, symbol: str, exchange: str) -> str:
        """
        根据交易所转换交易对格式

        Args:
            symbol: 原始交易对符号
            exchange: 交易所名称 (okx/gateio)

        Returns:
            转换后的交易对格式
        """
        try:
            # 标准化输入
            symbol = symbol.upper().strip()
            exchange = exchange.lower().strip()

            # 定义交易对映射表
            symbol_mapping = {
                'BTC/USDT': {
                    'okx': 'BTC-USDT-SWAP',
                    'gateio': 'BTC_USDT'
                },
                'ETH/USDT': {
                    'okx': 'ETH-USDT-SWAP',
                    'gateio': 'ETH_USDT'
                },
                'SOL/USDT': {
                    'okx': 'SOL-USDT-SWAP',
                    'gateio': 'SOL_USDT'
                },
                'ADA/USDT': {
                    'okx': 'ADA-USDT-SWAP',
                    'gateio': 'ADA_USDT'
                },
                'DOT/USDT': {
                    'okx': 'DOT-USDT-SWAP',
                    'gateio': 'DOT_USDT'
                }
            }

            # 处理已经是合约格式的交易对
            if '-SWAP' in symbol or '_' in symbol:
                # 如果已经是合约格式，根据目标交易所转换
                if '-SWAP' in symbol:
                    # OKX格式转换
                    base_symbol = symbol.replace('-SWAP', '').replace('-', '/')
                elif '_' in symbol and exchange == 'okx':
                    # Gate.io格式转OKX
                    base_symbol = symbol.replace('_', '/')
                else:
                    # 已经是目标格式
                    return symbol
            else:
                base_symbol = symbol

            # 查找映射
            if base_symbol in symbol_mapping:
                if exchange == 'okx':
                    return symbol_mapping[base_symbol]['okx']
                elif exchange in ['gateio', 'gate']:
                    return symbol_mapping[base_symbol]['gateio']

            # 如果没有找到映射，尝试自动转换
            if exchange == 'okx':
                if '/' in base_symbol:
                    return base_symbol.replace('/', '-') + '-SWAP'
                else:
                    return base_symbol
            elif exchange in ['gateio', 'gate']:
                if '/' in base_symbol:
                    return base_symbol.replace('/', '_')
                else:
                    return base_symbol

            return symbol

        except Exception as e:
            logging.error(f"交易对格式转换失败: {e}")
            return symbol

    def get_supported_symbols(self, exchange: str = None) -> dict:
        """
        获取支持的交易对列表

        Args:
            exchange: 交易所名称，如果为None则返回所有交易所的格式

        Returns:
            支持的交易对字典
        """
        symbols = {
            'BTC/USDT': {
                'name': 'Bitcoin',
                'okx': 'BTC-USDT-SWAP',
                'gateio': 'BTC_USDT',
                'popular': True
            },
            'ETH/USDT': {
                'name': 'Ethereum',
                'okx': 'ETH-USDT-SWAP',
                'gateio': 'ETH_USDT',
                'popular': True
            },
            'SOL/USDT': {
                'name': 'Solana',
                'okx': 'SOL-USDT-SWAP',
                'gateio': 'SOL_USDT',
                'popular': True
            },
            'ADA/USDT': {
                'name': 'Cardano',
                'okx': 'ADA-USDT-SWAP',
                'gateio': 'ADA_USDT',
                'popular': False
            },
            'DOT/USDT': {
                'name': 'Polkadot',
                'okx': 'DOT-USDT-SWAP',
                'gateio': 'DOT_USDT',
                'popular': False
            },
            'MATIC/USDT': {
                'name': 'Polygon',
                'okx': 'MATIC-USDT-SWAP',
                'gateio': 'MATIC_USDT',
                'popular': False
            },
            'AVAX/USDT': {
                'name': 'Avalanche',
                'okx': 'AVAX-USDT-SWAP',
                'gateio': 'AVAX_USDT',
                'popular': False
            }
        }

        if exchange:
            exchange = exchange.lower()
            if exchange == 'okx':
                return {k: v['okx'] for k, v in symbols.items()}
            elif exchange in ['gateio', 'gate']:
                return {k: v['gateio'] for k, v in symbols.items()}

        return symbols

    def get_timeframe_list(self) -> list:
        """获取支持的时间周期列表"""
        return ["15m", "30m", "1h", "2h", "4h", "1d"]

    def get_timeframe_display_names(self) -> dict:
        """获取时间周期的显示名称映射"""
        return {
            "15m": "15分钟",
            "30m": "30分钟",
            "1h": "1小时",
            "2h": "2小时",
            "4h": "4小时",
            "1d": "1日"
        }

    def get_higher_timeframe(self, base_timeframe: str) -> str:
        """
        根据基础时间周期获取更高级别的时间周期

        Args:
            base_timeframe: 基础时间周期

        Returns:
            str: 推荐的更高时间周期
        """
        timeframe_hierarchy = ["15m", "30m", "1h", "2h", "4h", "1d"]

        try:
            if base_timeframe not in timeframe_hierarchy:
                return "1h"  # 默认返回1小时

            current_index = timeframe_hierarchy.index(base_timeframe)

            # 返回比当前高一级的时间周期
            if current_index < len(timeframe_hierarchy) - 1:
                return timeframe_hierarchy[current_index + 1]
            else:
                # 如果已经是最高级，返回当前级别
                return base_timeframe

        except Exception as e:
            logging.error(f"获取更高时间周期失败: {e}")
            return "1h"

    def is_valid_timeframe(self, timeframe: str) -> bool:
        """
        验证时间周期格式（简单版本）

        Args:
            timeframe: 时间周期字符串

        Returns:
            bool: 是否有效
        """
        valid_timeframes = self.get_timeframe_list()
        return timeframe in valid_timeframes

    def sync_bb_signal_timeframe(self, base_timeframe: str) -> str:
        """
        同步布林带信号确认时间周期

        Args:
            base_timeframe: 基础时间周期

        Returns:
            str: 推荐的信号确认时间周期
        """
        try:
            # 获取推荐的更高时间周期
            recommended_timeframe = self.get_higher_timeframe(base_timeframe)

            logging.info(f"基础时间周期: {base_timeframe}, 推荐信号确认时间周期: {recommended_timeframe}")

            return recommended_timeframe

        except Exception as e:
            logging.error(f"同步布林带信号时间周期失败: {e}")
            return "1h"
    
    def export_config(self, filepath: str, config: Dict[str, Any]) -> bool:
        """导出配置到指定文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, filepath: str) -> Optional[Dict[str, Any]]:
        """从指定文件导入配置"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证导入的配置
            is_valid, message = self.validate_config(config)
            if not is_valid:
                logger.error(f"导入的配置无效: {message}")
                return None
            
            return config
            
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return None
    
    def reset_to_default(self) -> Dict[str, Any]:
        """重置为默认配置"""
        return self.default_config.copy()

    def migrate_old_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """迁移旧版本配置到新格式"""
        migrated_config = config.copy()

        # 处理旧版本的单选加仓类型
        if "add_position_type" in migrated_config and "add_position_types" not in migrated_config:
            old_type = migrated_config.pop("add_position_type")
            migrated_config["add_position_types"] = [old_type] if old_type else ["half"]

        # 确保加仓类型是列表格式
        if "add_position_types" in migrated_config and not isinstance(migrated_config["add_position_types"], list):
            migrated_config["add_position_types"] = [migrated_config["add_position_types"]]

        # 添加缺失的MACD参数
        macd_defaults = {
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            "min_signal_strength": 0.3,
            "macd_enabled": True,
            "multi_timeframe_confirmation": []
        }

        for key, default_value in macd_defaults.items():
            if key not in migrated_config:
                migrated_config[key] = default_value

        # 添加缺失的资金风险管理参数
        risk_defaults = {
            "max_open_positions": 10,
            "margin_ratio": 0.8,
            "max_position_ratio": 0.1,
            "daily_pnl_limit": 0.2,
            "max_open_amount": 4,
            "risk_control_enabled": True,
            "emergency_stop": False
        }

        for key, default_value in risk_defaults.items():
            if key not in migrated_config:
                migrated_config[key] = default_value

        return migrated_config
    
    def backup_config(self) -> bool:
        """备份当前配置"""
        try:
            if os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup"
                with open(self.config_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                logger.info(f"配置已备份到 {backup_file}")
                return True
            return False
        except Exception as e:
            logger.error(f"备份配置失败: {e}")
            return False
