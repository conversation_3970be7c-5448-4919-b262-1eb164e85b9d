"""
布林带逆势加仓策略实现
实现马丁格尔策略变种的逆势加仓机制
"""
import asyncio
import logging
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，创建空装饰器
    def monitor_task(task_type):
        def decorator(func):
            return func
        return decorator

    class TaskType:
        OPENING_CHECK = "opening_check"
        PRICE_FETCH = "price_fetch"
        ORDER_EXECUTION = "order_execution"
        POSITION_CHECK = "position_check"

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class BollingerBandSignal(Enum):
    """布林带信号"""
    THREE_LINES_DOWN = "three_lines_down"    # 三线向下
    THREE_LINES_UP = "three_lines_up"        # 三线向上
    PRICE_TOUCH_UPPER = "price_touch_upper"  # 价格触及上轨
    PRICE_TOUCH_LOWER = "price_touch_lower"  # 价格触及下轨
    SQUEEZE = "squeeze"                      # 布林带收缩
    EXPANSION = "expansion"                  # 布林带扩张

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class BollingerBandData:
    """布林带数据（包含可靠性评估）"""
    upper_band: float
    middle_band: float
    lower_band: float
    bandwidth: float
    percent_b: float
    signal: BollingerBandSignal
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 20  # 需要的数据点数
    calculation_period: int = 20  # 实际计算周期

class BollingerBandConfig:
    """布林带策略配置"""
    
    def __init__(self):
        # 布林带参数
        self.bb_period = 20              # 布林带周期
        self.bb_std_dev = 2.0           # 标准差倍数
        
        # 加仓触发参数
        self.trigger_distance_points = 50    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.0  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 3              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [5.0, 8.0, 12.0, 20.0]  # 加仓间距百分比
        
        # 风险控制 (基于ETH案例优化)
        self.max_total_loss_percent = 12.0   # 最大总亏损百分比 (更保守)
        self.max_investment_ratio = 0.15     # 最大投入资金比例 (15%)
        self.enable_emergency_stop = True    # 启用紧急停止

        # 布林带信号过滤
        self.require_bb_signal = True        # 是否需要布林带信号确认
        self.signal_timeframe = "1h"         # 信号确认时间周期
        self.require_signal_for_opening = True   # 开仓时需要信号确认
        self.require_signal_for_adding = False  # 加仓时不需要信号确认
        self.enable_emergency_add = True         # 启用紧急加仓
        self.emergency_distance_ratio = 0.8     # 紧急加仓距离阈值
        self.allowed_bb_signals = [          # 允许的布林带信号
            BollingerBandSignal.THREE_LINES_DOWN,
            BollingerBandSignal.PRICE_TOUCH_LOWER
        ]

        # 止盈止损 (基于ETH案例优化)
        self.take_profit_percent = 1.5       # 止盈百分比 (更现实的目标)
        self.stop_loss_percent = 5.6         # 止损百分比 (与ETH案例一致)
        self.trailing_stop = False           # 是否启用移动止损

        # 交易参数
        self.initial_margin = 500.0          # 初始保证金 (USDT)
        self.leverage = 50                   # 杠杆倍数

class BollingerBandStrategy:
    """布林带逆势加仓策略"""

    def __init__(self, config: BollingerBandConfig):
        self.config = config
        self.add_records: List[AddPositionRecord] = []
        self.current_position = None
        self.is_active = False
        self.is_running = False
        self.emergency_stopped = False

        # 从配置中复制关键属性
        self.enable_emergency_add = config.enable_emergency_add
        self.emergency_distance_ratio = config.emergency_distance_ratio
        self.require_signal_for_opening = config.require_signal_for_opening
        self.require_signal_for_adding = config.require_signal_for_adding
        self.stop_loss_percent = config.stop_loss_percent

        # 布林带计算缓存
        self.price_history: List[float] = []
        self.bb_history: List[BollingerBandData] = []

        # 并发安全锁
        self._price_history_lock = asyncio.Lock()  # 保护价格历史访问
        self._add_records_lock = asyncio.Lock()    # 保护加仓记录访问
        self._bb_history_lock = asyncio.Lock()     # 保护布林带历史访问

        # 自动开仓相关
        self.exchange = None
        self.symbol = None
        self.initial_margin = config.initial_margin  # 从配置获取
        self.leverage = config.leverage              # 从配置获取
        self.account_info = None
        self.auto_opening_enabled = False
        
    async def calculate_bollinger_bands(self, prices: List[float], min_periods: int = None, bb_history: List[BollingerBandData] = None) -> Optional[BollingerBandData]:
        """
        计算布林带指标（支持部分数据计算）

        Args:
            prices: 价格列表
            min_periods: 最小计算周期，默认为bb_period的一半

        Returns:
            Optional[BollingerBandData]: 布林带数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(10, self.config.bb_period // 2)  # 至少10个，或者bb_period的一半

        data_count = len(prices)
        required_count = self.config.bb_period

        # 数据完全不足
        if data_count < min_periods:
            logger.warning(f"❌ 价格数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分和实际使用的周期
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ 布林带数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ 布林带数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 转换为pandas Series，使用实际可用的数据
            price_series = pd.Series(prices[-calculation_period:])

            # 计算移动平均线(中轨)
            middle_band = price_series.mean()  # 使用简单平均，更适合少量数据

            # 计算标准差
            std_dev = price_series.std()

            # 根据可靠性调整标准差倍数（数据不足时收窄布林带）
            reliability_factor = 0.7 + 0.3 * reliability  # 0.7-1.0之间
            adjusted_std_multiplier = self.config.bb_std_dev * reliability_factor

            # 计算上轨和下轨
            upper_band = middle_band + (adjusted_std_multiplier * std_dev)
            lower_band = middle_band - (adjusted_std_multiplier * std_dev)
            
            # 计算带宽
            bandwidth = (upper_band - lower_band) / middle_band * 100
            
            # 计算%B指标（防止除零错误）
            current_price = prices[-1]
            band_range = upper_band - lower_band
            if band_range > 0:
                percent_b = (current_price - lower_band) / band_range * 100
            else:
                # 标准差为0时，价格在中轨，%B设为50%
                percent_b = 50.0
            
            # 判断布林带信号（考虑可靠性）
            signal = self._identify_bb_signal(prices, upper_band, middle_band, lower_band, reliability)

            logger.info(f"📊 布林带计算完成: 上轨={upper_band:.4f}, 中轨={middle_band:.4f}, 下轨={lower_band:.4f}")
            logger.info(f"📊 %B指标={percent_b:.1f}%, 带宽={bandwidth:.2f}%, 可靠性={reliability:.2f}")
            logger.info(f"📊 数据统计: 使用{calculation_period}个数据点 (需要{required_count}个)")
            logger.info(f"📊 计算参数: 标准差倍数={adjusted_std_multiplier:.3f} (原始={self.config.bb_std_dev}, 调整因子={reliability_factor:.3f})")
            logger.info(f"📊 价格位置: 当前价格={current_price:.4f}, 距上轨={((current_price/upper_band-1)*100):+.2f}%, 距下轨={((current_price/lower_band-1)*100):+.2f}%")

            return BollingerBandData(
                upper_band=upper_band,
                middle_band=middle_band,
                lower_band=lower_band,
                bandwidth=bandwidth,
                percent_b=percent_b,
                signal=signal,
                reliability=reliability,
                data_count=data_count,
                required_count=required_count,
                calculation_period=calculation_period
            )
            
        except Exception as e:
            logger.error(f"计算布林带指标失败: {e}")
            return None
    
    def _identify_bb_signal(self, prices: List[float], upper: float,
                           middle: float, lower: float, reliability: float = 1.0) -> BollingerBandSignal:
        """识别布林带信号"""
        current_price = prices[-1]

        # 根据可靠性调整触发阈值（平衡精度和敏感性）
        if reliability >= 0.9:
            # 高可靠性：要求接近或触及布林带
            upper_threshold = 1.0005  # 上轨的100.05%
            lower_threshold = 0.9995  # 下轨的99.95%
        else:
            # 低可靠性：稍微放宽条件
            upper_threshold = 1.001   # 上轨的100.1%
            lower_threshold = 0.999   # 下轨的99.9%

        # 检查价格是否真正触及轨道
        if current_price >= upper * upper_threshold:  # 真正触及或突破上轨
            return BollingerBandSignal.PRICE_TOUCH_UPPER
        elif current_price <= lower * lower_threshold:  # 真正触及或突破下轨
            return BollingerBandSignal.PRICE_TOUCH_LOWER
        
        # 检查三线方向(需要历史数据)
        if bb_history is None:
            bb_history = []

        bb_history_length = len(bb_history)
        if bb_history_length >= 3:
            recent_bb = bb_history[-3:]
            
            # 检查三线是否都向下
            upper_down = all(recent_bb[i].upper_band > recent_bb[i+1].upper_band 
                           for i in range(len(recent_bb)-1))
            middle_down = all(recent_bb[i].middle_band > recent_bb[i+1].middle_band 
                            for i in range(len(recent_bb)-1))
            lower_down = all(recent_bb[i].lower_band > recent_bb[i+1].lower_band 
                           for i in range(len(recent_bb)-1))
            
            if upper_down and middle_down and lower_down:
                return BollingerBandSignal.THREE_LINES_DOWN
            
            # 检查三线是否都向上
            upper_up = all(recent_bb[i].upper_band < recent_bb[i+1].upper_band 
                         for i in range(len(recent_bb)-1))
            middle_up = all(recent_bb[i].middle_band < recent_bb[i+1].middle_band 
                          for i in range(len(recent_bb)-1))
            lower_up = all(recent_bb[i].lower_band < recent_bb[i+1].lower_band 
                         for i in range(len(recent_bb)-1))
            
            if upper_up and middle_up and lower_up:
                return BollingerBandSignal.THREE_LINES_UP
        
        # 检查布林带收缩/扩张
        if bb_history_length >= 2:
            current_bandwidth = (upper - lower) / middle * 100
            prev_bandwidth = bb_history[-1].bandwidth
            
            if current_bandwidth < prev_bandwidth * 0.9:
                return BollingerBandSignal.SQUEEZE
            elif current_bandwidth > prev_bandwidth * 1.1:
                return BollingerBandSignal.EXPANSION
        
        # 默认返回触及下轨(适合逆势做多)
        return BollingerBandSignal.PRICE_TOUCH_LOWER
    
    def get_real_time_stop_loss_price(self, exchange, symbol: str) -> Dict:
        """
        获取实时止损价格

        Args:
            exchange: 交易所实例
            symbol: 交易对符号

        Returns:
            Dict: 止损价格信息
        """
        try:
            if not exchange:
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': None,
                    'error': '交易所实例为空'
                }

            # 调用交易所API获取实时止损价格（异步）
            # 注意：这个方法现在需要在异步上下文中调用
            try:
                loop = asyncio.get_running_loop()
                # 如果在异步上下文中，直接await
                if asyncio.iscoroutinefunction(exchange.get_position_stop_loss_price):
                    # 这里需要异步调用，但当前方法是同步的
                    # 这个方法需要被重构为异步
                    logger.warning("get_real_time_stop_loss_price需要在异步上下文中调用")
                    return {
                        'success': False,
                        'stop_loss_price': 0.0,
                        'timestamp': None,
                        'error': '需要异步调用'
                    }
                else:
                    result = exchange.get_position_stop_loss_price(symbol)
            except RuntimeError:
                # 没有运行中的事件循环
                logger.error("没有运行中的事件循环，无法调用异步方法")
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': None,
                    'error': '没有运行中的事件循环'
                }

            if result['success']:
                logger.info(f"成功获取实时止损价格: {result['stop_loss_price']:.4f}")
            else:
                logger.warning(f"获取实时止损价格失败: {result['error']}")

            return result

        except Exception as e:
            logger.error(f"获取实时止损价格异常: {e}")
            return {
                'success': False,
                'stop_loss_price': 0.0,
                'timestamp': None,
                'error': f'获取实时止损价格异常: {str(e)}'
            }

    async def get_real_time_stop_loss_price_async(self, exchange, symbol: str) -> Dict:
        """
        异步获取实时止损价格

        Args:
            exchange: 交易所实例
            symbol: 交易对符号

        Returns:
            Dict: 止损价格信息
        """
        try:
            if not exchange:
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': None,
                    'error': '交易所实例为空'
                }

            # 调用交易所API获取实时止损价格（异步）
            result = await exchange.get_position_stop_loss_price(symbol)

            if result['success']:
                logger.info(f"成功获取实时止损价格: {result['stop_loss_price']:.4f}")
            else:
                logger.warning(f"获取实时止损价格失败: {result['error']}")

            return result

        except Exception as e:
            logger.error(f"异步获取实时止损价格异常: {e}")
            return {
                'success': False,
                'stop_loss_price': 0.0,
                'timestamp': None,
                'error': f'异步获取实时止损价格异常: {str(e)}'
            }

    async def should_add_position(self, current_price: float, exchange=None, symbol: str = None,
                          account_info: Dict = None, position_info: Dict = None) -> Tuple[bool, str]:
        """
        判断是否应该加仓

        Args:
            current_price: 当前价格
            exchange: 交易所实例
            symbol: 交易对符号
            account_info: 账户信息
            position_info: 持仓信息

        Returns:
            Tuple[bool, str]: (是否应该加仓, 原因说明)
        """
        if self.emergency_stopped:
            return False, "策略已紧急停止"

        add_count = await self.get_add_records_count()
        if add_count >= self.config.max_add_count:
            return False, f"已达到最大加仓次数({self.config.max_add_count})"

        # 获取实时止损价格
        real_time_stop_loss = None
        if exchange and symbol:
            stop_loss_result = self.get_real_time_stop_loss_price(exchange, symbol)
            if stop_loss_result['success']:
                real_time_stop_loss = stop_loss_result['stop_loss_price']
                logger.info(f"使用实时止损价格: {real_time_stop_loss:.4f}")
            else:
                logger.warning(f"获取实时止损价格失败，将使用降级方案: {stop_loss_result['error']}")

        # 如果无法获取实时止损价格，使用降级方案
        if real_time_stop_loss is None:
            if position_info and hasattr(position_info, 'entry_price') and position_info.entry_price > 0:
                # 根据持仓信息计算止损价格
                entry_price = position_info.entry_price
                position_side = position_info.side.value if hasattr(position_info.side, 'value') else str(position_info.side)

                if position_side == 'long':
                    real_time_stop_loss = entry_price * (1 - self.stop_loss_percent / 100.0)
                else:
                    real_time_stop_loss = entry_price * (1 + self.stop_loss_percent / 100.0)

                logger.info(f"使用计算的止损价格: {real_time_stop_loss:.4f}")
            else:
                return False, "无法获取止损价格信息，无法判断加仓条件"

        # 检测是否为紧急加仓情况
        is_emergency = self.is_emergency_add_situation(current_price, position_info or {})
        add_count = await self.get_add_records_count()
        is_opening = add_count == 0  # 第一次为开仓

        # 检查是否接近止损价（使用实时止损价格）
        if self.config.use_points_trigger:
            distance = abs(current_price - real_time_stop_loss)
            logger.info(f"📏 [加仓检测] 距离止损价: {distance:.2f}点 (触发阈值: {self.config.trigger_distance_points}点)")
            if distance > self.config.trigger_distance_points:
                return False, f"距离实时止损价还有{distance:.2f}点，未达到触发条件({self.config.trigger_distance_points}点)"
            else:
                logger.info(f"🎯 [加仓检测] 触发加仓条件: 距离止损价{distance:.2f}点 <= {self.config.trigger_distance_points}点")
        else:
            distance_percent = abs(current_price - real_time_stop_loss) / current_price * 100
            logger.info(f"📏 [加仓检测] 距离止损价: {distance_percent:.2f}% (触发阈值: {self.config.trigger_distance_percent}%)")
            if distance_percent > self.config.trigger_distance_percent:
                return False, f"距离实时止损价还有{distance_percent:.2f}%，未达到触发条件({self.config.trigger_distance_percent}%)"
            else:
                logger.info(f"🎯 [加仓检测] 触发加仓条件: 距离止损价{distance_percent:.2f}% <= {self.config.trigger_distance_percent}%")

        # 根据开仓/加仓类型和紧急情况判断是否需要布林带信号确认
        need_signal_confirmation = self.should_require_signal_confirmation(is_opening, is_emergency)

        if need_signal_confirmation:
            latest_bb_data = await self.get_latest_bb_data()
            if latest_bb_data and latest_bb_data.signal not in self.config.allowed_bb_signals:
                signal_type = "开仓" if is_opening else "加仓"
                return False, f"当前布林带信号({latest_bb_data.signal.value})不符合{signal_type}条件"

        # 检查总投入资金比例
        total_balance = account_info.get('total_balance', 0)
        current_investment = await self.get_total_margin()
        if current_investment / total_balance > self.config.max_investment_ratio:
            return False, f"总投入资金比例已超过限制({self.config.max_investment_ratio*100}%)"

        # 记录加仓原因（基于实时止损价格）
        if is_emergency:
            reason = f"紧急加仓：价格{current_price:.4f}接近实时止损线{real_time_stop_loss:.4f}，立即执行保护性加仓"
            logger.warning(f"触发紧急加仓: {reason}")
        elif is_opening:
            reason = f"开仓：满足布林带信号确认条件，实时止损价格{real_time_stop_loss:.4f}" if need_signal_confirmation else f"开仓：满足价格触发条件，实时止损价格{real_time_stop_loss:.4f}"
        else:
            reason = f"常规加仓：满足价格距离触发条件，基于实时止损价格{real_time_stop_loss:.4f}"

        return True, reason

    def start_auto_opening(self, exchange, symbol: str, initial_margin: float,
                          leverage: int, account_info: Dict) -> Tuple[bool, str]:
        """
        启动自动开仓功能

        Args:
            exchange: 交易所实例
            symbol: 交易对符号
            initial_margin: 初始保证金
            leverage: 杠杆倍数
            account_info: 账户信息

        Returns:
            Tuple[bool, str]: (是否成功启动, 结果消息)
        """
        try:
            if self.is_running:
                return False, "布林带策略已在运行中"

            if self.emergency_stopped:
                return False, "策略已紧急停止，请重置后再启动"

            # 验证参数
            if not exchange:
                return False, "交易所实例为空"

            if not symbol:
                return False, "交易对符号为空"

            if initial_margin <= 0:
                return False, "初始保证金必须大于0"

            if leverage <= 0:
                return False, "杠杆倍数必须大于0"

            # 验证账户余额
            if not self._validate_account_balance(account_info, initial_margin):
                return False, "账户余额不足或超过风险限制"

            # 设置运行参数
            self.exchange = exchange
            self.symbol = symbol
            self.initial_margin = initial_margin
            self.leverage = leverage
            self.account_info = account_info
            self.auto_opening_enabled = True
            self.is_running = True
            self.is_active = True

            logger.info(f"布林带自动开仓策略已启动: {symbol}, 保证金: {initial_margin}, 杠杆: {leverage}x")
            return True, f"布林带策略启动成功，监控交易对: {symbol}"

        except Exception as e:
            logger.error(f"启动自动开仓失败: {e}")
            return False, f"启动失败: {str(e)}"

    def stop_auto_opening(self) -> Tuple[bool, str]:
        """
        停止自动开仓功能

        Returns:
            Tuple[bool, str]: (是否成功停止, 结果消息)
        """
        try:
            if not self.is_running:
                return False, "布林带策略未在运行"

            self.is_running = False
            self.is_active = False
            self.auto_opening_enabled = False

            logger.info("布林带自动开仓策略已停止")
            return True, "布林带策略已停止"

        except Exception as e:
            logger.error(f"停止自动开仓失败: {e}")
            return False, f"停止失败: {str(e)}"

    @monitor_task(TaskType.OPENING_CHECK)
    async def analyze_opening_opportunity(self, current_price: float, exchange=None, symbol: str = None, force_check: bool = False) -> Tuple[bool, str, str]:
        """
        分析开仓机会（异步版本，包含持仓检查）

        Args:
            current_price: 当前价格
            exchange: 交易所实例
            symbol: 交易对符号
            force_check: 强制检查，忽略auto_opening_enabled状态

        Returns:
            Tuple[bool, str, str]: (是否有开仓机会, 开仓方向, 分析原因)
        """
        try:
            # 如果不是强制检查，则检查状态
            if not force_check and (not self.auto_opening_enabled or not self.is_running):
                return False, "", "自动开仓未启用"

            # 检查持仓状态（增强容错性）
            logger.info("🔍 检查持仓状态...")
            position_status = await self.check_position_status(exchange, symbol)

            # 如果交易所API失败，但本地有记录，仍然阻止开仓
            if position_status['error'] and position_status['local_records'] > 0:
                reason = f"交易所API失败但本地有持仓记录，为安全起见禁止开仓: {position_status['error']}"
                logger.warning(f"🚫 {reason}")
                return False, "", reason

            # 如果交易所API失败且无本地记录，记录警告但允许继续（风险操作）
            if position_status['error'] and position_status['local_records'] == 0:
                logger.warning(f"⚠️ 交易所API失败，无法验证持仓状态: {position_status['error']}")
                logger.warning("⚠️ 基于本地记录判断无持仓，继续开仓检查（存在风险）")

            if position_status['has_position']:
                reason = f"已有持仓，不执行新开仓 (方向: {position_status['position_side']}, 数量: {position_status['position_size']:.6f})"
                logger.info(f"🚫 {reason}")
                return False, "", reason

            logger.info("✅ 无持仓，可以执行开仓检查")

            # 更新价格历史
            await self.add_price_to_history(current_price)

            # 需要足够的历史数据
            history_length = await self.get_price_history_length()
            if history_length < self.config.bb_period:
                return False, "", f"价格历史数据不足，需要{self.config.bb_period}个数据点"

            # 计算布林带指标
            price_history_copy = await self.get_price_history_copy()
            bb_history_copy = await self.get_bb_history_copy()
            bb_data = await self.calculate_bollinger_bands(price_history_copy, bb_history=bb_history_copy)
            if not bb_data:
                return False, "", "布林带计算失败"

            # 更新布林带历史
            await self.add_bb_history(bb_data, max_length=10)

            # 分析开仓方向
            opening_direction = self._determine_opening_direction(current_price, bb_data)
            if not opening_direction:
                return False, "", "当前价格位置不适合开仓"

            # 验证开仓方向（双重检查）
            direction_valid, direction_reason = self.validate_opening_direction(opening_direction, position_status)
            if not direction_valid:
                logger.warning(f"🚫 开仓方向验证失败: {direction_reason}")
                return False, "", direction_reason

            # 检查信号确认
            if self.require_signal_for_opening:
                if not self._validate_opening_signal(bb_data):
                    return False, "", "布林带信号确认未通过"

            # 分析原因
            reason = self._generate_opening_reason(current_price, bb_data, opening_direction)

            logger.info(f"✅ 发现开仓机会: {opening_direction}, 原因: {reason}")
            return True, opening_direction, reason

        except Exception as e:
            logger.error(f"分析开仓机会异常: {e}")
            return False, "", f"分析异常: {str(e)}"

    async def collect_price_data(self, current_price: float) -> bool:
        """
        收集价格数据用于监控显示（独立于开仓逻辑）

        Args:
            current_price: 当前价格

        Returns:
            bool: 是否成功收集数据
        """
        try:
            if current_price <= 0:
                logger.warning(f"无效的价格数据: {current_price}")
                return False

            # 更新价格历史
            await self.add_price_to_history(current_price)

            # 记录数据收集进度
            current_count = await self.get_price_history_length()
            required_count = self.config.bb_period
            progress_percent = (current_count / required_count * 100) if required_count > 0 else 0

            if current_count % 5 == 0:  # 每5个数据点记录一次
                logger.info(f"📊 价格数据收集: {current_count}/{required_count} ({progress_percent:.1f}%) | 最新: {current_price:.4f}")

            # 如果数据充足，计算布林带用于显示
            if current_count >= required_count:
                price_history_copy = await self.get_price_history_copy()
                bb_history_copy = await self.get_bb_history_copy()
                bb_data = await self.calculate_bollinger_bands(price_history_copy, bb_history=bb_history_copy)
                if bb_data:
                    await self.add_bb_history(bb_data, max_length=100)

                    logger.debug(f"📈 布林带更新: 上轨={bb_data.upper_band:.4f}, 中轨={bb_data.middle_band:.4f}, 下轨={bb_data.lower_band:.4f}")

            return True

        except Exception as e:
            logger.error(f"收集价格数据失败: {e}")
            return False

    async def analyze_opening_opportunity_sync(self, current_price: float) -> Tuple[bool, str, str]:
        """
        分析开仓机会（同步版本，仅检查本地记录）

        Args:
            current_price: 当前价格

        Returns:
            Tuple[bool, str, str]: (是否有开仓机会, 开仓方向, 分析原因)
        """
        try:
            if not self.auto_opening_enabled or not self.is_running:
                return False, "", "自动开仓未启用"

            # 检查本地记录（现在是异步方法）
            add_count = await self.get_add_records_count()
            if add_count > 0:
                return False, "", "已有持仓，不执行新开仓"

            # 更新价格历史（异步版本）
            await self.add_price_to_history(current_price)

            # 需要足够的历史数据
            history_length = await self.get_price_history_length()
            if history_length < self.config.bb_period:
                return False, "", f"价格历史数据不足，需要{self.config.bb_period}个数据点"

            # 计算布林带指标（异步版本）
            price_history_copy = await self.get_price_history_copy()
            bb_history_copy = await self.get_bb_history_copy()
            bb_data = await self.calculate_bollinger_bands(price_history_copy, bb_history=bb_history_copy)
            if not bb_data:
                return False, "", "布林带计算失败"

            # 更新布林带历史（异步版本）
            await self.add_bb_history(bb_data, max_length=10)

            # 分析开仓方向
            opening_direction = self._determine_opening_direction(current_price, bb_data)
            if not opening_direction:
                return False, "", "当前价格位置不适合开仓"

            # 检查信号确认
            if self.require_signal_for_opening:
                if not self._validate_opening_signal(bb_data):
                    return False, "", "布林带信号确认未通过"

            # 分析原因
            reason = self._generate_opening_reason(current_price, bb_data, opening_direction)

            logger.info(f"✅ 发现开仓机会: {opening_direction}, 原因: {reason}")
            return True, opening_direction, reason

        except Exception as e:
            logger.error(f"分析开仓机会异常: {e}")
            return False, "", f"分析异常: {str(e)}"

    def _determine_opening_direction(self, current_price: float, bb_data: BollingerBandData) -> Optional[str]:
        """
        确定开仓方向

        Args:
            current_price: 当前价格
            bb_data: 布林带数据

        Returns:
            Optional[str]: 开仓方向 ('long' 或 'short')，None表示不开仓
        """
        try:
            # 逆势策略：价格真正触及布林带时开仓

            # 根据可靠性调整开仓条件
            if bb_data.reliability >= 0.9:
                # 高可靠性：严格要求触及布林带
                upper_trigger = bb_data.upper_band * 1.001  # 上轨的100.1%
                lower_trigger = bb_data.lower_band * 0.999  # 下轨的99.9%
            else:
                # 低可靠性：稍微放宽但仍然严格
                upper_trigger = bb_data.upper_band * 1.0005  # 上轨的100.05%
                lower_trigger = bb_data.lower_band * 0.9995  # 下轨的99.95%

            # 做多条件：价格真正触及下轨
            if current_price <= lower_trigger:
                # 检查是否有做多信号
                if bb_data.signal in [BollingerBandSignal.PRICE_TOUCH_LOWER,
                                     BollingerBandSignal.THREE_LINES_DOWN]:
                    logger.info(f"🔵 做多信号确认: 价格{current_price:.4f} <= 下轨触发点{lower_trigger:.4f}")
                    return "long"

            # 做空条件：价格真正触及上轨
            elif current_price >= upper_trigger:
                # 检查是否有做空信号
                if bb_data.signal in [BollingerBandSignal.PRICE_TOUCH_UPPER,
                                     BollingerBandSignal.THREE_LINES_UP]:
                    logger.info(f"🔴 做空信号确认: 价格{current_price:.4f} >= 上轨触发点{upper_trigger:.4f}")
                    return "short"

            # 记录未触发的情况
            logger.debug(f"📊 价格{current_price:.4f}未触发开仓: 上轨触发点{upper_trigger:.4f}, 下轨触发点{lower_trigger:.4f}")
            return None

        except Exception as e:
            logger.error(f"确定开仓方向异常: {e}")
            return None

    def _determine_immediate_opening_direction(self, current_price: float, bb_data: BollingerBandData) -> Optional[str]:
        """
        确定立即开仓方向（立即开仓专用逻辑）

        立即开仓逻辑说明：
        - 目的：无持仓时立即开仓，无需等待价格触及布林带边界
        - 条件：仅基于%B指标判断方向，不等待传统开仓信号
        - 区别：传统策略等待触边，立即开仓根据%B立即执行

        Args:
            current_price: 当前价格
            bb_data: 布林带数据

        Returns:
            Optional[str]: 开仓方向 ('long' 或 'short')，None表示暂不开仓
        """
        try:
            # 计算布林带宽度
            band_width = bb_data.upper_band - bb_data.lower_band

            # 计算%B指标（价格在布林带中的相对位置）
            if band_width > 0:
                percent_b = (current_price - bb_data.lower_band) / band_width
            else:
                logger.warning("⚠️ [立即开仓] 布林带宽度为0，无法计算%B指标")
                return None

            logger.info(f"📊 [立即开仓] %B指标: {percent_b*100:.1f}% (价格相对位置)")
            logger.info(f"📊 [立即开仓] 布林带: 上轨={bb_data.upper_band:.4f}, 中轨={bb_data.middle_band:.4f}, 下轨={bb_data.lower_band:.4f}")
            logger.info(f"📊 [立即开仓] 当前价格: {current_price:.4f}")

            # 立即开仓判断逻辑：
            # 做多条件：%B < 30%（价格接近下轨，预期反弹）
            if percent_b < 0.30:
                logger.info(f"🔵 [立即开仓] 触发做多信号: %B={percent_b*100:.1f}% < 30% (接近下轨)")
                return "long"

            # 做空条件：%B > 70%（价格接近上轨，预期回调）
            elif percent_b > 0.70:
                logger.info(f"🔴 [立即开仓] 触发做空信号: %B={percent_b*100:.1f}% > 70% (接近上轨)")
                return "short"

            # 中性区域：30% ≤ %B ≤ 70%（等待更明确信号）
            else:
                logger.info(f"⚪ [立即开仓] 中性区域: %B={percent_b*100:.1f}% (30%-70%之间)，等待下次检测")
                return None

        except Exception as e:
            logger.error(f"确定立即开仓方向异常: {e}")
            return None

    def _validate_opening_signal(self, bb_data: BollingerBandData) -> bool:
        """
        验证开仓信号

        Args:
            bb_data: 布林带数据

        Returns:
            bool: 信号是否有效
        """
        try:
            # 检查信号是否在允许列表中
            if bb_data.signal in self.config.allowed_bb_signals:
                return True

            # 检查%B指标
            if bb_data.percent_b <= 20:  # 超卖区域
                return True
            elif bb_data.percent_b >= 80:  # 超买区域
                return True

            return False

        except Exception as e:
            logger.error(f"验证开仓信号异常: {e}")
            return False

    async def get_current_bollinger_data(self) -> Optional[Dict]:
        """
        获取当前布林带数据

        Returns:
            Optional[Dict]: 布林带数据字典，包含上轨、中轨、下轨等信息
        """
        try:
            latest_bb_data = await self.get_latest_bb_data()
            if not latest_bb_data:
                logger.warning("布林带历史数据为空")
                return None

            return {
                'upper_band': latest_bb_data.upper_band,
                'middle_band': latest_bb_data.middle_band,
                'lower_band': latest_bb_data.lower_band,
                'percent_b': latest_bb_data.percent_b,
                'bandwidth': latest_bb_data.bandwidth,
                'signal': latest_bb_data.signal.value if latest_bb_data.signal else None,
                'timestamp': latest_bb_data.timestamp
            }

        except Exception as e:
            logger.error(f"获取当前布林带数据异常: {e}")
            return None

    @monitor_task(TaskType.POSITION_CHECK)
    async def check_position_status(self, exchange=None, symbol: str = None) -> Dict:
        """
        检查当前持仓状态

        Args:
            exchange: 交易所实例
            symbol: 交易对符号

        Returns:
            Dict: 持仓状态信息
            {
                'has_position': bool,  # 是否有持仓
                'position_side': str,  # 持仓方向 ('long', 'short', '')
                'position_size': float,  # 持仓数量
                'entry_price': float,  # 入场价格
                'unrealized_pnl': float,  # 未实现盈亏
                'local_records': int,  # 本地记录数量
                'exchange_verified': bool,  # 交易所验证状态
                'conflict_detected': bool,  # 是否检测到冲突
                'error': str  # 错误信息
            }
        """
        try:
            result = {
                'has_position': False,
                'position_side': '',
                'position_size': 0.0,
                'entry_price': 0.0,
                'unrealized_pnl': 0.0,
                'local_records': await self.get_add_records_count(),
                'exchange_verified': False,
                'conflict_detected': False,
                'error': ''
            }

            # 检查本地记录
            add_count = await self.get_add_records_count()
            if add_count > 0:
                result['has_position'] = True
                latest_record = await self.get_latest_add_record()
                if latest_record:
                    result['entry_price'] = latest_record.new_avg_cost
                result['position_size'] = await self.get_total_quantity()

                # 从当前持仓信息获取方向
                if hasattr(self, 'current_position') and self.current_position:
                    if hasattr(self.current_position, 'side'):
                        # PositionInfo对象
                        result['position_side'] = self.current_position.side.value if hasattr(self.current_position.side, 'value') else str(self.current_position.side)
                    elif isinstance(self.current_position, dict):
                        # 字典对象
                        result['position_side'] = self.current_position.get('side', '')
                    else:
                        result['position_side'] = ''

                logger.info(f"本地持仓记录: 数量={result['position_size']:.6f}, 方向={result['position_side']}, 平均成本={result['entry_price']:.4f}")

            # 交易所验证（如果提供了交易所实例）
            if exchange and symbol:
                try:
                    # 获取交易所持仓信息
                    exchange_positions = await exchange.get_positions(symbol)

                    if exchange_positions:
                        for pos in exchange_positions:
                            # PositionInfo对象，使用属性访问
                            if abs(pos.size) > 0:
                                result['exchange_verified'] = True
                                exchange_side = pos.side.value if hasattr(pos.side, 'value') else str(pos.side)
                                exchange_size = abs(pos.size)  # 使用绝对值
                                exchange_entry = pos.entry_price

                                logger.info(f"交易所持仓: 数量={exchange_size:.6f}, 方向={exchange_side}, 入场价={exchange_entry:.4f}")

                                # 检查是否与本地记录冲突
                                if result['has_position']:
                                    if (result['position_side'] != exchange_side or
                                        abs(result['position_size'] - exchange_size) > 0.001):
                                        result['conflict_detected'] = True
                                        logger.warning(f"持仓冲突: 本地({result['position_side']}, {result['position_size']:.6f}) vs 交易所({exchange_side}, {exchange_size:.6f})")
                                else:
                                    # 本地无记录但交易所有持仓
                                    result['has_position'] = True
                                    result['position_side'] = exchange_side
                                    result['position_size'] = exchange_size
                                    result['entry_price'] = exchange_entry
                                    result['unrealized_pnl'] = pos.unrealized_pnl
                                    logger.warning(f"发现未记录的交易所持仓: {exchange_side} {exchange_size:.6f}")
                                break
                    else:
                        result['exchange_verified'] = True
                        if result['has_position']:
                            result['conflict_detected'] = True
                            logger.warning("本地有持仓记录但交易所无持仓")

                except Exception as e:
                    result['error'] = f"交易所持仓查询失败: {e}"
                    logger.error(result['error'])

            return result

        except Exception as e:
            error_msg = f"检查持仓状态异常: {e}"
            logger.error(error_msg)
            return {
                'has_position': False,
                'position_side': '',
                'position_size': 0.0,
                'entry_price': 0.0,
                'unrealized_pnl': 0.0,
                'local_records': 0,
                'exchange_verified': False,
                'conflict_detected': False,
                'error': error_msg
            }

    def validate_opening_direction(self, new_direction: str, position_status: Dict) -> Tuple[bool, str]:
        """
        验证开仓方向是否允许

        Args:
            new_direction: 新的开仓方向 ('long' 或 'short')
            position_status: 持仓状态信息

        Returns:
            Tuple[bool, str]: (是否允许, 拒绝原因)
        """
        try:
            # 检查API失败但有本地记录的情况
            if position_status.get('error') and position_status.get('local_records', 0) > 0:
                reason = f"交易所API失败但有本地持仓记录，为安全起见禁止开仓"
                logger.warning(reason)
                return False, reason

            if not position_status['has_position']:
                return True, "无持仓，允许开仓"

            current_side = position_status['position_side']

            # 检查是否为反向开仓
            if current_side and current_side != new_direction:
                reason = f"禁止反向开仓: 当前持仓方向为{current_side}，尝试开{new_direction}仓"
                logger.warning(reason)
                return False, reason

            # 检查是否为重复开仓
            if current_side == new_direction:
                reason = f"禁止重复开仓: 已存在{current_side}仓位，应使用加仓功能"
                logger.warning(reason)
                return False, reason

            return True, "方向验证通过"

        except Exception as e:
            error_msg = f"验证开仓方向异常: {e}"
            logger.error(error_msg)
            return False, error_msg

    async def collect_initial_price_data(self, exchange=None, symbol: str = None) -> bool:
        """
        快速收集初始价格数据

        Args:
            exchange: 交易所实例
            symbol: 交易对符号

        Returns:
            bool: 是否成功收集到足够数据
        """
        try:
            if not exchange or not symbol:
                logger.warning("缺少交易所实例或交易对符号，无法收集历史数据")
                return False

            required_count = self.config.bb_period
            current_count = len(self.price_history)

            if current_count >= required_count:
                logger.info(f"✅ 已有足够的价格数据: {current_count}/{required_count}")
                return True

            logger.info(f"🔄 开始收集价格历史数据: 当前{current_count}个，需要{required_count}个")

            # 方法1: 尝试从交易所获取历史K线数据
            if hasattr(exchange, 'get_klines'):
                try:
                    # 获取最近的K线数据（1分钟K线）
                    klines = await exchange.get_klines(symbol, '1m', required_count)
                    if klines and len(klines) >= required_count:
                        # 提取收盘价作为价格历史
                        self.price_history = [float(kline['close']) for kline in klines[-required_count:]]
                        logger.info(f"✅ 从K线数据收集到{len(self.price_history)}个价格点")
                        return True
                except Exception as e:
                    logger.warning(f"从K线数据收集失败: {e}")

            # 方法2: 快速连续获取当前价格
            logger.info("🔄 使用快速价格采集方法...")
            needed_count = required_count - current_count

            # 收集所有需要的数据点，但限制最大尝试次数避免无限循环
            max_attempts = min(needed_count * 2, 30)  # 最多尝试30次
            successful_collections = 0

            for i in range(max_attempts):
                try:
                    current_price = await exchange.get_current_price(symbol)
                    if current_price and current_price > 0:
                        await self.add_price_to_history(current_price)
                        successful_collections += 1
                        logger.info(f"📊 采集价格数据 {len(self.price_history)}/{required_count}: {current_price}")

                        # 如果已经收集到足够数据，提前退出
                        if len(self.price_history) >= required_count:
                            logger.info(f"✅ 已收集到足够数据，提前结束快速采集")
                            break

                        # 短暂延迟避免请求过于频繁
                        await asyncio.sleep(0.3)  # 减少延迟，提高收集效率
                    else:
                        logger.warning(f"获取价格失败，跳过第{i+1}次采集")

                except Exception as e:
                    logger.warning(f"第{i+1}次价格采集失败: {e}")
                    continue

            logger.info(f"📊 快速采集完成: 尝试{max_attempts}次，成功{successful_collections}次")

            # 检查是否收集到足够数据
            final_count = len(self.price_history)
            if final_count >= required_count:
                logger.info(f"✅ 成功收集到足够的价格数据: {final_count}/{required_count}")
                logger.info(f"📈 价格范围: {min(self.price_history[-final_count:]):.2f} - {max(self.price_history[-final_count:]):.2f}")
                return True
            else:
                logger.warning(f"⚠️ 数据收集不完整: {final_count}/{required_count}")
                logger.info(f"💡 建议: 系统将在正常运行过程中继续收集价格数据")
                return False

        except Exception as e:
            logger.error(f"收集初始价格数据异常: {e}")
            return False

    def evaluate_signal_reliability(self, bb_data: BollingerBandData) -> Dict[str, Any]:
        """
        评估布林带信号的可靠性

        Args:
            bb_data: 布林带数据

        Returns:
            Dict[str, Any]: 可靠性评估结果
        """
        try:
            reliability_score = bb_data.reliability
            data_sufficiency = bb_data.data_count / bb_data.required_count

            # 基础可靠性评级
            if reliability_score >= 0.95:
                reliability_grade = "A+"
                risk_level = "低"
                recommendation = "可以正常开仓"
            elif reliability_score >= 0.8:
                reliability_grade = "A"
                risk_level = "较低"
                recommendation = "可以谨慎开仓，建议降低仓位"
            elif reliability_score >= 0.6:
                reliability_grade = "B"
                risk_level = "中等"
                recommendation = "建议等待更多数据或使用最小仓位"
            elif reliability_score >= 0.5:
                reliability_grade = "C"
                risk_level = "较高"
                recommendation = "不建议开仓，等待数据充足"
            else:
                reliability_grade = "D"
                risk_level = "高"
                recommendation = "禁止开仓，数据严重不足"

            # %B指标可信度评估
            percent_b_confidence = "高" if reliability_score > 0.8 else "中" if reliability_score > 0.6 else "低"

            # 布林带宽度可信度
            bandwidth_confidence = "高" if reliability_score > 0.8 else "中" if reliability_score > 0.6 else "低"

            # 信号强度调整
            signal_strength_multiplier = min(1.0, reliability_score + 0.2)  # 最高1.0

            evaluation = {
                "reliability_score": reliability_score,
                "reliability_grade": reliability_grade,
                "risk_level": risk_level,
                "recommendation": recommendation,
                "data_sufficiency": data_sufficiency,
                "percent_b_confidence": percent_b_confidence,
                "bandwidth_confidence": bandwidth_confidence,
                "signal_strength_multiplier": signal_strength_multiplier,
                "data_status": {
                    "current": bb_data.data_count,
                    "required": bb_data.required_count,
                    "calculation_period": bb_data.calculation_period
                }
            }

            logger.info(f"📊 信号可靠性评估:")
            logger.info(f"   等级: {reliability_grade} | 风险: {risk_level} | 评分: {reliability_score:.2f}")
            logger.info(f"   %B可信度: {percent_b_confidence} | 带宽可信度: {bandwidth_confidence}")
            logger.info(f"   建议: {recommendation}")

            return evaluation

        except Exception as e:
            logger.error(f"评估信号可靠性异常: {e}")
            return {
                "reliability_score": 0.0,
                "reliability_grade": "F",
                "risk_level": "极高",
                "recommendation": "禁止开仓，评估失败"
            }

    def _generate_opening_reason(self, current_price: float, bb_data: BollingerBandData,
                                direction: str) -> str:
        """
        生成开仓原因说明

        Args:
            current_price: 当前价格
            bb_data: 布林带数据
            direction: 开仓方向

        Returns:
            str: 开仓原因
        """
        try:
            signal_desc = {
                BollingerBandSignal.PRICE_TOUCH_LOWER: "价格触及下轨",
                BollingerBandSignal.PRICE_TOUCH_UPPER: "价格触及上轨",
                BollingerBandSignal.THREE_LINES_DOWN: "三线向下",
                BollingerBandSignal.THREE_LINES_UP: "三线向上"
            }.get(bb_data.signal, "布林带信号")

            direction_desc = "做多" if direction == "long" else "做空"

            return (f"{signal_desc}，{direction_desc}开仓 | "
                   f"当前价格: {current_price:.4f} | "
                   f"上轨: {bb_data.upper_band:.4f} | "
                   f"下轨: {bb_data.lower_band:.4f} | "
                   f"%B: {bb_data.percent_b:.1f}")

        except Exception as e:
            logger.error(f"生成开仓原因异常: {e}")
            return f"{direction}开仓"

    async def execute_auto_opening(self, current_price: float, direction: str) -> Tuple[bool, str]:
        """
        执行自动开仓

        Args:
            current_price: 当前价格
            direction: 开仓方向 ('long' 或 'short')

        Returns:
            Tuple[bool, str]: (是否成功, 结果消息)
        """
        try:
            if not self.auto_opening_enabled or not self.is_running:
                return False, "自动开仓未启用"

            add_count = await self.get_add_records_count()
            if add_count > 0:
                return False, "已有持仓，不能重复开仓"

            # 验证账户状态
            if not self._validate_account_balance(self.account_info, self.initial_margin):
                return False, "账户余额不足或超过风险限制"

            # 计算开仓数量
            quantity = self._calculate_opening_quantity(current_price)
            if quantity <= 0:
                return False, "计算开仓数量失败"

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 执行开仓交易（异步版本）
            # 注意：这个方法需要在异步上下文中调用
            logger.error("execute_auto_opening方法需要重构为异步版本")
            return False, "需要使用异步版本的开仓方法"

            if order_result['success']:
                # 记录开仓信息
                self._record_opening_position(current_price, quantity, direction, order_result)

                logger.info(f"自动开仓成功: {direction}, 价格: {current_price}, 数量: {quantity}")
                return True, f"开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}"
            else:
                logger.error(f"开仓失败: {order_result['error']}")
                return False, f"开仓失败: {order_result['error']}"

        except Exception as e:
            logger.error(f"执行自动开仓异常: {e}")
            return False, f"开仓异常: {str(e)}"

    def _validate_account_balance(self, account_info: Dict, required_margin: float) -> bool:
        """
        验证账户余额和风险控制

        Args:
            account_info: 账户信息
            required_margin: 所需保证金

        Returns:
            bool: 是否通过验证
        """
        try:
            if not account_info:
                return False

            # 检查账户余额
            available_balance = account_info.get('available_balance', 0.0)
            if available_balance < required_margin:
                logger.warning(f"账户余额不足: {available_balance} < {required_margin}")
                return False

            # 检查最大投入资金比例
            total_balance = account_info.get('total_balance', 0.0)
            if total_balance > 0:
                investment_ratio = required_margin / total_balance
                if investment_ratio > self.config.max_investment_ratio:
                    logger.warning(f"超过最大投入比例: {investment_ratio:.2%} > {self.config.max_investment_ratio:.2%}")
                    return False

            return True

        except Exception as e:
            logger.error(f"验证账户余额异常: {e}")
            return False

    async def _calculate_opening_quantity_async(self, current_price: float, exchange=None) -> float:
        """
        异步计算开仓数量（使用交易所API获取正确的交易规则）

        Args:
            current_price: 当前价格
            exchange: 交易所实例

        Returns:
            float: 开仓数量
        """
        try:
            # 基于保证金和杠杆计算数量
            # 数量 = 保证金 * 杠杆 / 当前价格
            raw_quantity = (self.initial_margin * self.leverage) / current_price

            # 如果有交易所实例，获取正确的交易规则
            if exchange and hasattr(exchange, 'get_trading_rules'):
                symbol = getattr(self, 'symbol', 'BNB-USDT-SWAP')

                logger.info(f"🔍 正在获取 {symbol} 的交易规则...")
                trading_rules = await exchange.get_trading_rules(symbol)

                if trading_rules and hasattr(exchange, 'calculate_valid_quantity'):
                    # 使用交易所的方法计算有效数量
                    quantity = exchange.calculate_valid_quantity(raw_quantity, trading_rules)
                    logger.info(f"✅ 使用交易所规则计算数量: 原始={raw_quantity:.6f}, 调整后={quantity:.6f}")
                    return quantity
                else:
                    logger.warning("⚠️ 无法获取交易规则，使用备用方法")

            # 备用方法：使用预设的规则
            return self._calculate_opening_quantity_fallback(raw_quantity)

        except Exception as e:
            logger.error(f"异步计算开仓数量异常: {e}")
            # 发生异常时使用备用方法
            raw_quantity = (self.initial_margin * self.leverage) / current_price
            return self._calculate_opening_quantity_fallback(raw_quantity)

    def _calculate_opening_quantity(self, current_price: float) -> float:
        """
        计算开仓数量（同步版本，向后兼容）

        Args:
            current_price: 当前价格

        Returns:
            float: 开仓数量
        """
        try:
            # 基于保证金和杠杆计算数量
            raw_quantity = (self.initial_margin * self.leverage) / current_price
            return self._calculate_opening_quantity_fallback(raw_quantity)

        except Exception as e:
            logger.error(f"计算开仓数量异常: {e}")
            return 0.0

    def _calculate_opening_quantity_fallback(self, raw_quantity: float) -> float:
        """
        备用的数量计算方法

        Args:
            raw_quantity: 原始数量

        Returns:
            float: 调整后的数量
        """
        try:
            symbol = getattr(self, 'symbol', 'BNB-USDT-SWAP')

            # 根据历史经验设置最小交易单位
            if 'BNB' in symbol:
                # BNB合约通常最小单位是1
                min_lot_size = 1.0
            elif 'BTC' in symbol:
                min_lot_size = 0.001
            elif 'ETH' in symbol:
                min_lot_size = 0.01
            else:
                min_lot_size = 1.0  # 默认最小单位

            # 计算调整后的数量
            quantity = max(min_lot_size, round(raw_quantity / min_lot_size) * min_lot_size)

            logger.info(f"备用方法计算数量: 原始={raw_quantity:.6f}, 最小单位={min_lot_size}, 调整后={quantity:.6f}")
            return quantity

        except Exception as e:
            logger.error(f"备用数量计算异常: {e}")
            return 1.0  # 最保守的默认值

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """
        计算止损价格

        Args:
            entry_price: 入场价格
            direction: 开仓方向

        Returns:
            float: 止损价格
        """
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            return round(stop_loss_price, 4)

        except Exception as e:
            logger.error(f"计算止损价格异常: {e}")
            return 0.0

    @monitor_task(TaskType.ORDER_EXECUTION)
    async def _place_opening_order(self, price: float, direction: str, quantity: float,
                                 stop_loss_price: float) -> Dict:
        """
        执行开仓订单

        Args:
            price: 开仓价格
            direction: 开仓方向
            quantity: 开仓数量
            stop_loss_price: 止损价格

        Returns:
            Dict: 订单执行结果
        """
        try:
            if not self.exchange:
                return {'success': False, 'error': '交易所实例为空'}

            if not self.exchange.is_connected:
                return {'success': False, 'error': '交易所未连接'}

            # 执行真实订单
            try:
                # 转换方向参数
                from exchanges import OrderSide
                order_side = OrderSide.BUY if direction == "long" else OrderSide.SELL

                # 执行市价开仓订单
                order_info = await self.exchange.place_order(
                    symbol=self.symbol,
                    side=order_side,
                    order_type="market",
                    quantity=quantity,
                    price=None  # 市价单不需要价格
                )

                if order_info:
                    # 等待订单执行完成（最多等待10秒）
                    max_wait_time = 10  # 秒
                    wait_interval = 0.5  # 每0.5秒检查一次
                    waited_time = 0

                    logger.info(f"订单已提交: {order_info.order_id}, 状态: {order_info.status}, 等待执行完成...")

                    while waited_time < max_wait_time:
                        # 检查订单是否已完成
                        if order_info.status in ["filled", "partially_filled"]:
                            logger.info(f"订单执行完成: 状态={order_info.status}, 成交数量={order_info.filled}")
                            break
                        elif order_info.status in ["canceled", "rejected", "failed"]:
                            logger.error(f"订单执行失败: 状态={order_info.status}")
                            return {'success': False, 'error': f'订单状态异常: {order_info.status}'}

                        # 等待一段时间后重新查询订单状态
                        await asyncio.sleep(wait_interval)
                        waited_time += wait_interval

                        try:
                            # 重新查询订单状态
                            updated_order = await exchange.get_order_status(symbol, order_info.order_id)
                            if updated_order:
                                order_info = updated_order
                                logger.debug(f"订单状态更新: {order_info.status}, 成交: {order_info.filled}")
                            else:
                                logger.warning("无法查询订单状态，继续等待...")
                        except Exception as e:
                            logger.warning(f"查询订单状态失败: {e}")

                    # 检查最终状态
                    if order_info.status in ["filled", "partially_filled"]:
                        # 设置止损订单（暂时禁用，避免参数错误）
                        if stop_loss_price > 0:
                            logger.info(f"💡 [止损订单] 计算止损价格: {stop_loss_price:.4f}，暂时禁用自动止损订单设置")
                            # await self._place_stop_loss_order(order_info, stop_loss_price, direction)

                        order_result = {
                            'success': True,
                            'order_id': order_info.order_id,
                            'symbol': order_info.symbol,
                            'side': direction,
                            'quantity': order_info.filled,  # 使用实际成交数量
                            'price': order_info.price,  # 使用实际成交价格
                            'stop_loss_price': stop_loss_price,
                            'timestamp': datetime.now(),
                            'status': order_info.status,
                            'error': None
                        }

                        logger.info(f"真实开仓订单执行成功: {order_result}")
                        return order_result
                    else:
                        error_msg = f"订单未完全成交: 状态={order_info.status}, 成交={order_info.filled}, 等待时间={waited_time}秒"
                        logger.error(error_msg)
                        return {'success': False, 'error': error_msg}
                else:
                    return {'success': False, 'error': '订单提交失败'}

            except Exception as api_error:
                logger.error(f"交易所API调用失败: {api_error}")
                return {'success': False, 'error': f'API调用失败: {str(api_error)}'}

        except Exception as e:
            logger.error(f"执行开仓订单异常: {e}")
            return {'success': False, 'error': str(e)}

    async def _place_stop_loss_order(self, original_order, stop_loss_price: float, direction: str) -> None:
        """
        设置止损订单

        Args:
            original_order: 原始开仓订单信息
            stop_loss_price: 止损价格
            direction: 开仓方向
        """
        try:
            if not self.exchange or not self.exchange.is_connected:
                logger.error("无法设置止损订单：交易所未连接")
                return

            # 止损订单方向与开仓方向相反
            from exchanges import OrderSide
            stop_side = OrderSide.SELL if direction == "long" else OrderSide.BUY

            # 设置止损订单（使用OKX策略委托接口）
            stop_order = await self._place_okx_algo_stop_loss_order(
                symbol=self.symbol,
                side=stop_side,
                quantity=original_order.filled,
                stop_loss_price=stop_loss_price,
                direction=direction
            )

            if stop_order:
                logger.info(f"止损订单设置成功: {stop_order.order_id} @ {stop_loss_price}")
            else:
                logger.error("止损订单设置失败")

        except Exception as e:
            logger.error(f"设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, symbol: str, side, quantity: float, stop_loss_price: float, direction: str):
        """
        使用OKX策略委托接口设置止损订单

        Args:
            symbol: 交易对
            side: 订单方向
            quantity: 数量
            stop_loss_price: 止损价格
            direction: 开仓方向
        """
        try:
            logger.info(f"🔧 [OKX策略委托] 设置止损订单: {symbol}, 方向: {side.value}, 数量: {quantity}, 止损价: {stop_loss_price}")

            # 检查交易所类型
            if not hasattr(self.exchange, 'place_algo_order'):
                logger.warning("⚠️ [止损订单] 交易所不支持策略委托，使用限价单替代")
                return await self._place_limit_stop_loss_order(symbol, side, quantity, stop_loss_price)

            # 使用OKX策略委托接口
            # ordType: "conditional" - 单向止盈止损
            # triggerPx: 触发价格
            # orderPx: 委托价格（-1表示市价）
            algo_order = await self.exchange.place_algo_order(
                symbol=symbol,
                side=side.value.lower(),  # "buy" 或 "sell"
                order_type="conditional",  # 条件单
                quantity=quantity,
                trigger_price=stop_loss_price,  # 触发价格
                order_price=-1,  # 市价执行
                reduce_only=True  # 只减仓
            )

            if algo_order:
                logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {algo_order.order_id}")
                return algo_order
            else:
                logger.error("❌ [OKX策略委托] 止损订单设置失败")
                return None

        except Exception as e:
            logger.error(f"❌ [OKX策略委托] 设置止损订单异常: {e}")
            # 降级到限价单
            logger.info("🔄 [止损订单] 降级使用限价单")
            return await self._place_limit_stop_loss_order(symbol, side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, symbol: str, side, quantity: float, stop_loss_price: float):
        """
        使用限价单作为止损订单的备选方案
        """
        try:
            logger.info(f"🔧 [限价止损] 设置限价止损订单: {symbol}, 方向: {side.value}, 数量: {quantity}, 价格: {stop_loss_price}")

            # 使用限价单，价格设置为止损价格
            limit_order = await self.exchange.place_order(
                symbol=symbol,
                side=side.value.lower(),
                order_type="limit",
                quantity=quantity,
                price=stop_loss_price
            )

            if limit_order:
                logger.info(f"✅ [限价止损] 限价止损订单设置成功: {limit_order.order_id}")
                return limit_order
            else:
                logger.error("❌ [限价止损] 限价止损订单设置失败")
                return None

        except Exception as e:
            logger.error(f"❌ [限价止损] 设置限价止损订单异常: {e}")
            return None

    # 移除同步版本的下单方法，使用纯异步架构

    async def execute_auto_opening_async(self, current_price: float, direction: str, exchange=None, force_execute: bool = False) -> Tuple[bool, str]:
        """
        异步执行自动开仓

        Args:
            current_price: 当前价格
            direction: 开仓方向 ('long' 或 'short')
            exchange: 交易所实例
            force_execute: 强制执行，忽略状态检查

        Returns:
            Tuple[bool, str]: (是否成功, 结果消息)
        """
        try:
            # 如果不是强制执行，则检查状态
            if not force_execute and (not self.auto_opening_enabled or not self.is_running):
                return False, "自动开仓未启用"

            add_count = await self.get_add_records_count()
            if add_count > 0:
                return False, "已有持仓，不能重复开仓"

            # 验证账户状态
            if not self._validate_account_balance(self.account_info, self.initial_margin):
                return False, "账户余额不足或超过风险限制"

            # 异步计算开仓数量（使用交易所API获取正确规则）
            quantity = await self._calculate_opening_quantity_async(current_price, exchange)
            if quantity <= 0:
                return False, "计算开仓数量失败"

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 执行开仓交易（异步）
            order_result = await self._place_opening_order(current_price, direction, quantity, stop_loss_price)

            if order_result['success']:
                # 记录开仓信息
                self._record_opening_position(current_price, quantity, direction, order_result)

                logger.info(f"自动开仓成功: {direction}, 价格: {current_price}, 数量: {quantity}")
                return True, f"开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}"
            else:
                logger.error(f"开仓失败: {order_result['error']}")
                return False, f"开仓失败: {order_result['error']}"

        except Exception as e:
            logger.error(f"执行自动开仓异常: {e}")
            return False, f"开仓异常: {str(e)}"

    async def _place_opening_order_async(self, price: float, direction: str, quantity: float,
                                       stop_loss_price: float) -> Dict:
        """
        异步执行开仓订单

        Args:
            price: 开仓价格
            direction: 开仓方向
            quantity: 开仓数量
            stop_loss_price: 止损价格

        Returns:
            Dict: 订单执行结果
        """
        return await self._place_opening_order(price, direction, quantity, stop_loss_price)

    async def _record_opening_position(self, price: float, quantity: float, direction: str,
                               order_result: Dict) -> None:
        """
        记录开仓位置信息

        Args:
            price: 开仓价格
            quantity: 开仓数量
            direction: 开仓方向
            order_result: 订单执行结果
        """
        try:
            # 创建开仓记录
            opening_record = AddPositionRecord(
                timestamp=datetime.now(),
                price=price,
                quantity=quantity,
                margin=self.initial_margin,
                add_type=AddPositionType.EQUAL,
                trigger_reason=f"布林带{direction}开仓",
                new_avg_cost=price,
                total_margin=self.initial_margin,
                add_count=1
            )

            # 添加到记录列表
            await self.add_position_record(opening_record)

            # 更新当前持仓信息
            self.current_position = {
                'symbol': self.symbol,
                'side': direction,
                'entry_price': price,
                'quantity': quantity,
                'margin': self.initial_margin,
                'leverage': self.leverage,
                'stop_loss_price': order_result.get('stop_loss_price', 0.0),
                'order_id': order_result.get('order_id', ''),
                'timestamp': datetime.now()
            }

            logger.info(f"开仓记录已保存: {opening_record}")

        except Exception as e:
            logger.error(f"记录开仓位置异常: {e}")

    async def validate_account_balance_async(self, exchange) -> bool:
        """
        异步验证账户余额是否充足

        Args:
            exchange: 交易所实例

        Returns:
            bool: 余额是否充足
        """
        try:
            logger.info("🔍 [余额验证] 开始验证账户余额...")

            # 获取账户信息
            account_info = await exchange.get_account_info()
            if not account_info:
                logger.error("❌ [余额验证] 无法获取账户信息")
                return False

            # 调试信息：显示账户信息结构
            logger.info(f"📊 [余额验证] 账户信息类型: {type(account_info)}")
            if isinstance(account_info, dict):
                logger.info(f"📊 [余额验证] 账户信息字段: {list(account_info.keys())}")
            else:
                logger.info(f"📊 [余额验证] 账户信息属性: {dir(account_info)}")

            # 获取可用余额（处理不同的返回格式）
            if hasattr(account_info, 'available_balance'):
                available_balance = account_info.available_balance
            elif isinstance(account_info, dict):
                available_balance = account_info.get('available_balance', 0)
                if available_balance == 0:
                    # 尝试其他可能的字段名
                    available_balance = account_info.get('availableBalance', 0)
                    if available_balance == 0:
                        available_balance = account_info.get('balance', 0)
            else:
                logger.error(f"❌ [余额验证] 未知的账户信息格式: {type(account_info)}")
                return False

            logger.info(f"💰 [余额验证] 可用余额: {available_balance:.2f} USDT")

            # 计算所需保证金
            required_margin = self.config.initial_margin / self.config.leverage
            logger.info(f"📊 [余额验证] 所需保证金: {required_margin:.2f} USDT")

            # 添加安全边际（20%）
            safety_margin = required_margin * 1.2
            logger.info(f"🛡️ [余额验证] 安全保证金: {safety_margin:.2f} USDT")

            # 验证余额充足性
            if available_balance >= safety_margin:
                logger.info(f"✅ [余额验证] 余额充足: {available_balance:.2f} >= {safety_margin:.2f}")
                return True
            else:
                logger.warning(f"⚠️ [余额验证] 余额不足: {available_balance:.2f} < {safety_margin:.2f}")
                return False

        except Exception as e:
            logger.error(f"❌ [余额验证] 验证失败: {e}")
            return False

    async def check_take_profit_stop_loss(self, exchange, symbol: str) -> Dict[str, Any]:
        """
        检查止盈止损条件

        Args:
            exchange: 交易所实例
            symbol: 交易对符号

        Returns:
            Dict: 检查结果
        """
        try:
            logger.info("🔍 [风险控制] 开始检查止盈止损条件...")

            # 获取当前持仓
            positions = await exchange.get_positions(symbol)
            if not positions:
                return {
                    'action': 'none',
                    'reason': '无持仓，无需检查止盈止损',
                    'should_close': False
                }

            position = positions[0]
            current_price = await exchange.get_current_price(symbol)

            # 计算盈亏
            if position.side.value == 'long':
                pnl_percent = (current_price - position.entry_price) / position.entry_price * 100
            else:
                pnl_percent = (position.entry_price - current_price) / position.entry_price * 100

            logger.info(f"💰 [风险控制] 当前盈亏: {pnl_percent:.2f}%")
            logger.info(f"📊 [风险控制] 持仓信息: {position.side.value}仓, 入场价: {position.entry_price:.4f}, 当前价: {current_price:.4f}")

            # 检查止盈条件
            if pnl_percent >= self.config.take_profit_percent:
                logger.info(f"🎯 [风险控制] 触发止盈: {pnl_percent:.2f}% >= {self.config.take_profit_percent}%")
                return {
                    'action': 'take_profit',
                    'reason': f'达到止盈目标: {pnl_percent:.2f}%',
                    'should_close': True,
                    'pnl_percent': pnl_percent,
                    'position': position
                }

            # 检查止损条件
            elif pnl_percent <= -self.config.stop_loss_percent:
                logger.warning(f"🛑 [风险控制] 触发止损: {pnl_percent:.2f}% <= -{self.config.stop_loss_percent}%")
                return {
                    'action': 'stop_loss',
                    'reason': f'达到止损线: {pnl_percent:.2f}%',
                    'should_close': True,
                    'pnl_percent': pnl_percent,
                    'position': position
                }

            else:
                logger.info(f"📊 [风险控制] 盈亏正常: {pnl_percent:.2f}% (止盈: {self.config.take_profit_percent}%, 止损: -{self.config.stop_loss_percent}%)")
                return {
                    'action': 'hold',
                    'reason': f'盈亏正常，继续持有: {pnl_percent:.2f}%',
                    'should_close': False,
                    'pnl_percent': pnl_percent,
                    'position': position
                }

        except Exception as e:
            logger.error(f"❌ [风险控制] 检查止盈止损失败: {e}")
            return {
                'action': 'error',
                'reason': f'检查失败: {str(e)}',
                'should_close': False
            }

    async def execute_close_position(self, exchange, symbol: str, reason: str) -> bool:
        """
        执行平仓操作

        Args:
            exchange: 交易所实例
            symbol: 交易对符号
            reason: 平仓原因

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"🚀 [风险控制] 开始执行平仓: {reason}")

            # 获取当前持仓
            positions = await exchange.get_positions(symbol)
            if not positions:
                logger.warning("⚠️ [风险控制] 无持仓，无需平仓")
                return True

            position = positions[0]

            # 确定平仓方向
            from exchanges import OrderSide
            if position.side.value == 'long':
                close_side = OrderSide.SELL
            else:
                close_side = OrderSide.BUY

            # 执行市价平仓
            logger.info(f"📤 [风险控制] 执行市价平仓: {position.side.value}仓, 数量: {position.size}")

            order_result = await exchange.place_order(
                symbol=symbol,
                side=close_side,
                order_type="market",
                quantity=position.size
            )

            if order_result and order_result.order_id:
                logger.info(f"✅ [风险控制] 平仓成功: 订单ID {order_result.order_id}")

                # 重置策略状态
                self.reset_strategy()

                return True
            else:
                logger.error("❌ [风险控制] 平仓失败: 订单执行失败")
                return False

        except Exception as e:
            logger.error(f"❌ [风险控制] 执行平仓异常: {e}")
            return False

    def get_current_position_info(self) -> Optional[Dict]:
        """
        获取当前持仓信息

        Returns:
            Optional[Dict]: 持仓信息，None表示无持仓
        """
        return self.current_position

    async def get_opening_records(self) -> List[AddPositionRecord]:
        """
        获取开仓记录列表

        Returns:
            List[AddPositionRecord]: 开仓记录列表
        """
        return await self.get_add_records_copy()

    async def reset_strategy(self) -> None:
        """重置策略状态"""
        try:
            await self.clear_add_records()
            self.current_position = None
            # 清理价格历史需要使用锁
            async with self._price_history_lock:
                self.price_history.clear()
            await self.clear_bb_history()
            self.emergency_stopped = False
            self.is_running = False
            self.is_active = False
            self.auto_opening_enabled = False

            logger.info("布林带策略状态已重置")

        except Exception as e:
            logger.error(f"重置策略状态异常: {e}")
    
    def calculate_add_position_size(self, add_count: int, initial_margin: float) -> Tuple[float, AddPositionType]:
        """计算加仓数量和类型"""
        if add_count >= len(self.config.add_position_types):
            # 如果超出预设类型，使用最后一种类型
            add_type = self.config.add_position_types[-1]
        else:
            add_type = self.config.add_position_types[add_count]
        
        # 根据加仓类型计算保证金
        if add_type == AddPositionType.EQUAL:
            margin = initial_margin
        elif add_type == AddPositionType.HALF:
            margin = initial_margin * 0.5
        elif add_type == AddPositionType.QUARTER:
            margin = initial_margin * 0.25
        else:
            margin = initial_margin
        
        return margin, add_type
    
    async def add_position(self, price: float, initial_margin: float,
                    trigger_reason: str = "") -> Optional[AddPositionRecord]:
        """执行加仓"""
        try:
            add_count = await self.get_add_records_count()
            margin, add_type = self.calculate_add_position_size(add_count, initial_margin)
            
            # 计算数量
            quantity = margin / price
            
            # 计算新的平均成本
            current_total_margin = await self.get_total_margin()
            current_total_quantity = await self.get_total_quantity()
            total_margin = current_total_margin + margin
            total_quantity = current_total_quantity + quantity
            new_avg_cost = total_margin / total_quantity if total_quantity > 0 else price
            
            # 创建加仓记录
            record = AddPositionRecord(
                timestamp=datetime.now(),
                price=price,
                quantity=quantity,
                margin=margin,
                add_type=add_type,
                trigger_reason=trigger_reason,
                new_avg_cost=new_avg_cost,
                total_margin=total_margin,
                add_count=add_count + 1
            )
            
            await self.add_position_record(record)
            logger.info(f"执行加仓: 价格={price}, 数量={quantity:.6f}, 类型={add_type.value}")
            
            return record
            
        except Exception as e:
            logger.error(f"执行加仓失败: {e}")
            return None
    
    async def calculate_new_stop_loss(self, stop_loss_percent: float) -> float:
        """计算新的止损价"""
        latest_record = await self.get_latest_add_record()
        if not latest_record:
            return 0

        new_stop_loss = latest_record.new_avg_cost * (1 - stop_loss_percent / 100)
        return new_stop_loss
    
    async def check_total_risk(self, current_price: float) -> Tuple[bool, float]:
        """检查总体风险"""
        latest_record = await self.get_latest_add_record()
        if not latest_record:
            return False, 0

        total_margin = latest_record.total_margin
        total_quantity = await self.get_total_quantity()
        current_value = total_quantity * current_price
        
        total_loss = total_margin - current_value
        loss_percent = total_loss / total_margin * 100 if total_margin > 0 else 0
        
        is_risk_exceeded = loss_percent > self.config.max_total_loss_percent
        return is_risk_exceeded, loss_percent
    
    def reset_strategy(self):
        """重置策略状态"""
        self.add_records.clear()
        self.current_position = None
        self.is_active = False
        self.emergency_stopped = False
        logger.info("布林带策略状态已重置")
    
    async def get_strategy_summary(self) -> Dict:
        """获取策略摘要"""
        latest_record = await self.get_latest_add_record()
        if not latest_record:
            return {
                'total_adds': 0,
                'total_margin': 0,
                'avg_cost': 0,
                'total_quantity': 0
            }

        return {
            'total_adds': await self.get_add_records_count(),
            'total_margin': latest_record.total_margin,
            'avg_cost': latest_record.new_avg_cost,
            'total_quantity': await self.get_total_quantity(),
            'last_add_price': latest_record.price,
            'last_add_time': latest_record.timestamp
        }

    def convert_timeframe_to_minutes(self, timeframe: str) -> int:
        """
        将时间周期字符串转换为分钟数

        Args:
            timeframe: 时间周期字符串 (如: "15m", "1h", "1d")

        Returns:
            int: 对应的分钟数
        """
        try:
            timeframe = timeframe.lower().strip()

            if timeframe.endswith('m'):
                return int(timeframe[:-1])
            elif timeframe.endswith('h'):
                return int(timeframe[:-1]) * 60
            elif timeframe.endswith('d'):
                return int(timeframe[:-1]) * 24 * 60
            else:
                # 默认按分钟处理
                return int(timeframe) if timeframe.isdigit() else 60

        except Exception as e:
            logger.error(f"时间周期转换失败: {e}")
            return 60  # 默认1小时

    def validate_timeframe_compatibility(self, base_timeframe: str, signal_timeframe: str) -> bool:
        """
        验证基础时间周期和信号时间周期的兼容性

        Args:
            base_timeframe: 基础时间周期
            signal_timeframe: 信号确认时间周期

        Returns:
            bool: 是否兼容
        """
        try:
            base_minutes = self.convert_timeframe_to_minutes(base_timeframe)
            signal_minutes = self.convert_timeframe_to_minutes(signal_timeframe)

            # 信号时间周期应该大于等于基础时间周期
            if signal_minutes >= base_minutes:
                return True
            else:
                logger.warning(f"信号时间周期({signal_timeframe})小于基础时间周期({base_timeframe})")
                return False

        except Exception as e:
            logger.error(f"时间周期兼容性验证失败: {e}")
            return False

    def get_timeframe_multiplier(self, base_timeframe: str, signal_timeframe: str) -> int:
        """
        获取信号时间周期相对于基础时间周期的倍数

        Args:
            base_timeframe: 基础时间周期
            signal_timeframe: 信号确认时间周期

        Returns:
            int: 时间周期倍数
        """
        try:
            base_minutes = self.convert_timeframe_to_minutes(base_timeframe)
            signal_minutes = self.convert_timeframe_to_minutes(signal_timeframe)

            if base_minutes > 0:
                multiplier = signal_minutes // base_minutes
                return max(1, multiplier)  # 至少为1
            else:
                return 1

        except Exception as e:
            logger.error(f"时间周期倍数计算失败: {e}")
            return 1

    def is_emergency_add_situation(self, current_price: float, position_info: dict) -> bool:
        """
        检测是否为紧急加仓情况

        Args:
            current_price: 当前价格
            position_info: 持仓信息

        Returns:
            bool: 是否需要紧急加仓
        """
        try:
            if not self.enable_emergency_add:
                return False

            if not position_info or position_info.get('size', 0) <= 0:
                return False  # 没有持仓，不需要紧急加仓

            # 获取持仓方向和入场价格
            position_side = position_info.get('side', 'long')
            entry_price = position_info.get('entry_price', 0)

            if entry_price <= 0:
                return False

            # 计算止损价格
            stop_loss_percent = self.stop_loss_percent / 100.0

            if position_side == 'long':
                # 多仓止损价格
                stop_loss_price = entry_price * (1 - stop_loss_percent)
                # 计算当前价格到止损价格的距离
                distance_to_stop = current_price - stop_loss_price
                total_stop_distance = entry_price - stop_loss_price
            else:
                # 空仓止损价格
                stop_loss_price = entry_price * (1 + stop_loss_percent)
                # 计算当前价格到止损价格的距离
                distance_to_stop = stop_loss_price - current_price
                total_stop_distance = stop_loss_price - entry_price

            if total_stop_distance <= 0:
                return False

            # 计算距离比例
            distance_ratio = distance_to_stop / total_stop_distance

            # 如果距离止损线的比例小于等于阈值，触发紧急加仓
            # 注意：distance_ratio越小表示越接近止损线
            is_emergency = distance_ratio <= self.emergency_distance_ratio

            if is_emergency:
                logger.warning(f"检测到紧急加仓情况: 当前价格{current_price}, "
                             f"止损价格{stop_loss_price:.4f}, "
                             f"距离比例{distance_ratio:.2%}, "
                             f"阈值{self.emergency_distance_ratio:.2%}")

            return is_emergency

        except Exception as e:
            logger.error(f"紧急加仓检测失败: {e}")
            return False

    def should_require_signal_confirmation(self, is_opening: bool, is_emergency: bool = False) -> bool:
        """
        判断是否需要布林带信号确认

        Args:
            is_opening: 是否为开仓操作
            is_emergency: 是否为紧急情况

        Returns:
            bool: 是否需要信号确认
        """
        try:
            # 紧急情况下不需要信号确认
            if is_emergency:
                logger.info("紧急情况，跳过布林带信号确认")
                return False

            # 根据开仓/加仓类型决定是否需要信号确认
            if is_opening:
                return self.require_signal_for_opening
            else:
                return self.require_signal_for_adding

        except Exception as e:
            logger.error(f"信号确认判断失败: {e}")
            return True  # 默认需要确认，更安全

    # ==================== 线程安全的价格历史管理 ====================

    async def add_price_to_history(self, price: float) -> None:
        """线程安全地添加价格到历史记录"""
        async with self._price_history_lock:
            self.price_history.append(price)
            # 限制历史记录长度
            max_length = self.config.bb_period * 2
            if len(self.price_history) > max_length:
                self.price_history = self.price_history[-max_length:]

    async def get_price_history_copy(self) -> List[float]:
        """线程安全地获取价格历史副本"""
        async with self._price_history_lock:
            return self.price_history.copy()

    async def get_price_history_length(self) -> int:
        """线程安全地获取价格历史长度"""
        async with self._price_history_lock:
            return len(self.price_history)

    # ==================== 线程安全的加仓记录管理 ====================

    async def add_position_record(self, record: AddPositionRecord) -> None:
        """线程安全地添加加仓记录"""
        async with self._add_records_lock:
            self.add_records.append(record)

    async def get_add_records_copy(self) -> List[AddPositionRecord]:
        """线程安全地获取加仓记录副本"""
        async with self._add_records_lock:
            return self.add_records.copy()

    async def get_add_records_count(self) -> int:
        """线程安全地获取加仓记录数量"""
        async with self._add_records_lock:
            return len(self.add_records)

    async def clear_add_records(self) -> None:
        """线程安全地清空加仓记录"""
        async with self._add_records_lock:
            self.add_records.clear()

    async def get_latest_add_record(self) -> Optional[AddPositionRecord]:
        """线程安全地获取最新加仓记录"""
        async with self._add_records_lock:
            return self.add_records[-1] if self.add_records else None

    async def get_total_margin(self) -> float:
        """线程安全地计算总保证金"""
        async with self._add_records_lock:
            return sum(record.margin for record in self.add_records)

    async def get_total_quantity(self) -> float:
        """线程安全地计算总数量"""
        async with self._add_records_lock:
            return sum(record.quantity for record in self.add_records)

    # ==================== 线程安全的布林带历史管理 ====================

    async def add_bb_history(self, bb_data: BollingerBandData, max_length: int = 100) -> None:
        """线程安全地添加布林带历史数据"""
        async with self._bb_history_lock:
            self.bb_history.append(bb_data)
            if len(self.bb_history) > max_length:
                self.bb_history = self.bb_history[-max_length:]

    async def get_bb_history_copy(self) -> List[BollingerBandData]:
        """线程安全地获取布林带历史副本"""
        async with self._bb_history_lock:
            return self.bb_history.copy()

    async def get_latest_bb_data(self) -> Optional[BollingerBandData]:
        """线程安全地获取最新布林带数据"""
        async with self._bb_history_lock:
            return self.bb_history[-1] if self.bb_history else None

    async def get_bb_history_length(self) -> int:
        """线程安全地获取布林带历史长度"""
        async with self._bb_history_lock:
            return len(self.bb_history)

    async def clear_bb_history(self) -> None:
        """线程安全地清空布林带历史"""
        async with self._bb_history_lock:
            self.bb_history.clear()
