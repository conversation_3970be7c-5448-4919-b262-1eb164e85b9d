#!/usr/bin/env python3
"""
BIT交易系统实盘交易配置脚本
将系统配置为完全的实盘交易模式
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主配置函数"""
    print("🚨 BIT交易系统实盘交易配置工具")
    print("=" * 70)
    print(f"📅 配置时间: {datetime.now()}")
    print("=" * 70)
    
    print("\n⚠️ 重要风险警告:")
    print("   🔴 您即将配置系统为完全的实盘交易模式")
    print("   🔴 所有交易将使用真实资金")
    print("   🔴 可能导致资金损失")
    print("   🔴 请确保您已充分理解风险")
    
    print("\n✅ 已完成的实盘交易配置:")
    
    configurations = [
        {
            "component": "默认配置",
            "changes": [
                "沙盒模式默认设置为 False（实盘模式）",
                "移除所有模拟交易相关的默认设置",
                "确保所有API调用连接到生产环境"
            ]
        },
        {
            "component": "用户界面",
            "changes": [
                "添加实盘交易模式的醒目警告标识",
                "沙盒模式作为可选项，需要主动启用",
                "实时显示当前交易模式状态",
                "添加交易模式切换的风险确认"
            ]
        },
        {
            "component": "风险确认机制",
            "changes": [
                "开始交易前的多重风险确认",
                "实盘模式的二次确认机制",
                "策略启动前的风险警告",
                "连接测试时的模式明确提示"
            ]
        },
        {
            "component": "安全验证",
            "changes": [
                "API配置正确性验证",
                "交易所连接状态验证",
                "实盘环境连接确认",
                "用户风险理解确认"
            ]
        }
    ]
    
    for i, config in enumerate(configurations, 1):
        print(f"\n   {i}. {config['component']}:")
        for change in config['changes']:
            print(f"      ✅ {change}")
    
    print("\n🔧 实盘交易功能确认:")
    
    live_features = [
        "所有策略（MACD、布林带、插针等）使用真实交易",
        "开仓操作使用真实资金和真实订单",
        "加仓操作基于真实持仓和资金",
        "平仓操作影响真实持仓和盈亏",
        "止损止盈基于真实市场价格执行",
        "持仓监控基于真实交易所数据",
        "资金操作使用真实账户余额"
    ]
    
    for i, feature in enumerate(live_features, 1):
        print(f"   {i}. 🔴 {feature}")
    
    print("\n🛡️ 保留的安全机制:")
    
    safety_features = [
        "风险控制参数（止损、止盈、最大持仓）",
        "资金管理限制（最大投入比例、日盈亏限制）",
        "紧急停止功能",
        "实时持仓监控和风险预警",
        "API权限验证和安全检查",
        "交易冷却时间和频率限制",
        "多重确认机制防止误操作"
    ]
    
    for i, feature in enumerate(safety_features, 1):
        print(f"   {i}. 🔒 {feature}")
    
    print("\n📋 实盘交易操作流程:")
    
    workflow = [
        "启动BIT交易系统",
        "进入'配置设置'标签页",
        "确认交易模式显示为'🔴 实盘交易模式'",
        "填写真实的API配置信息",
        "点击'测试连接'验证连接到生产环境",
        "配置交易参数（交易对、杠杆、保证金）",
        "设置风险控制参数",
        "选择要使用的交易策略",
        "启动策略时确认实盘交易风险",
        "密切监控交易状态和盈亏"
    ]
    
    for i, step in enumerate(workflow, 1):
        print(f"   {i}. {step}")
    
    print("\n⚠️ 实盘交易风险提醒:")
    
    risks = [
        {
            "风险": "资金损失风险",
            "说明": "实盘交易可能导致部分或全部资金损失",
            "建议": "只投入可承受损失的资金，从小额开始"
        },
        {
            "风险": "市场波动风险", 
            "说明": "加密货币市场波动剧烈，价格变化难以预测",
            "建议": "设置合理的止损，控制单次交易风险"
        },
        {
            "风险": "技术风险",
            "说明": "网络中断、系统故障可能影响交易执行",
            "建议": "确保网络稳定，定期检查系统状态"
        },
        {
            "风险": "策略风险",
            "说明": "交易策略可能在某些市场条件下失效",
            "建议": "充分回测策略，持续监控和调整"
        }
    ]
    
    for i, risk in enumerate(risks, 1):
        print(f"\n   {i}. ⚠️ {risk['风险']}")
        print(f"      说明: {risk['说明']}")
        print(f"      建议: {risk['建议']}")
    
    print("\n🔑 API配置要求（实盘环境）:")
    
    api_requirements = {
        "OKX实盘": [
            "API Key: 从OKX生产环境获取",
            "API Secret: 对应的私钥",
            "Passphrase: 创建API时设置的密码短语",
            "权限: 读取 + 交易（不要开启提币权限）",
            "环境: 确保连接到生产环境，不是沙盒"
        ],
        "Gate.io实盘": [
            "API Key: 从Gate.io生产环境获取",
            "API Secret: 对应的私钥",
            "Passphrase: 留空",
            "权限: 现货交易 + 合约交易 + 查看",
            "环境: 确保连接到生产环境"
        ]
    }
    
    for exchange, requirements in api_requirements.items():
        print(f"\n   📈 {exchange}:")
        for req in requirements:
            print(f"      • {req}")
    
    print("\n🚨 最终安全检查清单:")
    
    safety_checklist = [
        "[ ] 已充分理解实盘交易风险",
        "[ ] 已在沙盒模式下充分测试策略",
        "[ ] API配置来自真实交易所生产环境",
        "[ ] 设置了合理的止损和风险控制",
        "[ ] 只投入可承受损失的资金",
        "[ ] 确保网络连接稳定可靠",
        "[ ] 已准备好密切监控交易状态",
        "[ ] 了解如何紧急停止交易",
        "[ ] 确认所有配置参数正确",
        "[ ] 已阅读并理解所有风险警告"
    ]
    
    for item in safety_checklist:
        print(f"   {item}")
    
    print("\n📞 紧急情况处理:")
    print("   🚨 如需紧急停止交易:")
    print("      1. 点击'停止交易'按钮")
    print("      2. 使用'紧急停止'功能")
    print("      3. 手动平仓所有持仓")
    print("      4. 直接在交易所平台操作")
    
    print("\n🏆 配置完成总结:")
    print("   🎉 BIT交易系统已配置为完全实盘交易模式")
    print("   🎉 所有安全机制和风险控制功能保持完整")
    print("   🎉 用户界面已更新为实盘交易警告")
    print("   🎉 多重确认机制已启用")
    print("   ⚠️ 请务必谨慎操作，理性交易")
    
    print("\n📋 下一步操作:")
    print("   1. 重新启动BIT交易系统")
    print("   2. 验证界面显示实盘交易模式")
    print("   3. 配置真实的API密钥")
    print("   4. 测试连接到生产环境")
    print("   5. 从小额资金开始实盘交易")
    
    return True

if __name__ == "__main__":
    try:
        result = main()
        print(f"\n{'='*70}")
        print("🚀 实盘交易配置完成！请谨慎操作，理性交易。")
        print(f"{'='*70}")
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ 配置过程异常: {e}")
        sys.exit(1)
