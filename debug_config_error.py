"""
精确定位配置验证错误的调试脚本
"""
import sys
import os
import traceback

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_config_validation():
    """调试配置验证错误"""
    print("🔍 开始调试配置验证错误...")
    
    try:
        from gui.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 创建一个基本的测试配置
        test_config = {
            "exchange": "okx",
            "api_key": "test_key",
            "api_secret": "test_secret",
            "passphrase": "test_pass",
            "sandbox": True,
            "symbol": "BTC-USDT-SWAP",
            "leverage": 10,
            "initial_margin": 100.0,
            "timeframe": "30m",
            "alert_points": 0.5,
            "add_position_types": ["equal"],
            "max_add_times": 3,
            "bollinger_enabled": True,
            "bb_period": 20,
            "bb_std_dev": 2.0,
            "bb_trigger_distance_points": 50,
            "bb_trigger_distance_percent": 1.0,
            "bb_max_add_count": 3,
            "bb_add_intervals": [5.0, 8.0, 12.0],
            "bb_max_total_loss_percent": 15.0,
            "bb_max_investment_ratio": 0.2,
            "bb_take_profit_percent": 2.0,
            "bb_stop_loss_percent": 5.0,
            "bb_signal_timeframe": "1h",
            "bb_emergency_distance_ratio": 0.8,
            "max_open_positions": 10,
            "margin_ratio": 0.8,
            "max_position_ratio": 0.1,
            "daily_pnl_limit": 0.2,
            "max_open_amount": 4
        }
        
        print("📋 测试配置创建完成")
        
        # 逐步测试每个验证方法
        print("\n🔧 测试各个验证方法:")
        
        # 1. 测试布林带策略参数验证
        print("1. 测试布林带策略参数验证...")
        try:
            result = config_manager.validate_bollinger_strategy_params(test_config)
            print(f"   结果类型: {type(result)}")
            print(f"   结果值: {result}")
            if isinstance(result, tuple) and len(result) == 2:
                is_valid, message = result
                print(f"   ✅ 布林带验证正常: {is_valid}, {message}")
            else:
                print(f"   ❌ 布林带验证返回格式错误: {result}")
        except Exception as e:
            print(f"   ❌ 布林带验证异常: {e}")
            traceback.print_exc()
        
        # 2. 测试风险管理参数验证
        print("\n2. 测试风险管理参数验证...")
        try:
            result = config_manager.validate_risk_management_params(test_config)
            print(f"   结果类型: {type(result)}")
            print(f"   结果值: {result}")
            if isinstance(result, tuple) and len(result) == 2:
                is_valid, message = result
                print(f"   ✅ 风险管理验证正常: {is_valid}, {message}")
            else:
                print(f"   ❌ 风险管理验证返回格式错误: {result}")
        except Exception as e:
            print(f"   ❌ 风险管理验证异常: {e}")
            traceback.print_exc()
        
        # 3. 测试完整配置验证
        print("\n3. 测试完整配置验证...")
        try:
            result = config_manager.validate_config(test_config)
            print(f"   结果类型: {type(result)}")
            print(f"   结果值: {result}")
            if isinstance(result, tuple) and len(result) == 2:
                is_valid, message = result
                print(f"   ✅ 完整配置验证正常: {is_valid}, {message}")
            else:
                print(f"   ❌ 完整配置验证返回格式错误: {result}")
        except Exception as e:
            print(f"   ❌ 完整配置验证异常: {e}")
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 调试过程异常: {e}")
        traceback.print_exc()
        return False

def debug_gui_calls():
    """调试GUI中的调用"""
    print("\n🔍 调试GUI中的配置验证调用...")
    
    try:
        # 模拟GUI中的调用方式
        import tkinter as tk
        from gui.main_window import MainWindow
        
        # 创建隐藏窗口进行测试
        root = tk.Tk()
        root.withdraw()
        
        print("📋 创建MainWindow实例...")
        
        # 这里不能完全初始化MainWindow，因为它会创建完整的GUI
        # 我们只测试配置管理器部分
        from gui.config_manager import ConfigManager
        config_manager = ConfigManager()
        
        # 加载默认配置
        current_config = config_manager.load_config()
        print(f"✅ 配置加载成功，类型: {type(current_config)}")
        
        # 测试配置验证（模拟GUI中的调用）
        print("🔧 模拟GUI中的配置验证调用...")
        try:
            is_valid, message = config_manager.validate_config(current_config)
            print(f"✅ GUI模拟调用成功: {is_valid}, {message}")
        except Exception as e:
            print(f"❌ GUI模拟调用失败: {e}")
            traceback.print_exc()
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI调试异常: {e}")
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("="*60)
    print("🎯 配置验证错误精确调试")
    print("="*60)
    
    print("📋 调试目标:")
    print("   • 精确定位 'cannot unpack non-iterable bool object' 错误")
    print("   • 确认每个验证方法的返回值类型")
    print("   • 找出返回布尔值而非元组的方法")
    
    # 执行调试
    debug_tests = [
        ("配置验证方法", debug_config_validation),
        ("GUI调用模拟", debug_gui_calls)
    ]
    
    for test_name, test_func in debug_tests:
        print(f"\n📊 {test_name}调试:")
        print("-" * 50)
        
        try:
            test_func()
        except Exception as e:
            print(f"❌ {test_name}调试异常: {e}")
            traceback.print_exc()
    
    print("\n" + "="*60)
    print("🎯 调试完成")
    print("="*60)

if __name__ == "__main__":
    main()
