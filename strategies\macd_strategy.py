"""
MACD策略实现
基于MACD指标的金叉死叉信号进行交易
"""
import asyncio
import logging
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，创建空装饰器
    def monitor_task(task_type):
        def decorator(func):
            return func
        return decorator

    class TaskType:
        OPENING_CHECK = "opening_check"
        PRICE_FETCH = "price_fetch"
        ORDER_EXECUTION = "order_execution"
        POSITION_CHECK = "position_check"

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class MACDSignal(Enum):
    """MACD信号"""
    GOLDEN_CROSS = "golden_cross"        # 金叉信号 (MACD线上穿信号线)
    DEATH_CROSS = "death_cross"          # 死叉信号 (MACD线下穿信号线)
    ABOVE_ZERO = "above_zero"            # MACD线在零轴上方
    BELOW_ZERO = "below_zero"            # MACD线在零轴下方
    DIVERGENCE_BULLISH = "divergence_bullish"  # 底背离
    DIVERGENCE_BEARISH = "divergence_bearish"  # 顶背离

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class MACDData:
    """MACD数据（包含可靠性评估）"""
    macd_line: float          # MACD线 (DIF)
    signal_line: float        # 信号线 (DEA)
    histogram: float          # 柱状图 (MACD - Signal)
    ema_fast: float          # 快线EMA
    ema_slow: float          # 慢线EMA
    signal: MACDSignal       # MACD信号
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 26  # 需要的数据点数
    calculation_period: int = 26  # 实际计算周期

class MACDConfig:
    """MACD策略配置"""
    
    def __init__(self):
        # MACD指标参数
        self.fast_period = 12            # 快线周期
        self.slow_period = 26            # 慢线周期
        self.signal_period = 9           # 信号线周期
        
        # 交易参数
        self.initial_margin = 500.0      # 初始保证金 (USDT)
        self.leverage = 50               # 杠杆倍数
        self.take_profit_percent = 1.5   # 止盈百分比
        self.stop_loss_percent = 5.6     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 50    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.0  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 3              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [5.0, 8.0, 12.0, 20.0]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 12.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.15     # 最大投入资金比例 (15%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # MACD信号过滤
        self.require_zero_line_filter = True  # 是否需要零轴过滤
        self.min_histogram_threshold = 0.1   # 最小柱状图阈值
        self.signal_confirmation_periods = 2  # 信号确认周期数
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 80.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 30             # 下单冷却时间(秒)
        self.position_check_interval = 10    # 持仓检查间隔(秒)
        
        # 交易对和交易所设置
        self.symbol = "BNB/USDT"            # 默认交易对
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 100              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证MACD参数
            if self.fast_period >= self.slow_period:
                logger.error("快线周期必须小于慢线周期")
                return False
            
            if self.signal_period <= 0:
                logger.error("信号线周期必须大于0")
                return False
            
            # 验证交易参数
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            if self.leverage <= 0 or self.leverage > 100:
                logger.error("杠杆倍数必须在1-100之间")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            logger.info("✅ MACD策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ MACD策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # MACD需要的最小数据点数 = 慢线周期 + 信号线周期
        return self.slow_period + self.signal_period
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # MACD参数
            "fast_period": self.fast_period,
            "slow_period": self.slow_period,
            "signal_period": self.signal_period,
            
            # 交易参数
            "initial_margin": self.initial_margin,
            "leverage": self.leverage,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "require_zero_line_filter": self.require_zero_line_filter,
            "min_histogram_threshold": self.min_histogram_threshold,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "symbol": self.symbol,
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""MACD策略配置:
        MACD参数: 快线={self.fast_period}, 慢线={self.slow_period}, 信号线={self.signal_period}
        交易参数: 保证金={self.initial_margin}, 杠杆={self.leverage}x
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易对: {self.symbol} ({self.exchange_name})"""

class MACDCalculator:
    """MACD计算器 - 核心计算逻辑"""

    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

    def calculate_ema(self, prices: List[float], period: int) -> List[float]:
        """
        计算指数移动平均线 (EMA)

        Args:
            prices: 价格列表
            period: 计算周期

        Returns:
            List[float]: EMA值列表
        """
        if len(prices) < period:
            return []

        # 转换为pandas Series进行计算
        price_series = pd.Series(prices)
        ema_values = price_series.ewm(span=period, adjust=False).mean()

        return ema_values.tolist()

    def calculate_macd(self, prices: List[float], min_periods: int = None) -> Optional[MACDData]:
        """
        计算MACD指标（支持部分数据计算）

        Args:
            prices: 价格列表
            min_periods: 最小计算周期，默认为slow_period

        Returns:
            Optional[MACDData]: MACD数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.slow_period, 20)  # 至少需要慢线周期的数据

        data_count = len(prices)
        required_count = self.slow_period + self.signal_period

        # 数据完全不足
        if data_count < min_periods:
            logger.warning(f"❌ 价格数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ MACD数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ MACD数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_prices = prices[-calculation_period:] if calculation_period < data_count else prices

            # 计算快线和慢线EMA
            ema_fast_list = self.calculate_ema(calc_prices, self.fast_period)
            ema_slow_list = self.calculate_ema(calc_prices, self.slow_period)

            if not ema_fast_list or not ema_slow_list:
                logger.error("❌ EMA计算失败")
                return None

            # 获取最新的EMA值
            ema_fast = ema_fast_list[-1]
            ema_slow = ema_slow_list[-1]

            # 计算MACD线 (DIF = 快线EMA - 慢线EMA)
            macd_line = ema_fast - ema_slow

            # 计算MACD线的EMA作为信号线 (DEA)
            # 需要先计算历史MACD值
            macd_history = []
            min_length = min(len(ema_fast_list), len(ema_slow_list))
            for i in range(min_length):
                macd_history.append(ema_fast_list[i] - ema_slow_list[i])

            # 计算信号线和信号线历史
            signal_history = []
            if len(macd_history) >= self.signal_period:
                signal_ema_list = self.calculate_ema(macd_history, self.signal_period)
                signal_line = signal_ema_list[-1] if signal_ema_list else macd_line
                signal_history = signal_ema_list
            else:
                # 数据不足时，使用简单移动平均
                signal_line = sum(macd_history[-self.signal_period:]) / len(macd_history[-self.signal_period:]) if macd_history else macd_line
                # 为历史数据创建简单的信号线历史
                for i in range(len(macd_history)):
                    if i + 1 >= self.signal_period:
                        avg_signal = sum(macd_history[max(0, i+1-self.signal_period):i+1]) / min(i+1, self.signal_period)
                        signal_history.append(avg_signal)

            # 计算柱状图 (MACD - Signal)
            histogram = macd_line - signal_line

            # 分析MACD信号
            signal = self._analyze_macd_signal(macd_line, signal_line, histogram, macd_history, signal_history)

            # 记录详细信息
            logger.info(f"📊 MACD计算完成: MACD={macd_line:.6f}, Signal={signal_line:.6f}, Histogram={histogram:.6f}")
            logger.info(f"📊 EMA快线={ema_fast:.4f}, EMA慢线={ema_slow:.4f}, 信号={signal.value}")
            logger.info(f"📊 数据统计: 使用{len(calc_prices)}个数据点, 可靠性={reliability:.2f}")

            return MACDData(
                macd_line=round(macd_line, 6),
                signal_line=round(signal_line, 6),
                histogram=round(histogram, 6),
                ema_fast=round(ema_fast, 4),
                ema_slow=round(ema_slow, 4),
                signal=signal,
                reliability=reliability,
                data_count=len(calc_prices),
                required_count=required_count,
                calculation_period=len(calc_prices)
            )

        except Exception as e:
            logger.error(f"计算MACD异常: {e}")
            return None

    def _analyze_macd_signal(self, macd_line: float, signal_line: float, histogram: float,
                           macd_history: List[float], signal_history: List[float] = None) -> MACDSignal:
        """
        分析MACD信号

        Args:
            macd_line: 当前MACD线值
            signal_line: 当前信号线值
            histogram: 当前柱状图值
            macd_history: MACD历史值列表

        Returns:
            MACDSignal: MACD信号类型
        """
        try:
            # 检查金叉死叉信号（需要历史数据进行趋势判断）
            if len(macd_history) >= 2:
                # 获取前一个MACD值
                prev_macd = macd_history[-2]

                # 计算前一个信号线值（如果有信号线历史）
                if signal_history and len(signal_history) >= 2:
                    prev_signal = signal_history[-2]
                else:
                    # 简化处理：假设信号线变化较小
                    prev_signal = signal_line

                # 判断金叉：MACD线从下方穿越信号线
                if prev_macd <= prev_signal and macd_line > signal_line:
                    # 确保穿越幅度足够大，避免噪音
                    cross_strength = abs(macd_line - signal_line)
                    if cross_strength > 0.0001:  # 最小穿越幅度
                        logger.info(f"🟢 MACD金叉信号: MACD({macd_line:.6f}) > Signal({signal_line:.6f}), 强度: {cross_strength:.6f}")
                        return MACDSignal.GOLDEN_CROSS

                # 判断死叉：MACD线从上方穿越信号线
                elif prev_macd >= prev_signal and macd_line < signal_line:
                    # 确保穿越幅度足够大，避免噪音
                    cross_strength = abs(macd_line - signal_line)
                    if cross_strength > 0.0001:  # 最小穿越幅度
                        logger.info(f"🔴 MACD死叉信号: MACD({macd_line:.6f}) < Signal({signal_line:.6f}), 强度: {cross_strength:.6f}")
                        return MACDSignal.DEATH_CROSS

            # 判断零轴位置
            if macd_line > 0:
                logger.debug(f"📈 MACD在零轴上方: {macd_line:.6f}")
                return MACDSignal.ABOVE_ZERO
            else:
                logger.debug(f"📉 MACD在零轴下方: {macd_line:.6f}")
                return MACDSignal.BELOW_ZERO

        except Exception as e:
            logger.error(f"分析MACD信号异常: {e}")
            return MACDSignal.BELOW_ZERO

    def calculate_macd_signal_direction(self, macd_data: MACDData) -> str:
        """
        基于MACD数据计算开仓方向

        Args:
            macd_data: MACD数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 金叉且MACD在零轴上方 - 强烈做多信号
            if macd_data.signal == MACDSignal.GOLDEN_CROSS and macd_data.macd_line > 0:
                logger.info("🚀 强烈做多信号: 金叉 + 零轴上方")
                return "long"

            # 金叉但MACD在零轴下方 - 谨慎做多信号
            elif macd_data.signal == MACDSignal.GOLDEN_CROSS and macd_data.macd_line <= 0:
                logger.info("📈 谨慎做多信号: 金叉 + 零轴下方")
                return "long"

            # 死叉且MACD在零轴下方 - 强烈做空信号
            elif macd_data.signal == MACDSignal.DEATH_CROSS and macd_data.macd_line < 0:
                logger.info("💥 强烈做空信号: 死叉 + 零轴下方")
                return "short"

            # 死叉但MACD在零轴上方 - 谨慎做空信号
            elif macd_data.signal == MACDSignal.DEATH_CROSS and macd_data.macd_line >= 0:
                logger.info("📉 谨慎做空信号: 死叉 + 零轴上方")
                return "short"

            # 其他情况保持观望
            else:
                logger.info(f"⏸️ 保持观望: 信号={macd_data.signal.value}, MACD={macd_data.macd_line:.6f}")
                return "hold"

        except Exception as e:
            logger.error(f"计算MACD开仓方向异常: {e}")
            return "hold"

    def detect_macd_crossover(self, prices: List[float], min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测MACD金叉死叉信号

        Args:
            prices: 价格列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(prices) < (self.slow_period + self.signal_period):
                return False, "hold", 0.0

            # 计算当前和前一个MACD数据
            current_macd = self.calculate_macd(prices, min_periods)
            if not current_macd:
                return False, "hold", 0.0

            # 计算前一个周期的MACD数据
            prev_prices = prices[:-1]
            if len(prev_prices) >= (self.slow_period + self.signal_period):
                prev_macd = self.calculate_macd(prev_prices, min_periods)
                if not prev_macd:
                    return False, "hold", 0.0

                # 检测金叉
                if (prev_macd.macd_line <= prev_macd.signal_line and
                    current_macd.macd_line > current_macd.signal_line):

                    cross_strength = abs(current_macd.macd_line - current_macd.signal_line)

                    # 根据零轴位置判断信号强度
                    if current_macd.macd_line > 0:
                        signal_type = "strong_long"  # 强烈做多
                        logger.info(f"🚀 强烈金叉信号: MACD在零轴上方, 强度: {cross_strength:.6f}")
                    else:
                        signal_type = "weak_long"    # 谨慎做多
                        logger.info(f"📈 谨慎金叉信号: MACD在零轴下方, 强度: {cross_strength:.6f}")

                    return True, signal_type, cross_strength

                # 检测死叉
                elif (prev_macd.macd_line >= prev_macd.signal_line and
                      current_macd.macd_line < current_macd.signal_line):

                    cross_strength = abs(current_macd.macd_line - current_macd.signal_line)

                    # 根据零轴位置判断信号强度
                    if current_macd.macd_line < 0:
                        signal_type = "strong_short"  # 强烈做空
                        logger.info(f"💥 强烈死叉信号: MACD在零轴下方, 强度: {cross_strength:.6f}")
                    else:
                        signal_type = "weak_short"    # 谨慎做空
                        logger.info(f"📉 谨慎死叉信号: MACD在零轴上方, 强度: {cross_strength:.6f}")

                    return True, signal_type, cross_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测MACD交叉信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, prices: List[float], signal_type: str, confirmation_periods: int = 2) -> bool:
        """
        确认MACD信号的有效性

        Args:
            prices: 价格列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if len(prices) < confirmation_periods + 1:
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                check_prices = prices[:-(i+1)] if i > 0 else prices
                has_signal, detected_type, strength = self.detect_macd_crossover(check_prices)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ MACD信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ MACD信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认MACD信号异常: {e}")
            return False

class MACDStrategy:
    """MACD策略主类"""

    def __init__(self, config: MACDConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = MACDCalculator(
            fast_period=config.fast_period,
            slow_period=config.slow_period,
            signal_period=config.signal_period
        )

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"🎯 MACD策略初始化完成: {config}")

    async def start(self):
        """启动MACD策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ MACD策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ MACD策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 MACD策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ MACD策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止MACD策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 MACD策略已停止")

        except Exception as e:
            logger.error(f"❌ MACD策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动MACD立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            prices = await self._get_price_data()
            if not prices or len(prices) < self.config.get_required_data_count():
                logger.warning("⚠️ 价格数据不足，无法进行MACD分析")
                return

            # 检测MACD金叉死叉信号
            has_signal, signal_type, signal_strength = self.calculator.detect_macd_crossover(prices)

            if not has_signal:
                logger.debug("未检测到MACD交叉信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(prices, signal_type, self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ MACD信号未确认: {signal_type}")
                    return

            # 零轴过滤（如果启用）
            if self.config.require_zero_line_filter:
                macd_data = self.calculator.calculate_macd(prices)
                if macd_data:
                    # 强信号才执行开仓
                    if signal_type in ["strong_long", "strong_short"]:
                        logger.info(f"🎯 检测到强烈MACD信号: {signal_type}, 强度: {signal_strength:.6f}")
                    elif signal_type in ["weak_long", "weak_short"]:
                        logger.info(f"⚠️ 检测到谨慎MACD信号: {signal_type}, 强度: {signal_strength:.6f}")
                        # 可以选择是否执行谨慎信号
                        if signal_strength < self.config.min_histogram_threshold:
                            logger.warning(f"信号强度不足，跳过开仓: {signal_strength:.6f} < {self.config.min_histogram_threshold}")
                            return

            # 转换信号类型为开仓方向
            if signal_type in ["strong_long", "weak_long"]:
                direction = "long"
            elif signal_type in ["strong_short", "weak_short"]:
                direction = "short"
            else:
                logger.warning(f"未知信号类型: {signal_type}")
                return

            logger.info(f"🚀 准备执行MACD开仓: 方向={direction}, 信号={signal_type}, 强度={signal_strength:.6f}")
            await self._execute_immediate_opening(direction, signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    async def _execute_immediate_opening(self, direction: str, signal_type: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [MACD立即开仓] 执行开仓: 方向={direction}, 信号={signal_type}, 强度={signal_strength:.6f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [MACD开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [MACD信号详情] 类型: {signal_type}, 强度: {signal_strength:.6f}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [MACD立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'signal_type': signal_type,
                    'signal_strength': signal_strength,
                    'direction': direction,
                    'price': current_price,
                    'quantity': quantity
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [MACD立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [MACD立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.95 if direction == "long" else entry_price * 1.05  # 默认5%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            # 这里可以根据Gate.io的API实现具体的止损逻辑
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动MACD持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    # 检查平仓机会
                    await self._check_close_position_opportunity()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_close_position_opportunity(self):
        """检查平仓机会"""
        try:
            # 检查是否有持仓
            if not await self._has_position():
                return

            # 获取当前持仓信息
            positions = await self._get_current_positions()
            if not positions:
                return

            current_position = positions[0]

            # 获取当前价格数据
            prices = await self._get_price_data()
            if not prices or len(prices) < self.config.get_required_data_count():
                logger.debug("价格数据不足，无法进行MACD平仓分析")
                return

            # 检测MACD平仓信号
            should_close, close_reason = await self._detect_macd_close_signal(
                prices, current_position
            )

            if should_close:
                logger.info(f"🎯 [MACD平仓] 检测到平仓信号: {close_reason}")
                await self._execute_close_position(current_position, close_reason)

        except Exception as e:
            logger.error(f"❌ 检查MACD平仓机会异常: {e}")

    async def _detect_macd_close_signal(self, prices: List[float], position) -> Tuple[bool, str]:
        """
        检测MACD平仓信号

        Args:
            prices: 价格列表
            position: 当前持仓信息

        Returns:
            Tuple[bool, str]: (是否应该平仓, 平仓原因)
        """
        try:
            # 检测MACD交叉信号
            has_signal, signal_type, signal_strength = self.calculator.detect_macd_crossover(prices)

            if not has_signal:
                return False, "无MACD交叉信号"

            # 获取持仓方向
            position_side = position.side.value if hasattr(position.side, 'value') else str(position.side)

            # 平仓逻辑：
            # 多仓 + 死叉信号 = 平仓
            # 空仓 + 金叉信号 = 平仓
            if position_side == "long" and signal_type in ["strong_short", "weak_short"]:
                return True, f"多仓遇到MACD死叉信号: {signal_type}, 强度: {signal_strength:.6f}"

            elif position_side == "short" and signal_type in ["strong_long", "weak_long"]:
                return True, f"空仓遇到MACD金叉信号: {signal_type}, 强度: {signal_strength:.6f}"

            # 检查信号强度是否足够
            if signal_strength < self.config.min_histogram_threshold:
                return False, f"MACD信号强度不足: {signal_strength:.6f} < {self.config.min_histogram_threshold}"

            return False, f"MACD信号与持仓方向不匹配: 持仓={position_side}, 信号={signal_type}"

        except Exception as e:
            logger.error(f"❌ 检测MACD平仓信号异常: {e}")
            return False, f"检测异常: {str(e)}"

    async def _execute_close_position(self, position, reason: str):
        """
        执行平仓操作

        Args:
            position: 持仓信息
            reason: 平仓原因
        """
        try:
            logger.info(f"🚀 [MACD平仓] 开始执行平仓: {reason}")

            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            # 确定平仓方向
            position_side = position.side.value if hasattr(position.side, 'value') else str(position.side)

            if position_side == "long":
                close_side = "sell"
            elif position_side == "short":
                close_side = "buy"
            else:
                logger.error(f"❌ 未知的持仓方向: {position_side}")
                return False

            # 执行市价平仓
            logger.info(f"📤 [MACD平仓] 执行市价平仓: {position_side}仓, 数量: {position.size}")

            # 确定平仓方向
            from exchanges.base_exchange import OrderSide
            if position_side == "long":
                close_side_enum = OrderSide.SELL
            else:
                close_side_enum = OrderSide.BUY

            # 调用真实的交易所API进行平仓
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.symbol,
                side=close_side_enum,
                amount=position.size,
                reduce_only=True  # 关键：标记为平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [MACD平仓] 平仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证平仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.symbol)
                if not positions_after or all(p.size == 0 for p in positions_after):
                    logger.info(f"✅ [MACD平仓] 平仓验证成功：持仓已清零")
                else:
                    logger.warning(f"⚠️ [MACD平仓] 平仓可能未完全成功，剩余持仓: {[p.size for p in positions_after]}")
            else:
                logger.error(f"❌ [MACD平仓] 平仓订单提交失败")
                return False

            # 重置策略状态
            self.current_position = None

            # 禁用立即开仓功能（平仓后重新启用）
            self.config.immediate_open_enabled = True
            logger.info("🔓 [MACD平仓] 平仓后重新启用立即开仓功能")

            return True

        except Exception as e:
            logger.error(f"❌ [MACD平仓] 执行平仓异常: {e}")
            return False

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_data(self) -> List[float]:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                return []

            # 这里实现价格数据获取逻辑
            return []  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return []

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [MACD市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用交易对: {self.config.symbol}")

            # 确定订单方向
            from exchanges.base_exchange import OrderSide
            if direction == "long":
                order_side = OrderSide.BUY
            elif direction == "short":
                order_side = OrderSide.SELL
            else:
                logger.error(f"❌ 未知的开仓方向: {direction}")
                return False

            # 调用交易所API下单
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.symbol,
                side=order_side,
                amount=quantity,
                reduce_only=False  # 开仓单，不是平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [MACD开仓] 开仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证开仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.symbol)
                if positions_after and any(p.size > 0 for p in positions_after):
                    logger.info(f"✅ [MACD开仓] 开仓验证成功：已建立持仓")
                    return True
                else:
                    logger.warning(f"⚠️ [MACD开仓] 开仓可能未成功，未检测到持仓")
                    return False
            else:
                logger.error(f"❌ [MACD开仓] 开仓订单提交失败")
                return False

        except Exception as e:
            logger.error(f"❌ MACD市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
        }
