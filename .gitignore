# 测试和演示文件
test_*.py
demo_*.py
*_test.py
*_demo.py
security_check_report.py
cleanup_test_files.py

# 备份目录
backup_test_files/

# 日志文件
*.log
logs/

# 配置文件（包含敏感信息）
user_config.json
.env

# Python缓存
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db