"""
布林带策略参数验证脚本
验证更新后的参数是否与ETH/USDT案例匹配
"""
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.config_manager import ConfigManager
from strategies.bollinger_strategy import BollingerBandConfig

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_section(title):
    """打印章节"""
    print(f"\n📊 {title}")
    print("-" * 40)

def analyze_eth_case():
    """分析ETH/USDT案例参数"""
    print_section("ETH/USDT案例参数分析")
    
    # 案例数据
    open_price = 3685.95
    stop_loss_price = 3479.95
    take_profit_price = 3710.95
    margin = 1.91
    
    print(f"📈 原始案例数据:")
    print(f"   开仓价格: {open_price} USDT")
    print(f"   止损价格: {stop_loss_price} USDT")
    print(f"   止盈价格: {take_profit_price} USDT")
    print(f"   保证金: {margin} USDT")
    
    # 计算百分比
    stop_loss_percent = (open_price - stop_loss_price) / open_price * 100
    take_profit_percent = (take_profit_price - open_price) / open_price * 100
    
    print(f"\n📊 计算得出的百分比:")
    print(f"   止损百分比: {stop_loss_percent:.2f}%")
    print(f"   止盈百分比: {take_profit_percent:.2f}%")
    print(f"   风险收益比: {stop_loss_percent/take_profit_percent:.2f}:1")
    
    # 分析风险收益比
    print(f"\n🔍 风险收益比分析:")
    if stop_loss_percent / take_profit_percent > 5:
        print(f"   ⚠️ 风险收益比过高 ({stop_loss_percent/take_profit_percent:.1f}:1)")
        print(f"   💡 建议调整:")
        print(f"      • 降低止损百分比到 3-4%")
        print(f"      • 提高止盈百分比到 1.5-2%")
        print(f"      • 目标风险收益比: 2-3:1")
    
    return stop_loss_percent, take_profit_percent

def verify_config_manager():
    """验证配置管理器的默认值"""
    print_section("配置管理器默认值验证")
    
    config_manager = ConfigManager()
    default_config = config_manager.default_config
    
    print(f"📋 布林带策略默认配置:")
    bb_configs = {k: v for k, v in default_config.items() if k.startswith('bb_')}
    
    for key, value in bb_configs.items():
        display_name = key.replace('bb_', '').replace('_', ' ').title()
        if 'percent' in key:
            print(f"   {display_name}: {value}%")
        elif 'ratio' in key:
            print(f"   {display_name}: {value*100:.1f}%")
        else:
            print(f"   {display_name}: {value}")
    
    # 验证关键参数
    print(f"\n✅ 关键参数验证:")
    
    expected_values = {
        'bb_max_total_loss_percent': 12.0,
        'bb_max_investment_ratio': 0.15,
        'bb_take_profit_percent': 1.5,
        'bb_stop_loss_percent': 5.6
    }
    
    all_correct = True
    for key, expected in expected_values.items():
        actual = default_config.get(key)
        status = "✅" if actual == expected else "❌"
        print(f"   {status} {key}: 期望 {expected}, 实际 {actual}")
        if actual != expected:
            all_correct = False
    
    return all_correct

def verify_strategy_class():
    """验证策略类的默认值"""
    print_section("策略类默认值验证")
    
    config = BollingerBandConfig()
    
    print(f"📋 BollingerBandConfig默认值:")
    print(f"   最大总亏损百分比: {config.max_total_loss_percent}%")
    print(f"   最大投入资金比例: {config.max_investment_ratio*100:.1f}%")
    print(f"   止盈百分比: {config.take_profit_percent}%")
    print(f"   止损百分比: {config.stop_loss_percent}%")
    print(f"   最大加仓次数: {config.max_add_count}")
    print(f"   触发距离(点数): {config.trigger_distance_points}")
    print(f"   触发距离(百分比): {config.trigger_distance_percent}%")
    
    # 验证关键参数
    print(f"\n✅ 关键参数验证:")
    
    expected_values = {
        'max_total_loss_percent': 12.0,
        'max_investment_ratio': 0.15,
        'take_profit_percent': 1.5,
        'stop_loss_percent': 5.6
    }
    
    all_correct = True
    for key, expected in expected_values.items():
        actual = getattr(config, key)
        status = "✅" if actual == expected else "❌"
        print(f"   {status} {key}: 期望 {expected}, 实际 {actual}")
        if actual != expected:
            all_correct = False
    
    return all_correct

def test_parameter_validation():
    """测试参数验证功能"""
    print_section("参数验证功能测试")
    
    config_manager = ConfigManager()
    
    # 测试有效配置
    valid_config = {
        "bb_period": 20,
        "bb_std_dev": 2.0,
        "bb_trigger_distance_points": 50,
        "bb_trigger_distance_percent": 1.0,
        "bb_max_add_count": 3,
        "bb_add_intervals": [5.0, 8.0, 12.0],
        "bb_max_total_loss_percent": 12.0,  # 更新后的值
        "bb_max_investment_ratio": 0.15,    # 更新后的值
        "bb_take_profit_percent": 1.5,      # 更新后的值
        "bb_stop_loss_percent": 5.6,        # 更新后的值
        "bb_add_position_types": ["equal", "half"],
        "bb_allowed_signals": ["three_lines_down", "price_touch_lower"]
    }
    
    is_valid, message = config_manager.validate_bollinger_strategy_params(valid_config)
    print(f"✅ 有效配置验证: {is_valid}")
    if not is_valid:
        print(f"   错误信息: {message}")
    
    # 测试边界值
    print(f"\n🔍 边界值测试:")
    
    boundary_tests = [
        ("最大总亏损 5%", {"bb_max_total_loss_percent": 5.0}),
        ("最大总亏损 50%", {"bb_max_total_loss_percent": 50.0}),
        ("最大投入比例 5%", {"bb_max_investment_ratio": 0.05}),
        ("最大投入比例 100%", {"bb_max_investment_ratio": 1.0}),
        ("止盈 0.5%", {"bb_take_profit_percent": 0.5}),
        ("止盈 20%", {"bb_take_profit_percent": 20.0}),
        ("止损 1%", {"bb_stop_loss_percent": 1.0}),
        ("止损 30%", {"bb_stop_loss_percent": 30.0}),
    ]
    
    for test_name, test_config in boundary_tests:
        test_cfg = valid_config.copy()
        test_cfg.update(test_config)
        is_valid, _ = config_manager.validate_bollinger_strategy_params(test_cfg)
        status = "✅" if is_valid else "❌"
        print(f"   {status} {test_name}")

def simulate_eth_scenario():
    """模拟ETH案例场景"""
    print_section("ETH案例场景模拟")
    
    from strategies.bollinger_strategy import BollingerBandStrategy
    
    # 使用更新后的配置
    config = BollingerBandConfig()
    strategy = BollingerBandStrategy(config)
    
    print(f"🎯 模拟ETH交易场景:")
    print(f"   使用更新后的默认配置")
    print(f"   最大总亏损: {config.max_total_loss_percent}%")
    print(f"   最大投入比例: {config.max_investment_ratio*100:.1f}%")
    print(f"   止盈目标: {config.take_profit_percent}%")
    print(f"   止损设置: {config.stop_loss_percent}%")
    
    # 模拟加仓场景
    initial_price = 3685.95
    initial_margin = 1.91
    prices = [3630.0, 3580.0, 3530.0, 3480.0]
    
    print(f"\n📉 价格下跌模拟:")
    print(f"   初始开仓: {initial_price} USDT, 保证金: {initial_margin} USDT")
    
    for i, price in enumerate(prices):
        record = strategy.add_position(price, initial_margin, f"第{i+1}次加仓")
        if record:
            print(f"   第{i+1}次加仓: {price} USDT, 平均成本: {record.new_avg_cost:.2f} USDT")
            
            # 检查风险
            is_risk_exceeded, loss_percent = strategy.check_total_risk(price)
            risk_status = "🚨超限" if is_risk_exceeded else "✅正常"
            print(f"      风险状态: {loss_percent:.2f}% {risk_status}")
    
    # 模拟反弹盈利
    if strategy.add_records:
        print(f"\n📈 反弹盈利模拟:")
        total_quantity = sum(record.quantity for record in strategy.add_records)
        total_investment = strategy.add_records[-1].total_margin
        avg_cost = strategy.add_records[-1].new_avg_cost
        
        rebound_prices = [3500, 3550, 3600, 3650]
        for price in rebound_prices:
            current_value = total_quantity * price
            profit = current_value - total_investment
            profit_percent = profit / total_investment * 100
            
            if profit_percent >= config.take_profit_percent:
                print(f"   🎯 价格 {price} USDT: 盈利 {profit_percent:.2f}% - 达到止盈目标!")
                break
            else:
                print(f"   📊 价格 {price} USDT: 盈利 {profit_percent:.2f}% - 继续持有")

def compare_old_vs_new():
    """对比新旧参数"""
    print_section("新旧参数对比")
    
    old_params = {
        "最大总亏损": (15.0, 12.0),
        "最大投入比例": (20.0, 15.0),
        "止盈百分比": (2.0, 1.5),
        "止损百分比": (5.0, 5.6)
    }
    
    print(f"📊 参数对比表:")
    print(f"{'参数名称':<12} {'旧值':<8} {'新值':<8} {'变化':<12} {'说明'}")
    print("-" * 60)
    
    for param_name, (old_val, new_val) in old_params.items():
        change = new_val - old_val
        change_str = f"{change:+.1f}"
        if "比例" in param_name:
            old_display = f"{old_val:.1f}%"
            new_display = f"{new_val:.1f}%"
        else:
            old_display = f"{old_val:.1f}%"
            new_display = f"{new_val:.1f}%"
        
        if param_name == "最大总亏损":
            explanation = "更保守"
        elif param_name == "最大投入比例":
            explanation = "更保守"
        elif param_name == "止盈百分比":
            explanation = "更现实"
        elif param_name == "止损百分比":
            explanation = "匹配案例"
        else:
            explanation = ""
        
        print(f"{param_name:<12} {old_display:<8} {new_display:<8} {change_str:<12} {explanation}")
    
    print(f"\n💡 参数调整说明:")
    print(f"   • 最大总亏损: 从15%降至12% - 更保守的风险控制")
    print(f"   • 最大投入比例: 从20%降至15% - 减少资金集中度")
    print(f"   • 止盈百分比: 从2%降至1.5% - 更现实的盈利目标")
    print(f"   • 止损百分比: 从5%升至5.6% - 与ETH案例保持一致")

def main():
    """主函数"""
    print_header("布林带策略参数验证")
    
    print(f"🎯 验证目标:")
    print(f"   • 确认参数已正确更新为基于ETH案例的优化值")
    print(f"   • 验证配置管理器和策略类的一致性")
    print(f"   • 测试参数验证功能的正确性")
    print(f"   • 模拟ETH案例场景验证参数合理性")
    
    # 执行验证
    stop_loss_pct, take_profit_pct = analyze_eth_case()
    
    config_ok = verify_config_manager()
    strategy_ok = verify_strategy_class()
    
    test_parameter_validation()
    simulate_eth_scenario()
    compare_old_vs_new()
    
    # 总结
    print_header("验证结果总结")
    
    print(f"✅ 验证结果:")
    print(f"   • ETH案例分析: 完成")
    print(f"   • 配置管理器: {'✅通过' if config_ok else '❌失败'}")
    print(f"   • 策略类配置: {'✅通过' if strategy_ok else '❌失败'}")
    print(f"   • 参数验证功能: ✅正常")
    print(f"   • 场景模拟: ✅完成")
    
    if config_ok and strategy_ok:
        print(f"\n🎉 所有验证通过！")
        print(f"   布林带策略参数已成功更新为基于ETH案例的优化值")
        print(f"   系统配置与策略类保持一致")
        print(f"   参数设置更加合理和实用")
    else:
        print(f"\n⚠️ 发现问题，需要进一步检查配置")
    
    print(f"\n💡 优化效果:")
    print(f"   • 风险控制更加保守和安全")
    print(f"   • 盈利目标更加现实可达")
    print(f"   • 止损设置与实际案例匹配")
    print(f"   • 整体参数更适合实际交易")

if __name__ == "__main__":
    main()
