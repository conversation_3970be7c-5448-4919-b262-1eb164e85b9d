#!/usr/bin/env python3
"""
立即开仓功能执行问题诊断工具
"""

import sys
import os
import re
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_log_file(log_file_path: str, hours_back: int = 2) -> Dict[str, Any]:
    """分析日志文件"""
    try:
        if not os.path.exists(log_file_path):
            return {"error": f"日志文件不存在: {log_file_path}"}
        
        # 计算时间范围
        now = datetime.now()
        start_time = now - timedelta(hours=hours_back)
        
        # 读取日志
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 过滤时间范围内的日志
        filtered_lines = []
        for line in lines:
            # 提取时间戳
            time_match = re.match(r'(\d{2}:\d{2}:\d{2})', line)
            if time_match:
                time_str = time_match.group(1)
                try:
                    log_time = datetime.strptime(f"{now.strftime('%Y-%m-%d')} {time_str}", '%Y-%m-%d %H:%M:%S')
                    if log_time >= start_time:
                        filtered_lines.append(line.strip())
                except:
                    continue
        
        return {"lines": filtered_lines, "count": len(filtered_lines)}
        
    except Exception as e:
        return {"error": f"分析日志文件失败: {e}"}

def extract_immediate_open_logs(log_lines: List[str]) -> Dict[str, List[str]]:
    """提取立即开仓相关日志"""
    categories = {
        "monitor_startup": [],      # 监控启动
        "timer_status": [],         # 定时器状态
        "check_execution": [],      # 检查执行
        "signal_analysis": [],      # 信号分析
        "position_check": [],       # 持仓检查
        "balance_validation": [],   # 余额验证
        "opening_execution": [],    # 开仓执行
        "errors": [],              # 错误信息
        "warnings": []             # 警告信息
    }
    
    # 关键词匹配模式
    patterns = {
        "monitor_startup": [
            r"立即开仓.*监控.*启动",
            r"立即开仓.*定时器.*启动",
            r"立即开仓.*检测间隔"
        ],
        "timer_status": [
            r"AsyncTimer.*set_interval",
            r"定时器.*启动",
            r"定时器.*停止",
            r"定时器ID"
        ],
        "check_execution": [
            r"\[立即开仓\].*开始检查",
            r"\[立即开仓\].*检查开仓机会"
        ],
        "signal_analysis": [
            r"\[立即开仓\].*%B指标",
            r"\[立即开仓\].*信号",
            r"\[立即开仓\].*做多",
            r"\[立即开仓\].*做空",
            r"\[立即开仓\].*中性区域"
        ],
        "position_check": [
            r"\[立即开仓\].*持仓",
            r"\[立即开仓\].*无持仓",
            r"\[立即开仓\].*检查.*持仓状态"
        ],
        "balance_validation": [
            r"\[余额验证\]",
            r"账户余额",
            r"余额.*充足",
            r"余额.*不足"
        ],
        "opening_execution": [
            r"\[立即开仓\].*执行.*开仓",
            r"开始执行立即开仓",
            r"\[立即开仓\].*开仓成功",
            r"\[立即开仓\].*开仓失败"
        ],
        "errors": [
            r"ERROR.*立即开仓",
            r"❌.*立即开仓",
            r"异常.*立即开仓",
            r"失败.*立即开仓"
        ],
        "warnings": [
            r"WARNING.*立即开仓",
            r"⚠️.*立即开仓",
            r"警告.*立即开仓"
        ]
    }
    
    # 分类日志
    for line in log_lines:
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                if re.search(pattern, line, re.IGNORECASE):
                    categories[category].append(line)
                    break
    
    return categories

def analyze_bb_indicators(log_lines: List[str]) -> Dict[str, Any]:
    """分析布林带指标"""
    bb_data = {
        "percent_b_values": [],
        "band_data": [],
        "current_prices": [],
        "signal_triggers": []
    }
    
    for line in log_lines:
        # 提取%B指标
        percent_b_match = re.search(r'%B指标[=:]?\s*(\d+\.?\d*)%', line)
        if percent_b_match:
            bb_data["percent_b_values"].append({
                "value": float(percent_b_match.group(1)),
                "line": line
            })
        
        # 提取布林带数据
        band_match = re.search(r'上轨[=:]?(\d+\.?\d+).*中轨[=:]?(\d+\.?\d+).*下轨[=:]?(\d+\.?\d+)', line)
        if band_match:
            bb_data["band_data"].append({
                "upper": float(band_match.group(1)),
                "middle": float(band_match.group(2)),
                "lower": float(band_match.group(3)),
                "line": line
            })
        
        # 提取当前价格
        price_match = re.search(r'当前价格[=:]?\s*(\d+\.?\d+)', line)
        if price_match:
            bb_data["current_prices"].append({
                "price": float(price_match.group(1)),
                "line": line
            })
        
        # 提取信号触发
        if re.search(r'触发.*信号', line):
            bb_data["signal_triggers"].append(line)
    
    return bb_data

def diagnose_execution_flow(categories: Dict[str, List[str]]) -> Dict[str, Any]:
    """诊断执行流程"""
    diagnosis = {
        "monitor_started": len(categories["monitor_startup"]) > 0,
        "timer_working": len(categories["timer_status"]) > 0,
        "checks_running": len(categories["check_execution"]) > 0,
        "signals_analyzed": len(categories["signal_analysis"]) > 0,
        "positions_checked": len(categories["position_check"]) > 0,
        "balance_validated": len(categories["balance_validation"]) > 0,
        "execution_attempted": len(categories["opening_execution"]) > 0,
        "has_errors": len(categories["errors"]) > 0,
        "has_warnings": len(categories["warnings"]) > 0,
        "issues": []
    }
    
    # 诊断问题
    if not diagnosis["monitor_started"]:
        diagnosis["issues"].append("❌ 立即开仓监控未启动")
    
    if not diagnosis["timer_working"]:
        diagnosis["issues"].append("❌ 定时器未正常工作")
    
    if not diagnosis["checks_running"]:
        diagnosis["issues"].append("❌ 检查流程未执行")
    
    if not diagnosis["signals_analyzed"]:
        diagnosis["issues"].append("❌ 信号分析未进行")
    
    if not diagnosis["positions_checked"]:
        diagnosis["issues"].append("❌ 持仓状态未检查")
    
    if diagnosis["has_errors"]:
        diagnosis["issues"].append("🚨 发现错误日志")
    
    if diagnosis["has_warnings"]:
        diagnosis["issues"].append("⚠️ 发现警告日志")
    
    return diagnosis

def generate_report(log_analysis: Dict[str, Any], categories: Dict[str, List[str]], 
                   bb_data: Dict[str, Any], diagnosis: Dict[str, Any]) -> str:
    """生成诊断报告"""
    report = []
    report.append("🔍 立即开仓功能执行问题诊断报告")
    report.append("=" * 60)
    
    # 基本信息
    if "error" in log_analysis:
        report.append(f"❌ 日志分析失败: {log_analysis['error']}")
        return "\n".join(report)
    
    report.append(f"📊 分析时间范围: 最近2小时")
    report.append(f"📝 日志条目总数: {log_analysis['count']}")
    report.append("")
    
    # 执行流程诊断
    report.append("🔧 执行流程诊断:")
    report.append(f"   监控启动: {'✅' if diagnosis['monitor_started'] else '❌'}")
    report.append(f"   定时器工作: {'✅' if diagnosis['timer_working'] else '❌'}")
    report.append(f"   检查执行: {'✅' if diagnosis['checks_running'] else '❌'}")
    report.append(f"   信号分析: {'✅' if diagnosis['signals_analyzed'] else '❌'}")
    report.append(f"   持仓检查: {'✅' if diagnosis['positions_checked'] else '❌'}")
    report.append(f"   余额验证: {'✅' if diagnosis['balance_validated'] else '❌'}")
    report.append(f"   执行尝试: {'✅' if diagnosis['execution_attempted'] else '❌'}")
    report.append("")
    
    # 发现的问题
    if diagnosis["issues"]:
        report.append("🚨 发现的问题:")
        for issue in diagnosis["issues"]:
            report.append(f"   {issue}")
        report.append("")
    
    # 布林带指标分析
    if bb_data["percent_b_values"]:
        report.append("📊 布林带指标分析:")
        latest_percent_b = bb_data["percent_b_values"][-1]["value"]
        report.append(f"   最新%B指标: {latest_percent_b:.1f}%")
        
        if latest_percent_b < 30:
            report.append("   🔵 应触发做多信号 (%B < 30%)")
        elif latest_percent_b > 70:
            report.append("   🔴 应触发做空信号 (%B > 70%)")
        else:
            report.append("   ⚪ 处于中性区域 (30% ≤ %B ≤ 70%)，不开仓")
        report.append("")
    
    # 最新价格和布林带
    if bb_data["current_prices"] and bb_data["band_data"]:
        latest_price = bb_data["current_prices"][-1]["price"]
        latest_bands = bb_data["band_data"][-1]
        report.append("💰 最新市场数据:")
        report.append(f"   当前价格: {latest_price:.4f}")
        report.append(f"   上轨: {latest_bands['upper']:.4f}")
        report.append(f"   中轨: {latest_bands['middle']:.4f}")
        report.append(f"   下轨: {latest_bands['lower']:.4f}")
        report.append("")
    
    # 关键日志摘要
    report.append("📋 关键日志摘要:")
    
    # 监控启动日志
    if categories["monitor_startup"]:
        report.append("   🚀 监控启动:")
        for log in categories["monitor_startup"][-3:]:  # 最近3条
            report.append(f"      {log}")
    
    # 信号分析日志
    if categories["signal_analysis"]:
        report.append("   📊 信号分析:")
        for log in categories["signal_analysis"][-3:]:  # 最近3条
            report.append(f"      {log}")
    
    # 错误日志
    if categories["errors"]:
        report.append("   ❌ 错误信息:")
        for log in categories["errors"][-3:]:  # 最近3条
            report.append(f"      {log}")
    
    # 警告日志
    if categories["warnings"]:
        report.append("   ⚠️ 警告信息:")
        for log in categories["warnings"][-3:]:  # 最近3条
            report.append(f"      {log}")
    
    report.append("")
    
    # 修复建议
    report.append("💡 修复建议:")
    if not diagnosis["monitor_started"]:
        report.append("   1. 检查立即开仓功能是否正确勾选")
        report.append("   2. 重新启动布林带策略")
        report.append("   3. 检查异步定时器是否正常工作")
    
    if not diagnosis["signals_analyzed"]:
        report.append("   1. 检查布林带数据是否充足")
        report.append("   2. 验证%B指标计算是否正确")
        report.append("   3. 确认价格数据获取是否正常")
    
    if diagnosis["has_errors"]:
        report.append("   1. 查看具体错误信息并修复")
        report.append("   2. 检查交易所连接状态")
        report.append("   3. 验证账户权限和余额")
    
    if bb_data["percent_b_values"]:
        latest_percent_b = bb_data["percent_b_values"][-1]["value"]
        if 30 <= latest_percent_b <= 70:
            report.append("   1. 当前%B指标处于中性区域，等待信号")
            report.append("   2. 可以手动调整开仓条件阈值")
            report.append("   3. 或等待价格进入触发区域")
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🔍 开始诊断立即开仓功能执行问题...")
    
    # 查找日志文件
    log_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".log") and ("trading" in file.lower() or "main" in file.lower()):
                log_files.append(os.path.join(root, file))
    
    if not log_files:
        print("❌ 未找到日志文件")
        return
    
    print(f"📁 找到 {len(log_files)} 个日志文件")
    
    # 分析每个日志文件
    all_lines = []
    for log_file in log_files:
        print(f"📖 分析日志文件: {log_file}")
        analysis = analyze_log_file(log_file)
        if "lines" in analysis:
            all_lines.extend(analysis["lines"])
    
    if not all_lines:
        print("❌ 未找到相关日志内容")
        return
    
    print(f"📊 总共分析 {len(all_lines)} 条日志")
    
    # 提取立即开仓相关日志
    categories = extract_immediate_open_logs(all_lines)
    
    # 分析布林带指标
    bb_data = analyze_bb_indicators(all_lines)
    
    # 诊断执行流程
    diagnosis = diagnose_execution_flow(categories)
    
    # 生成报告
    report = generate_report(
        {"count": len(all_lines)}, 
        categories, 
        bb_data, 
        diagnosis
    )
    
    print("\n" + report)
    
    # 保存报告到文件
    with open("immediate_open_diagnosis_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 诊断报告已保存到: immediate_open_diagnosis_report.txt")

if __name__ == "__main__":
    main()
