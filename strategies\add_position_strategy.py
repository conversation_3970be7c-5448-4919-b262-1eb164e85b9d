"""
智能加仓策略模块
当价格接近强平价时执行加仓操作
支持等量和半量加仓，严格遵守异步编程原则
"""
import asyncio
import time
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from enum import Enum
import logging

from exchanges import BaseExchange, OrderSide, PositionSide, OrderInfo, PositionInfo
from monitoring.position_monitor import PositionAlert, PositionRisk, AsyncPositionMonitor

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"     # 等量加仓
    HALF = "half"       # 半量加仓
    CUSTOM = "custom"   # 自定义比例

@dataclass
class AddPositionConfig:
    """加仓配置"""
    symbol: str
    add_type: AddPositionType
    initial_margin: float      # 初始保证金
    max_add_times: int = 3     # 最大加仓次数
    custom_ratio: float = 0.5  # 自定义加仓比例（仅当add_type为CUSTOM时使用）
    min_interval: int = 300    # 最小加仓间隔（秒）
    risk_threshold: float = 0.1 # 风险阈值，低于此值才允许加仓

@dataclass
class AddPositionResult:
    """加仓结果"""
    success: bool
    order_info: Optional[OrderInfo]
    add_amount: float
    add_margin: float
    reason: str
    timestamp: int
    new_total_size: float
    new_avg_price: float

@dataclass
class AddPositionRecord:
    """加仓记录"""
    symbol: str
    timestamp: int
    add_amount: float
    add_margin: float
    price: float
    total_times: int

class AsyncAddPositionStrategy:
    """
    异步智能加仓策略
    基于持仓风险和价格预警执行加仓操作
    """
    
    def __init__(self, exchange: BaseExchange, position_monitor: AsyncPositionMonitor):
        """
        初始化加仓策略
        
        Args:
            exchange: 交易所实例
            position_monitor: 持仓监控器
        """
        self.exchange = exchange
        self.position_monitor = position_monitor
        self._add_records: Dict[str, List[AddPositionRecord]] = {}
        self._last_add_time: Dict[str, int] = {}
        self._is_active = False
        
        # 注册预警回调
        self.position_monitor.add_alert_callback(self._handle_position_alert)
        
        logger.info("智能加仓策略初始化完成")
    
    async def _handle_position_alert(self, alert: PositionAlert) -> None:
        """
        处理持仓预警
        
        Args:
            alert: 持仓预警信息
        """
        try:
            if not self._is_active:
                return
            
            if alert.alert_type == 'add_position_trigger' and alert.should_add_position:
                logger.info(f"收到加仓触发信号: {alert.message}")
                
                # 从配置管理器获取配置信息
                # 如果没有配置管理器，使用合理的默认值
                config = self._get_add_position_config(alert.symbol)
                
                # 执行加仓
                result = await self.execute_add_position(config, alert.risk_info)
                
                if result.success:
                    logger.info(f"自动加仓成功: {result.reason}")
                else:
                    logger.warning(f"自动加仓失败: {result.reason}")
                    
        except Exception as e:
            logger.error(f"处理持仓预警异常: {e}")

    def _get_add_position_config(self, symbol: str) -> AddPositionConfig:
        """
        获取加仓配置

        Args:
            symbol: 交易对符号

        Returns:
            AddPositionConfig: 加仓配置
        """
        # 这里可以从配置管理器获取配置
        # 暂时使用合理的默认配置
        return AddPositionConfig(
            symbol=symbol,
            add_type=AddPositionType.HALF,
            initial_margin=100.0,  # 应该从初始开仓记录获取
            max_add_times=3,
            min_interval=300,
            risk_threshold=0.1
        )
    
    async def calculate_add_amount(self, config: AddPositionConfig, current_position: PositionInfo) -> tuple[float, float]:
        """
        计算加仓数量和保证金
        
        Args:
            config: 加仓配置
            current_position: 当前持仓信息
            
        Returns:
            tuple[float, float]: (加仓数量, 加仓保证金)
        """
        try:
            if config.add_type == AddPositionType.EQUAL:
                # 等量加仓：使用与初始相同的保证金
                add_margin = config.initial_margin
            elif config.add_type == AddPositionType.HALF:
                # 半量加仓：使用初始保证金的一半
                add_margin = config.initial_margin * 0.5
            elif config.add_type == AddPositionType.CUSTOM:
                # 自定义比例加仓
                add_margin = config.initial_margin * config.custom_ratio
            else:
                raise ValueError(f"不支持的加仓类型: {config.add_type}")
            
            # 根据当前价格和杠杆计算加仓数量
            current_price = await self.exchange.get_current_price(config.symbol)
            if current_price <= 0:
                raise ValueError("无法获取当前价格")
            
            # 计算加仓数量（考虑杠杆）
            add_amount = (add_margin * current_position.leverage) / current_price
            
            logger.debug(f"计算加仓数量: 保证金={add_margin}, 杠杆={current_position.leverage}, "
                        f"价格={current_price}, 数量={add_amount}")
            
            return add_amount, add_margin
            
        except Exception as e:
            logger.error(f"计算加仓数量异常: {e}")
            return 0.0, 0.0
    
    async def check_add_position_conditions(self, config: AddPositionConfig, risk: PositionRisk) -> tuple[bool, str]:
        """
        检查加仓条件
        
        Args:
            config: 加仓配置
            risk: 持仓风险信息
            
        Returns:
            tuple[bool, str]: (是否满足条件, 原因)
        """
        try:
            # 检查加仓次数限制
            symbol_records = self._add_records.get(config.symbol, [])
            if len(symbol_records) >= config.max_add_times:
                return False, f"已达到最大加仓次数限制: {config.max_add_times}"
            
            # 检查加仓间隔
            last_add_time = self._last_add_time.get(config.symbol, 0)
            current_time = int(time.time())
            if current_time - last_add_time < config.min_interval:
                remaining = config.min_interval - (current_time - last_add_time)
                return False, f"加仓间隔不足，还需等待{remaining}秒"
            
            # 检查风险阈值
            if abs(risk.distance_percentage) < config.risk_threshold:
                return False, f"风险过高，距离强平价仅{abs(risk.distance_percentage)*100:.2f}%"
            
            # 检查账户余额
            balances = await self.exchange.get_account_balance()
            usdt_balance = balances.get('USDT', 0.0)
            
            # 估算所需保证金
            _, required_margin = await self.calculate_add_amount(config, None)
            if usdt_balance < required_margin:
                return False, f"账户余额不足，需要{required_margin}USDT，当前{usdt_balance}USDT"
            
            return True, "满足加仓条件"
            
        except Exception as e:
            logger.error(f"检查加仓条件异常: {e}")
            return False, f"检查条件异常: {str(e)}"
    
    async def execute_add_position(self, config: AddPositionConfig, risk: PositionRisk) -> AddPositionResult:
        """
        执行加仓操作
        
        Args:
            config: 加仓配置
            risk: 持仓风险信息
            
        Returns:
            AddPositionResult: 加仓结果
        """
        try:
            # 检查加仓条件
            can_add, reason = await self.check_add_position_conditions(config, risk)
            if not can_add:
                return AddPositionResult(
                    success=False,
                    order_info=None,
                    add_amount=0.0,
                    add_margin=0.0,
                    reason=reason,
                    timestamp=int(time.time() * 1000),
                    new_total_size=0.0,
                    new_avg_price=0.0
                )
            
            # 获取当前持仓
            positions = await self.exchange.get_positions(config.symbol)
            if not positions:
                return AddPositionResult(
                    success=False,
                    order_info=None,
                    add_amount=0.0,
                    add_margin=0.0,
                    reason="没有找到当前持仓",
                    timestamp=int(time.time() * 1000),
                    new_total_size=0.0,
                    new_avg_price=0.0
                )
            
            current_position = positions[0]
            
            # 计算加仓数量
            add_amount, add_margin = await self.calculate_add_amount(config, current_position)
            if add_amount <= 0:
                return AddPositionResult(
                    success=False,
                    order_info=None,
                    add_amount=0.0,
                    add_margin=0.0,
                    reason="计算加仓数量失败",
                    timestamp=int(time.time() * 1000),
                    new_total_size=0.0,
                    new_avg_price=0.0
                )
            
            # 确定加仓方向
            if current_position.side == PositionSide.LONG:
                order_side = OrderSide.BUY
                position_side = PositionSide.LONG
            else:
                order_side = OrderSide.SELL
                position_side = PositionSide.SHORT
            
            # 执行加仓订单
            logger.info(f"执行加仓: {config.symbol}, 方向: {position_side.value}, 数量: {add_amount}")
            
            order_info = await self.exchange.place_market_order(
                symbol=config.symbol,
                side=order_side,
                amount=add_amount,
                position_side=position_side
            )
            
            # 获取当前价格
            current_price = await self.exchange.get_current_price(config.symbol)
            
            # 记录加仓信息
            record = AddPositionRecord(
                symbol=config.symbol,
                timestamp=int(time.time()),
                add_amount=add_amount,
                add_margin=add_margin,
                price=current_price,
                total_times=len(self._add_records.get(config.symbol, [])) + 1
            )
            
            if config.symbol not in self._add_records:
                self._add_records[config.symbol] = []
            self._add_records[config.symbol].append(record)
            self._last_add_time[config.symbol] = int(time.time())
            
            # 计算新的持仓信息（估算）
            new_total_size = current_position.size + add_amount
            new_avg_price = ((current_position.size * current_position.entry_price) + 
                           (add_amount * current_price)) / new_total_size if new_total_size > 0 else 0.0
            
            logger.info(f"加仓成功: 订单ID={order_info.order_id}, 数量={add_amount}, 价格={current_price}")
            
            return AddPositionResult(
                success=True,
                order_info=order_info,
                add_amount=add_amount,
                add_margin=add_margin,
                reason=f"成功加仓{add_amount}，价格{current_price}",
                timestamp=int(time.time() * 1000),
                new_total_size=new_total_size,
                new_avg_price=new_avg_price
            )
            
        except Exception as e:
            logger.error(f"执行加仓异常: {e}")
            return AddPositionResult(
                success=False,
                order_info=None,
                add_amount=0.0,
                add_margin=0.0,
                reason=f"加仓异常: {str(e)}",
                timestamp=int(time.time() * 1000),
                new_total_size=0.0,
                new_avg_price=0.0
            )
    
    async def manual_add_position(self, config: AddPositionConfig) -> AddPositionResult:
        """
        手动触发加仓
        
        Args:
            config: 加仓配置
            
        Returns:
            AddPositionResult: 加仓结果
        """
        try:
            # 获取当前风险信息
            risk = self.position_monitor.get_position_risk(config.symbol)
            if not risk:
                # 强制更新风险信息
                risk = await self.position_monitor.force_update_position(config.symbol, 0.5)
                if not risk:
                    return AddPositionResult(
                        success=False,
                        order_info=None,
                        add_amount=0.0,
                        add_margin=0.0,
                        reason="无法获取持仓风险信息",
                        timestamp=int(time.time() * 1000),
                        new_total_size=0.0,
                        new_avg_price=0.0
                    )
            
            return await self.execute_add_position(config, risk)
            
        except Exception as e:
            logger.error(f"手动加仓异常: {e}")
            return AddPositionResult(
                success=False,
                order_info=None,
                add_amount=0.0,
                add_margin=0.0,
                reason=f"手动加仓异常: {str(e)}",
                timestamp=int(time.time() * 1000),
                new_total_size=0.0,
                new_avg_price=0.0
            )
    
    def activate(self) -> None:
        """激活加仓策略"""
        self._is_active = True
        logger.info("智能加仓策略已激活")
    
    def deactivate(self) -> None:
        """停用加仓策略"""
        self._is_active = False
        logger.info("智能加仓策略已停用")
    
    def is_active(self) -> bool:
        """检查策略是否激活"""
        return self._is_active
    
    def get_add_records(self, symbol: str) -> List[AddPositionRecord]:
        """获取加仓记录"""
        return self._add_records.get(symbol, []).copy()
    
    def get_add_times(self, symbol: str) -> int:
        """获取加仓次数"""
        return len(self._add_records.get(symbol, []))
    
    def reset_add_records(self, symbol: str) -> None:
        """重置加仓记录"""
        if symbol in self._add_records:
            del self._add_records[symbol]
        if symbol in self._last_add_time:
            del self._last_add_time[symbol]
        logger.info(f"已重置{symbol}的加仓记录")
    
    def get_total_add_margin(self, symbol: str) -> float:
        """获取总加仓保证金"""
        records = self._add_records.get(symbol, [])
        return sum(record.add_margin for record in records)
