"""
异步MACD指标计算模块
严格遵守异步编程原则，禁止多线程
"""
import asyncio
import numpy as np
import pandas as pd
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
import logging

from exchanges import BaseExchange, KlineData

logger = logging.getLogger(__name__)

@dataclass
class MACDResult:
    """MACD计算结果"""
    dif: float          # DIF线（快线-慢线）
    dea: float          # DEA线（信号线）
    histogram: float    # 柱状线（DIF-DEA）
    timestamp: int      # 时间戳

@dataclass
class MACDSignal:
    """MACD交易信号"""
    signal_type: str    # 'buy' 或 'sell' 或 'hold'
    strength: float     # 信号强度 (0-1)
    reason: str         # 信号原因
    timestamp: int      # 时间戳
    macd_data: MACDResult  # MACD数据

class AsyncMACDCalculator:
    """
    异步MACD指标计算器
    使用纯异步方式计算MACD指标，避免阻塞事件循环
    """
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        """
        初始化MACD计算器
        
        Args:
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        
        # 历史数据缓存
        self._price_history: List[float] = []
        self._macd_history: List[MACDResult] = []
        
        # EMA计算所需的前值
        self._fast_ema_prev: Optional[float] = None
        self._slow_ema_prev: Optional[float] = None
        self._signal_ema_prev: Optional[float] = None
        
        logger.info(f"MACD计算器初始化: 快线={fast_period}, 慢线={slow_period}, 信号线={signal_period}")
    
    async def _calculate_ema_async(self, prices: List[float], period: int, prev_ema: Optional[float] = None) -> float:
        """
        异步计算指数移动平均线
        
        Args:
            prices: 价格列表
            period: 周期
            prev_ema: 前一个EMA值
            
        Returns:
            float: EMA值
        """
        if not prices:
            return 0.0
        
        # 使用异步方式避免阻塞
        await asyncio.sleep(0)  # 让出控制权
        
        if len(prices) < period:
            # 数据不足，使用简单移动平均
            return sum(prices) / len(prices)
        
        # 计算平滑系数
        multiplier = 2.0 / (period + 1)
        
        if prev_ema is None:
            # 第一次计算，使用SMA作为初始值
            sma = sum(prices[-period:]) / period
            return sma
        else:
            # 使用前一个EMA值计算
            current_price = prices[-1]
            ema = (current_price * multiplier) + (prev_ema * (1 - multiplier))
            return ema
    
    async def calculate_macd_from_klines(self, klines: List[KlineData]) -> Optional[MACDResult]:
        """
        从K线数据计算MACD
        
        Args:
            klines: K线数据列表
            
        Returns:
            Optional[MACDResult]: MACD结果，数据不足时返回None
        """
        if len(klines) < self.slow_period:
            logger.warning(f"K线数据不足，需要至少{self.slow_period}根K线")
            return None
        
        # 提取收盘价
        prices = [kline.close for kline in klines]
        
        return await self.calculate_macd_from_prices(prices, klines[-1].timestamp)
    
    async def calculate_macd_from_prices(self, prices: List[float], timestamp: int) -> Optional[MACDResult]:
        """
        从价格列表计算MACD
        
        Args:
            prices: 价格列表
            timestamp: 时间戳
            
        Returns:
            Optional[MACDResult]: MACD结果
        """
        if len(prices) < self.slow_period:
            logger.warning(f"价格数据不足，需要至少{self.slow_period}个价格点")
            return None
        
        try:
            # 异步计算快线EMA
            fast_ema = await self._calculate_ema_async(prices, self.fast_period, self._fast_ema_prev)
            
            # 异步计算慢线EMA
            slow_ema = await self._calculate_ema_async(prices, self.slow_period, self._slow_ema_prev)
            
            # 计算DIF线
            dif = fast_ema - slow_ema
            
            # 更新DIF历史用于计算DEA
            if not hasattr(self, '_dif_history'):
                self._dif_history = []
            self._dif_history.append(dif)
            
            # 保持历史数据长度
            if len(self._dif_history) > self.signal_period * 2:
                self._dif_history = self._dif_history[-self.signal_period * 2:]
            
            # 异步计算DEA线（DIF的EMA）
            dea = await self._calculate_ema_async(self._dif_history, self.signal_period, self._signal_ema_prev)
            
            # 计算柱状线
            histogram = dif - dea
            
            # 更新前值
            self._fast_ema_prev = fast_ema
            self._slow_ema_prev = slow_ema
            self._signal_ema_prev = dea
            
            # 创建结果
            result = MACDResult(
                dif=dif,
                dea=dea,
                histogram=histogram,
                timestamp=timestamp
            )
            
            # 更新历史
            self._macd_history.append(result)
            if len(self._macd_history) > 100:  # 保持最近100个结果
                self._macd_history = self._macd_history[-100:]
            
            logger.debug(f"MACD计算完成: DIF={dif:.6f}, DEA={dea:.6f}, Histogram={histogram:.6f}")
            
            return result
            
        except Exception as e:
            logger.error(f"MACD计算异常: {e}")
            return None
    
    async def get_trend_signal(self, exchange: BaseExchange, symbol: str, timeframe: str = "30m") -> Optional[MACDSignal]:
        """
        获取MACD趋势信号
        
        Args:
            exchange: 交易所实例
            symbol: 交易对
            timeframe: 时间周期
            
        Returns:
            Optional[MACDSignal]: 交易信号
        """
        try:
            # 获取K线数据
            klines = await exchange.get_klines(symbol, timeframe, limit=50)
            if not klines:
                logger.error("无法获取K线数据")
                return None
            
            # 计算当前MACD
            current_macd = await self.calculate_macd_from_klines(klines)
            if not current_macd:
                logger.error("无法计算MACD")
                return None
            
            # 需要至少2个MACD值来判断趋势
            if len(self._macd_history) < 2:
                return MACDSignal(
                    signal_type='hold',
                    strength=0.0,
                    reason='数据不足，无法判断趋势',
                    timestamp=current_macd.timestamp,
                    macd_data=current_macd
                )
            
            # 获取前一个MACD值
            prev_macd = self._macd_history[-2]
            
            # 分析趋势
            signal = await self._analyze_macd_trend(current_macd, prev_macd)
            
            return signal
            
        except Exception as e:
            logger.error(f"获取MACD趋势信号异常: {e}")
            return None
    
    async def _analyze_macd_trend(self, current: MACDResult, previous: MACDResult) -> MACDSignal:
        """
        分析MACD趋势
        
        Args:
            current: 当前MACD值
            previous: 前一个MACD值
            
        Returns:
            MACDSignal: 交易信号
        """
        await asyncio.sleep(0)  # 让出控制权
        
        # 柱状线变化
        histogram_change = current.histogram - previous.histogram
        
        # DIF和DEA的关系
        dif_above_dea = current.dif > current.dea
        
        # 判断信号
        if dif_above_dea and histogram_change > 0:
            # DIF在DEA上方且柱状线增长 - 看涨信号
            strength = min(abs(histogram_change) * 10, 1.0)  # 强度基于变化幅度
            return MACDSignal(
                signal_type='buy',
                strength=strength,
                reason=f'MACD看涨：DIF({current.dif:.6f}) > DEA({current.dea:.6f})，柱状线增长({histogram_change:.6f})',
                timestamp=current.timestamp,
                macd_data=current
            )
        elif not dif_above_dea and histogram_change < 0:
            # DIF在DEA下方且柱状线递减 - 看跌信号
            strength = min(abs(histogram_change) * 10, 1.0)
            return MACDSignal(
                signal_type='sell',
                strength=strength,
                reason=f'MACD看跌：DIF({current.dif:.6f}) < DEA({current.dea:.6f})，柱状线递减({histogram_change:.6f})',
                timestamp=current.timestamp,
                macd_data=current
            )
        else:
            # 无明确信号
            return MACDSignal(
                signal_type='hold',
                strength=0.0,
                reason=f'MACD无明确信号：DIF={current.dif:.6f}, DEA={current.dea:.6f}, 柱状线变化={histogram_change:.6f}',
                timestamp=current.timestamp,
                macd_data=current
            )
    
    def get_macd_history(self, limit: int = 10) -> List[MACDResult]:
        """获取MACD历史数据"""
        return self._macd_history[-limit:] if self._macd_history else []
    
    def reset(self) -> None:
        """重置计算器状态"""
        self._price_history.clear()
        self._macd_history.clear()
        self._fast_ema_prev = None
        self._slow_ema_prev = None
        self._signal_ema_prev = None
        if hasattr(self, '_dif_history'):
            self._dif_history.clear()
        logger.info("MACD计算器状态已重置")
