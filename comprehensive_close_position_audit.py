#!/usr/bin/env python3
"""
全面平仓问题检测工具
检测所有交易策略中的平仓执行问题
"""

import os
import re
import ast
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class ClosePositionIssue:
    """平仓问题"""
    severity: str  # 'critical', 'high', 'medium', 'low'
    issue_type: str
    file_path: str
    line_number: int
    method_name: str
    code_snippet: str
    description: str
    fix_suggestion: str
    risk_level: str

class ComprehensiveClosePositionAuditor:
    """全面平仓问题审计器"""
    
    def __init__(self):
        self.issues = []
        self.strategy_files = []
        self.exchange_files = []
        
    def run_comprehensive_audit(self) -> Dict[str, Any]:
        """运行全面审计"""
        print("🚨 启动全面平仓问题检测")
        print("=" * 80)
        
        # 步骤1：收集所有策略和交易所文件
        self._collect_target_files()
        
        # 步骤2：检测OKX交易所posSide参数问题
        self._detect_pos_side_issues()
        
        # 步骤3：检测平仓验证逻辑缺陷
        self._detect_close_verification_issues()
        
        # 步骤4：检测reduce_only参数缺失
        self._detect_reduce_only_issues()
        
        # 步骤5：检测订单成交验证不足
        self._detect_order_fill_verification_issues()
        
        # 步骤6：检测持仓状态交叉验证缺失
        self._detect_position_cross_verification_issues()
        
        # 步骤7：生成全面报告
        return self._generate_comprehensive_report()
    
    def _collect_target_files(self):
        """收集目标文件"""
        print("🔍 收集目标文件...")
        
        # 策略文件
        strategy_dir = "strategies"
        if os.path.exists(strategy_dir):
            for file in os.listdir(strategy_dir):
                if file.endswith('.py') and not file.startswith('__'):
                    self.strategy_files.append(os.path.join(strategy_dir, file))
        
        # 交易所文件
        exchange_dir = "exchanges"
        if os.path.exists(exchange_dir):
            for file in os.listdir(exchange_dir):
                if file.endswith('.py') and not file.startswith('__'):
                    self.exchange_files.append(os.path.join(exchange_dir, file))
        
        print(f"   策略文件: {len(self.strategy_files)} 个")
        print(f"   交易所文件: {len(self.exchange_files)} 个")
    
    def _detect_pos_side_issues(self):
        """检测posSide参数问题"""
        print("\n🔍 检测1: OKX交易所posSide参数问题")
        
        # 检查所有交易所文件
        for file_path in self.exchange_files:
            self._check_pos_side_in_file(file_path)
    
    def _check_pos_side_in_file(self, file_path: str):
        """检查单个文件中的posSide问题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                # 检查错误的posSide逻辑
                if '"posSide"' in line and 'if side ==' in line:
                    # 检查是否是错误的逻辑
                    if 'OrderSide.BUY' in line and 'else' in line:
                        self.issues.append(ClosePositionIssue(
                            severity='critical',
                            issue_type='pos_side_logic_error',
                            file_path=file_path,
                            line_number=i,
                            method_name=self._extract_method_name(lines, i),
                            code_snippet=line.strip(),
                            description='posSide参数设置逻辑错误，会导致平仓变成开仓',
                            fix_suggestion='区分开仓和平仓，使用reduce_only参数',
                            risk_level='极高'
                        ))
                        print(f"   ❌ 发现posSide错误: {file_path}:{i}")
        
        except Exception as e:
            print(f"   ⚠️ 检查文件失败: {file_path} - {e}")
    
    def _detect_close_verification_issues(self):
        """检测平仓验证逻辑缺陷"""
        print("\n🔍 检测2: 平仓验证逻辑缺陷")
        
        for file_path in self.strategy_files:
            self._check_close_verification_in_file(file_path)
    
    def _check_close_verification_in_file(self, file_path: str):
        """检查单个文件中的平仓验证问题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 查找平仓相关方法
            close_methods = [
                'close_position', 'execute_close_position', 'close_all_positions',
                'exit_position', 'liquidate_position', '_close_position'
            ]
            
            for method in close_methods:
                if f'def {method}' in content or f'async def {method}' in content:
                    method_start = self._find_method_start(lines, method)
                    if method_start:
                        method_lines = self._extract_method_lines(lines, method_start)
                        self._analyze_close_verification_logic(file_path, method, method_start, method_lines)
        
        except Exception as e:
            print(f"   ⚠️ 检查文件失败: {file_path} - {e}")
    
    def _analyze_close_verification_logic(self, file_path: str, method_name: str, 
                                        start_line: int, method_lines: List[str]):
        """分析平仓验证逻辑"""
        method_content = '\n'.join(method_lines)
        
        # 检查是否只验证订单ID
        if 'order_result.order_id' in method_content:
            # 检查是否有充分的验证
            has_status_check = any(keyword in method_content for keyword in [
                'order_result.status', 'get_order_status', 'filled', 'verify'
            ])
            
            has_position_check = any(keyword in method_content for keyword in [
                'get_positions', 'position', 'verify_position'
            ])
            
            if not has_status_check or not has_position_check:
                severity = 'critical' if not has_status_check else 'high'
                
                self.issues.append(ClosePositionIssue(
                    severity=severity,
                    issue_type='insufficient_close_verification',
                    file_path=file_path,
                    line_number=start_line,
                    method_name=method_name,
                    code_snippet=f"方法: {method_name}",
                    description='平仓验证逻辑不充分，可能导致误判平仓成功',
                    fix_suggestion='增加订单状态验证和持仓状态二次确认',
                    risk_level='高' if severity == 'critical' else '中'
                ))
                print(f"   ❌ 发现验证不足: {file_path}:{method_name}")
    
    def _detect_reduce_only_issues(self):
        """检测reduce_only参数缺失"""
        print("\n🔍 检测3: reduce_only参数缺失")
        
        for file_path in self.strategy_files:
            self._check_reduce_only_in_file(file_path)
    
    def _check_reduce_only_in_file(self, file_path: str):
        """检查单个文件中的reduce_only参数问题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                # 查找place_order调用
                if 'place_order(' in line or 'place_market_order(' in line:
                    # 检查上下文是否是平仓操作
                    context_start = max(0, i-10)
                    context_end = min(len(lines), i+5)
                    context = '\n'.join(lines[context_start:context_end])
                    
                    # 判断是否是平仓上下文
                    is_close_context = any(keyword in context.lower() for keyword in [
                        'close', 'exit', 'liquidate', '平仓', 'close_side'
                    ])
                    
                    if is_close_context and 'reduce_only' not in line:
                        self.issues.append(ClosePositionIssue(
                            severity='high',
                            issue_type='missing_reduce_only',
                            file_path=file_path,
                            line_number=i,
                            method_name=self._extract_method_name(lines, i),
                            code_snippet=line.strip(),
                            description='平仓调用缺少reduce_only=True参数',
                            fix_suggestion='在place_order调用中添加reduce_only=True',
                            risk_level='高'
                        ))
                        print(f"   ❌ 缺少reduce_only: {file_path}:{i}")
        
        except Exception as e:
            print(f"   ⚠️ 检查文件失败: {file_path} - {e}")
    
    def _detect_order_fill_verification_issues(self):
        """检测订单成交验证不足"""
        print("\n🔍 检测4: 订单成交验证不足")
        
        for file_path in self.strategy_files:
            self._check_order_fill_verification_in_file(file_path)
    
    def _check_order_fill_verification_in_file(self, file_path: str):
        """检查订单成交验证"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找下单后的处理逻辑
            if 'place_order' in content:
                # 检查是否有订单状态验证
                has_status_verification = any(keyword in content for keyword in [
                    'get_order_status', 'order.status', 'filled', 'partially_filled'
                ])
                
                if not has_status_verification:
                    self.issues.append(ClosePositionIssue(
                        severity='medium',
                        issue_type='missing_order_fill_verification',
                        file_path=file_path,
                        line_number=0,
                        method_name='整个文件',
                        code_snippet='place_order调用',
                        description='缺少订单成交状态验证',
                        fix_suggestion='增加订单状态查询和成交验证',
                        risk_level='中'
                    ))
                    print(f"   ⚠️ 缺少成交验证: {file_path}")
        
        except Exception as e:
            print(f"   ⚠️ 检查文件失败: {file_path} - {e}")
    
    def _detect_position_cross_verification_issues(self):
        """检测持仓状态交叉验证缺失"""
        print("\n🔍 检测5: 持仓状态交叉验证缺失")
        
        for file_path in self.strategy_files:
            self._check_position_cross_verification_in_file(file_path)
    
    def _check_position_cross_verification_in_file(self, file_path: str):
        """检查持仓状态交叉验证"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找平仓方法
            if any(keyword in content for keyword in ['close_position', 'exit_position', '平仓']):
                # 检查是否有持仓状态验证
                has_position_verification = any(keyword in content for keyword in [
                    'get_positions', 'verify_position', 'check_position'
                ])
                
                if not has_position_verification:
                    self.issues.append(ClosePositionIssue(
                        severity='medium',
                        issue_type='missing_position_cross_verification',
                        file_path=file_path,
                        line_number=0,
                        method_name='平仓相关方法',
                        code_snippet='平仓操作',
                        description='缺少平仓后持仓状态交叉验证',
                        fix_suggestion='增加平仓后持仓状态二次确认',
                        risk_level='中'
                    ))
                    print(f"   ⚠️ 缺少持仓验证: {file_path}")
        
        except Exception as e:
            print(f"   ⚠️ 检查文件失败: {file_path} - {e}")
    
    def _extract_method_name(self, lines: List[str], line_number: int) -> str:
        """提取方法名"""
        for i in range(line_number - 1, max(0, line_number - 20), -1):
            line = lines[i].strip()
            if line.startswith('def ') or line.startswith('async def '):
                return line.split('(')[0].replace('def ', '').replace('async ', '').strip()
        return 'unknown'
    
    def _find_method_start(self, lines: List[str], method_name: str) -> int:
        """查找方法开始行"""
        for i, line in enumerate(lines):
            if f'def {method_name}(' in line or f'async def {method_name}(' in line:
                return i + 1
        return 0
    
    def _extract_method_lines(self, lines: List[str], start_line: int) -> List[str]:
        """提取方法的所有行"""
        method_lines = []
        indent_level = None
        
        for i in range(start_line - 1, len(lines)):
            line = lines[i]
            
            if indent_level is None and line.strip():
                indent_level = len(line) - len(line.lstrip())
            
            if line.strip() and len(line) - len(line.lstrip()) <= indent_level and i > start_line - 1:
                if not line.strip().startswith('#'):  # 不是注释
                    break
            
            method_lines.append(line)
            
            if len(method_lines) > 100:  # 防止过长
                break
        
        return method_lines
    
    def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成全面报告"""
        print("\n" + "=" * 80)
        print("📋 全面平仓问题检测报告")
        print("=" * 80)
        
        # 按严重程度分类
        critical_issues = [i for i in self.issues if i.severity == 'critical']
        high_issues = [i for i in self.issues if i.severity == 'high']
        medium_issues = [i for i in self.issues if i.severity == 'medium']
        
        # 按文件分组
        issues_by_file = {}
        for issue in self.issues:
            file_name = os.path.basename(issue.file_path)
            if file_name not in issues_by_file:
                issues_by_file[file_name] = []
            issues_by_file[file_name].append(issue)
        
        print(f"🎯 检测总结:")
        print(f"   🚨 关键问题: {len(critical_issues)} 个")
        print(f"   ⚠️  高级问题: {len(high_issues)} 个")
        print(f"   💡 中等问题: {len(medium_issues)} 个")
        print(f"   📊 总计: {len(self.issues)} 个问题")
        print(f"   📁 涉及文件: {len(issues_by_file)} 个")
        
        # 详细报告
        if critical_issues:
            print(f"\n🚨 关键问题详情:")
            for i, issue in enumerate(critical_issues, 1):
                print(f"   {i}. [{issue.issue_type}] {os.path.basename(issue.file_path)}:{issue.line_number}")
                print(f"      方法: {issue.method_name}")
                print(f"      问题: {issue.description}")
                print(f"      风险: {issue.risk_level}")
                print(f"      修复: {issue.fix_suggestion}")
                print()
        
        if high_issues:
            print(f"\n⚠️  高级问题详情:")
            for i, issue in enumerate(high_issues, 1):
                print(f"   {i}. [{issue.issue_type}] {os.path.basename(issue.file_path)}:{issue.line_number}")
                print(f"      方法: {issue.method_name}")
                print(f"      问题: {issue.description}")
                print(f"      修复: {issue.fix_suggestion}")
        
        # 按文件汇总
        print(f"\n📁 按文件汇总:")
        for file_name, file_issues in issues_by_file.items():
            critical_count = len([i for i in file_issues if i.severity == 'critical'])
            high_count = len([i for i in file_issues if i.severity == 'high'])
            medium_count = len([i for i in file_issues if i.severity == 'medium'])
            
            print(f"   📄 {file_name}: {len(file_issues)} 个问题")
            if critical_count > 0:
                print(f"      🚨 关键: {critical_count}")
            if high_count > 0:
                print(f"      ⚠️  高级: {high_count}")
            if medium_count > 0:
                print(f"      💡 中等: {medium_count}")
        
        return {
            'total_issues': len(self.issues),
            'critical_issues': len(critical_issues),
            'high_issues': len(high_issues),
            'medium_issues': len(medium_issues),
            'issues_by_file': issues_by_file,
            'all_issues': self.issues
        }

def main():
    """主函数"""
    try:
        auditor = ComprehensiveClosePositionAuditor()
        report = auditor.run_comprehensive_audit()
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        with open(f'close_position_audit_{timestamp}.json', 'w', encoding='utf-8') as f:
            import json
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n✅ 检测完成，报告已保存")
        
        if report['critical_issues'] > 0:
            print("\n❌ 发现关键问题，需要立即修复")
            return False
        elif report['high_issues'] > 0:
            print("\n⚠️  发现高级问题，建议尽快修复")
            return True
        else:
            print("\n✅ 未发现关键问题")
            return True
        
    except Exception as e:
        print(f"❌ 检测异常: {e}")
        return False

if __name__ == "__main__":
    main()
