#!/usr/bin/env python3
"""
BIT系统BUG修复验证测试
验证异步资源管理和并发安全修复的正确性
"""

import asyncio
import sys
import os
import time
import logging
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_async_resource_management():
    """测试异步资源管理修复"""
    logger.info("🧪 测试异步资源管理修复...")
    
    try:
        from exchanges.base_exchange import BaseExchange
        
        # 创建模拟的交易所实例
        class MockExchange(BaseExchange):
            async def connect(self) -> bool:
                self._is_connected = True
                return True
            
            async def disconnect(self) -> None:
                self._is_connected = False
            
            async def get_account_info(self) -> Dict[str, Any]:
                return {"balance": 1000}
            
            async def get_current_price(self, symbol: str) -> float:
                return 50000.0
            
            async def place_order(self, symbol: str, side: str, amount: float, price: float = None, order_type: str = "market") -> Dict[str, Any]:
                return {"order_id": "test_order", "status": "filled"}
            
            async def get_positions(self, symbol: str = None) -> List[Dict[str, Any]]:
                return []
            
            async def cancel_order(self, symbol: str, order_id: str) -> bool:
                return True
        
        # 测试资源清理
        exchange = MockExchange("test_key", "test_secret", sandbox=True)
        
        # 使用异步上下文管理器
        async with exchange:
            logger.info("✅ 交易所连接成功")
            
            # 模拟添加后台任务
            async def dummy_task():
                await asyncio.sleep(0.1)
                return "task_completed"
            
            task = asyncio.create_task(dummy_task())
            exchange.add_background_task(task)
            
            logger.info("✅ 后台任务添加成功")
            
            # 等待一小段时间
            await asyncio.sleep(0.05)
        
        # 验证资源已清理
        assert not exchange.is_connected, "连接状态应该为False"
        assert len(exchange._background_tasks) == 0, "后台任务应该已清理"
        
        logger.info("✅ 异步资源管理测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 异步资源管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_concurrent_safety():
    """测试并发安全修复"""
    logger.info("🧪 测试并发安全修复...")
    
    try:
        from core.trading_controller import AsyncTradingController, TradingState
        from config import TradingConfig
        
        # 创建交易控制器
        config = TradingConfig()
        config.SANDBOX = True
        
        controller = AsyncTradingController(config)
        
        # 测试状态更新的并发安全
        async def update_state_worker(worker_id: int):
            """并发状态更新工作器"""
            for i in range(5):
                await controller.update_trading_state(TradingState.ANALYZING)
                await asyncio.sleep(0.001)
                await controller.update_trading_state(TradingState.MONITORING)
                await asyncio.sleep(0.001)
        
        # 启动多个并发工作器
        tasks = []
        for i in range(3):
            task = asyncio.create_task(update_state_worker(i))
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks)
        
        # 验证最终状态一致性
        final_state = await controller.get_trading_state()
        logger.info(f"✅ 最终状态: {final_state}")
        
        logger.info("✅ 并发安全测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 并发安全测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_syntax_check():
    """测试语法检查"""
    logger.info("🧪 测试语法检查...")
    
    try:
        # 测试导入关键模块
        from exchanges.base_exchange import BaseExchange
        from exchanges.okx_exchange import OKXExchange
        from exchanges.gateio_exchange import GateIOExchange
        from core.trading_controller import AsyncTradingController
        from monitoring.position_monitor import AsyncPositionMonitor
        from monitoring.price_monitor import AsyncPriceMonitor
        from strategies.bollinger_strategy import BollingerStrategy
        
        logger.info("✅ 所有关键模块导入成功")
        
        # 测试基础类实例化
        from config import TradingConfig
        config = TradingConfig()
        config.SANDBOX = True
        
        controller = AsyncTradingController(config)
        logger.info("✅ 交易控制器创建成功")
        
        logger.info("✅ 语法检查测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 语法检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_all_tests():
    """运行所有BUG修复验证测试"""
    logger.info("🚀 开始BIT系统BUG修复验证测试")
    logger.info("=" * 60)
    
    test_results = []
    
    # 测试1: 语法检查
    result1 = await test_syntax_check()
    test_results.append(("语法检查", result1))
    
    # 测试2: 异步资源管理
    result2 = await test_async_resource_management()
    test_results.append(("异步资源管理", result2))
    
    # 测试3: 并发安全
    result3 = await test_concurrent_safety()
    test_results.append(("并发安全", result3))
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 测试结果汇总:")
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed_count += 1
    
    total_tests = len(test_results)
    success_rate = (passed_count / total_tests) * 100
    
    logger.info("=" * 60)
    logger.info(f"🎯 测试总结: {passed_count}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if passed_count == total_tests:
        logger.info("🎉 所有BUG修复验证测试通过！")
        logger.info("✅ 异步资源管理修复成功")
        logger.info("✅ 并发安全问题修复成功")
        logger.info("🚀 系统稳定性显著提升")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(run_all_tests())
        exit_code = 0 if result else 1
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试运行异常: {e}")
        sys.exit(1)
