"""
成交量平衡指标(OBV)策略实现
基于OBV指标的成交量和价格关系分析进行交易
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，创建空装饰器
    def monitor_task(task_type):
        def decorator(func):
            return func
        return decorator

    class TaskType:
        OPENING_CHECK = "opening_check"
        PRICE_FETCH = "price_fetch"
        ORDER_EXECUTION = "order_execution"
        POSITION_CHECK = "position_check"

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class OBVSignal(Enum):
    """OBV信号"""
    BULLISH_TREND = "bullish_trend"        # 多头趋势 (OBV上升 + 价格上涨)
    BEARISH_TREND = "bearish_trend"        # 空头趋势 (OBV下降 + 价格下跌)
    BULLISH_DIVERGENCE = "bullish_divergence"  # 看涨背离 (价格下跌但OBV上升)
    BEARISH_DIVERGENCE = "bearish_divergence"  # 看跌背离 (价格上涨但OBV下降)
    VOLUME_BREAKOUT = "volume_breakout"    # 成交量突破
    VOLUME_CONFIRMATION = "volume_confirmation"  # 成交量确认
    TREND_WEAKENING = "trend_weakening"    # 趋势减弱
    TREND_STRENGTHENING = "trend_strengthening"  # 趋势加强
    NEUTRAL = "neutral"                    # 中性
    NO_SIGNAL = "no_signal"               # 无信号

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class OBVData:
    """OBV数据（包含可靠性评估）"""
    obv_value: float          # OBV值
    obv_change: float         # OBV变化量
    obv_trend: str           # OBV趋势 ("上升", "下降", "震荡")
    price_trend: str         # 价格趋势 ("上升", "下降", "震荡")
    signal: OBVSignal        # OBV信号
    divergence_strength: float  # 背离强度 (-1.0 到 1.0)
    trend_strength: float    # 趋势强度 (0.0 到 1.0)
    volume_ratio: float      # 成交量比率
    confirmation_periods: int  # 确认周期数
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 20  # 需要的数据点数
    calculation_period: int = 20  # 实际计算周期

class OBVConfig:
    """成交量平衡指标策略配置"""
    
    def __init__(self):
        # OBV指标参数
        self.period = 20                 # OBV计算周期
        self.signal_confirmation_periods = 2  # 信号确认周期
        self.trend_filter_threshold = 0.3    # 趋势过滤阈值
        self.divergence_threshold = 0.5      # 背离检测阈值
        self.volume_threshold = 1.2          # 成交量阈值倍数
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "LTC/USDT"  # 自定义交易对
        self.custom_leverage = 25        # 自定义杠杆倍数
        self.initial_margin = 600.0      # 初始保证金 (USDT)
        self.take_profit_percent = 1.8   # 止盈百分比
        self.stop_loss_percent = 3.5     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 40    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 0.7  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 4              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [1.5, 2.8, 4.5, 6.8]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 10.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.18     # 最大投入资金比例 (18%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # OBV信号过滤
        self.enable_trend_filter = True      # 启用趋势过滤
        self.enable_divergence_detection = True  # 启用背离检测
        self.enable_volume_confirmation = True   # 启用成交量确认
        self.min_trend_strength = 0.4       # 最小趋势强度
        self.min_divergence_strength = 0.3  # 最小背离强度
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 60.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 25             # 下单冷却时间(秒)
        self.position_check_interval = 12    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 100              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证OBV参数
            if self.period <= 0 or self.period > 100:
                logger.error("OBV周期必须在1-100之间")
                return False
            
            if not (0.1 <= self.trend_filter_threshold <= 1.0):
                logger.error("趋势过滤阈值必须在0.1-1.0之间")
                return False
            
            if not (0.1 <= self.divergence_threshold <= 1.0):
                logger.error("背离检测阈值必须在0.1-1.0之间")
                return False
            
            if not (1.0 <= self.volume_threshold <= 5.0):
                logger.error("成交量阈值倍数必须在1.0-5.0之间")
                return False
            
            if self.signal_confirmation_periods < 1 or self.signal_confirmation_periods > 10:
                logger.error("信号确认周期必须在1-10之间")
                return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            # 验证信号过滤参数
            if not (0.0 <= self.min_trend_strength <= 1.0):
                logger.error("最小趋势强度必须在0.0-1.0之间")
                return False
            
            if not (0.0 <= self.min_divergence_strength <= 1.0):
                logger.error("最小背离强度必须在0.0-1.0之间")
                return False
            
            logger.info("✅ OBV策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ OBV策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # OBV需要的最小数据点数
        return max(self.period, 20) + 10  # 额外10个点用于趋势分析
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # OBV参数
            "period": self.period,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            "trend_filter_threshold": self.trend_filter_threshold,
            "divergence_threshold": self.divergence_threshold,
            "volume_threshold": self.volume_threshold,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_trend_filter": self.enable_trend_filter,
            "enable_divergence_detection": self.enable_divergence_detection,
            "enable_volume_confirmation": self.enable_volume_confirmation,
            "min_trend_strength": self.min_trend_strength,
            "min_divergence_strength": self.min_divergence_strength,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""OBV策略配置:
        OBV参数: 周期={self.period}, 确认周期={self.signal_confirmation_periods}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class OBVCalculator:
    """成交量平衡指标计算器 - 核心计算逻辑"""

    def __init__(self, period: int = 20):
        self.period = period

    def calculate_obv(self, closes: List[float], volumes: List[float],
                     min_periods: int = None) -> Optional[OBVData]:
        """
        计算OBV指标（支持部分数据计算）

        Args:
            closes: 收盘价列表
            volumes: 成交量列表
            min_periods: 最小计算周期，默认为period

        Returns:
            Optional[OBVData]: OBV数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.period, 20)  # 至少需要period个数据点

        data_count = len(closes)
        required_count = self.period + 10  # 额外数据用于趋势分析

        # 数据完全不足
        if data_count < min_periods or len(volumes) != data_count:
            logger.warning(f"❌ OBV数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ OBV数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ OBV数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_closes = closes[-calculation_period:] if calculation_period < data_count else closes
            calc_volumes = volumes[-calculation_period:] if calculation_period < data_count else volumes

            # 转换为pandas Series进行计算
            close_series = pd.Series(calc_closes)
            volume_series = pd.Series(calc_volumes)

            # 计算OBV
            obv_series = self._calculate_obv_series(close_series, volume_series)

            # 获取最新OBV值和变化
            obv_value = obv_series.iloc[-1]
            obv_change = obv_series.iloc[-1] - obv_series.iloc[-2] if len(obv_series) > 1 else 0

            # 分析OBV趋势
            obv_trend = self._analyze_obv_trend(obv_series)

            # 分析价格趋势
            price_trend = self._analyze_price_trend(close_series)

            # 分析OBV信号
            signal = self._analyze_obv_signal(close_series, obv_series)

            # 计算背离强度
            divergence_strength = self._calculate_divergence_strength(close_series, obv_series)

            # 计算趋势强度
            trend_strength = self._calculate_trend_strength(obv_series)

            # 计算成交量比率
            volume_ratio = self._calculate_volume_ratio(volume_series)

            # 记录详细信息
            logger.info(f"📊 OBV计算完成: OBV={obv_value:.0f}, 变化={obv_change:.0f}")
            logger.info(f"📊 趋势: OBV={obv_trend}, 价格={price_trend}")
            logger.info(f"📊 信号: {signal.value}, 背离强度: {divergence_strength:.3f}")
            logger.info(f"📊 数据统计: 使用{len(calc_closes)}个数据点, 可靠性={reliability:.2f}")

            return OBVData(
                obv_value=round(obv_value, 0),
                obv_change=round(obv_change, 0),
                obv_trend=obv_trend,
                price_trend=price_trend,
                signal=signal,
                divergence_strength=round(divergence_strength, 3),
                trend_strength=round(trend_strength, 3),
                volume_ratio=round(volume_ratio, 3),
                confirmation_periods=2,
                reliability=reliability,
                data_count=len(calc_closes),
                required_count=required_count,
                calculation_period=len(calc_closes)
            )

        except Exception as e:
            logger.error(f"计算OBV异常: {e}")
            return None

    def _calculate_obv_series(self, closes: pd.Series, volumes: pd.Series) -> pd.Series:
        """
        计算OBV序列

        Args:
            closes: 收盘价序列
            volumes: 成交量序列

        Returns:
            pd.Series: OBV序列
        """
        try:
            obv = pd.Series(index=closes.index, dtype=float)
            obv.iloc[0] = volumes.iloc[0]  # 第一个值等于第一个成交量

            for i in range(1, len(closes)):
                if closes.iloc[i] > closes.iloc[i-1]:
                    # 价格上涨，加上成交量
                    obv.iloc[i] = obv.iloc[i-1] + volumes.iloc[i]
                elif closes.iloc[i] < closes.iloc[i-1]:
                    # 价格下跌，减去成交量
                    obv.iloc[i] = obv.iloc[i-1] - volumes.iloc[i]
                else:
                    # 价格不变，OBV不变
                    obv.iloc[i] = obv.iloc[i-1]

            return obv

        except Exception as e:
            logger.error(f"计算OBV序列异常: {e}")
            return pd.Series([0.0] * len(closes))

    def _analyze_obv_trend(self, obv_series: pd.Series) -> str:
        """
        分析OBV趋势

        Args:
            obv_series: OBV序列

        Returns:
            str: 趋势描述
        """
        try:
            if len(obv_series) < 5:
                return "震荡"

            # 计算最近几个周期的斜率
            recent_values = obv_series.tail(5).values
            x = np.arange(len(recent_values))
            slope = np.polyfit(x, recent_values, 1)[0]

            # 计算相对斜率
            avg_obv = abs(obv_series.mean())
            if avg_obv > 0:
                relative_slope = slope / avg_obv
            else:
                relative_slope = 0

            if relative_slope > 0.01:
                return "上升"
            elif relative_slope < -0.01:
                return "下降"
            else:
                return "震荡"

        except Exception as e:
            logger.error(f"分析OBV趋势异常: {e}")
            return "未知"

    def _analyze_price_trend(self, closes: pd.Series) -> str:
        """
        分析价格趋势

        Args:
            closes: 收盘价序列

        Returns:
            str: 趋势描述
        """
        try:
            if len(closes) < 5:
                return "震荡"

            # 计算最近几个周期的斜率
            recent_values = closes.tail(5).values
            x = np.arange(len(recent_values))
            slope = np.polyfit(x, recent_values, 1)[0]

            # 计算相对斜率
            avg_price = closes.mean()
            relative_slope = slope / avg_price

            if relative_slope > 0.005:
                return "上升"
            elif relative_slope < -0.005:
                return "下降"
            else:
                return "震荡"

        except Exception as e:
            logger.error(f"分析价格趋势异常: {e}")
            return "未知"

    def _analyze_obv_signal(self, closes: pd.Series, obv_series: pd.Series) -> OBVSignal:
        """
        分析OBV信号

        Args:
            closes: 收盘价序列
            obv_series: OBV序列

        Returns:
            OBVSignal: OBV信号类型
        """
        try:
            if len(closes) < 5 or len(obv_series) < 5:
                return OBVSignal.NO_SIGNAL

            # 分析趋势
            obv_trend = self._analyze_obv_trend(obv_series)
            price_trend = self._analyze_price_trend(closes)

            # 计算背离强度
            divergence_strength = self._calculate_divergence_strength(closes, obv_series)

            # 检测背离信号
            if abs(divergence_strength) > 0.5:
                if divergence_strength > 0:
                    logger.info(f"🟢 看涨背离: 价格下跌但OBV上升, 强度={divergence_strength:.3f}")
                    return OBVSignal.BULLISH_DIVERGENCE
                else:
                    logger.info(f"🔴 看跌背离: 价格上涨但OBV下降, 强度={abs(divergence_strength):.3f}")
                    return OBVSignal.BEARISH_DIVERGENCE

            # 检测趋势信号
            if obv_trend == "上升" and price_trend == "上升":
                logger.info(f"🚀 多头趋势: OBV和价格同步上升")
                return OBVSignal.BULLISH_TREND
            elif obv_trend == "下降" and price_trend == "下降":
                logger.info(f"💥 空头趋势: OBV和价格同步下降")
                return OBVSignal.BEARISH_TREND

            # 检测趋势强化/减弱
            elif obv_trend == "上升" and price_trend == "震荡":
                logger.debug(f"📊 趋势加强: OBV上升但价格震荡")
                return OBVSignal.TREND_STRENGTHENING
            elif obv_trend == "下降" and price_trend == "震荡":
                logger.debug(f"📊 趋势减弱: OBV下降但价格震荡")
                return OBVSignal.TREND_WEAKENING

            # 其他情况
            else:
                logger.debug(f"⚪ 中性信号: OBV={obv_trend}, 价格={price_trend}")
                return OBVSignal.NEUTRAL

        except Exception as e:
            logger.error(f"分析OBV信号异常: {e}")
            return OBVSignal.NO_SIGNAL

    def _calculate_divergence_strength(self, closes: pd.Series, obv_series: pd.Series) -> float:
        """
        计算背离强度

        Args:
            closes: 收盘价序列
            obv_series: OBV序列

        Returns:
            float: 背离强度 (-1.0 到 1.0)
        """
        try:
            if len(closes) < 10 or len(obv_series) < 10:
                return 0.0

            # 计算最近10个周期的价格和OBV变化
            recent_closes = closes.tail(10)
            recent_obv = obv_series.tail(10)

            # 计算价格变化率
            price_change = (recent_closes.iloc[-1] - recent_closes.iloc[0]) / recent_closes.iloc[0]

            # 计算OBV变化率
            if abs(recent_obv.iloc[0]) > 0:
                obv_change = (recent_obv.iloc[-1] - recent_obv.iloc[0]) / abs(recent_obv.iloc[0])
            else:
                obv_change = 0

            # 计算背离强度
            # 正值表示看涨背离（价格下跌但OBV上升）
            # 负值表示看跌背离（价格上涨但OBV下降）
            if price_change > 0 and obv_change < 0:
                # 价格上涨但OBV下降 - 看跌背离
                divergence = -min(abs(price_change), abs(obv_change))
            elif price_change < 0 and obv_change > 0:
                # 价格下跌但OBV上升 - 看涨背离
                divergence = min(abs(price_change), abs(obv_change))
            else:
                # 同向变化，无背离
                divergence = 0.0

            # 限制在-1到1之间
            return max(-1.0, min(1.0, divergence))

        except Exception as e:
            logger.error(f"计算背离强度异常: {e}")
            return 0.0

    def _calculate_trend_strength(self, obv_series: pd.Series) -> float:
        """
        计算趋势强度

        Args:
            obv_series: OBV序列

        Returns:
            float: 趋势强度 (0.0 到 1.0)
        """
        try:
            if len(obv_series) < 5:
                return 0.0

            # 计算最近几个周期的变化一致性
            recent_changes = obv_series.diff().tail(5).dropna()

            if len(recent_changes) == 0:
                return 0.0

            # 计算同向变化的比例
            positive_changes = (recent_changes > 0).sum()
            negative_changes = (recent_changes < 0).sum()

            # 趋势强度 = 同向变化比例
            if positive_changes > negative_changes:
                strength = positive_changes / len(recent_changes)
            elif negative_changes > positive_changes:
                strength = negative_changes / len(recent_changes)
            else:
                strength = 0.0

            return strength

        except Exception as e:
            logger.error(f"计算趋势强度异常: {e}")
            return 0.0

    def _calculate_volume_ratio(self, volumes: pd.Series) -> float:
        """
        计算成交量比率

        Args:
            volumes: 成交量序列

        Returns:
            float: 成交量比率
        """
        try:
            if len(volumes) < 5:
                return 1.0

            # 计算最近成交量与平均成交量的比率
            recent_volume = volumes.tail(3).mean()
            avg_volume = volumes.mean()

            if avg_volume > 0:
                ratio = recent_volume / avg_volume
            else:
                ratio = 1.0

            return ratio

        except Exception as e:
            logger.error(f"计算成交量比率异常: {e}")
            return 1.0

    def calculate_obv_signal_direction(self, obv_data: OBVData) -> str:
        """
        基于OBV数据计算开仓方向

        Args:
            obv_data: OBV数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 多头趋势信号 - 做多
            if obv_data.signal == OBVSignal.BULLISH_TREND:
                logger.info("🚀 OBV多头趋势做多信号")
                return "long"

            # 空头趋势信号 - 做空
            elif obv_data.signal == OBVSignal.BEARISH_TREND:
                logger.info("💥 OBV空头趋势做空信号")
                return "short"

            # 看涨背离信号 - 做多
            elif obv_data.signal == OBVSignal.BULLISH_DIVERGENCE:
                if obv_data.divergence_strength > 0.3:  # 需要足够的背离强度
                    logger.info("📈 OBV看涨背离做多信号")
                    return "long"
                else:
                    logger.info("⚠️ OBV看涨背离但强度不足")
                    return "hold"

            # 看跌背离信号 - 做空
            elif obv_data.signal == OBVSignal.BEARISH_DIVERGENCE:
                if abs(obv_data.divergence_strength) > 0.3:  # 需要足够的背离强度
                    logger.info("📉 OBV看跌背离做空信号")
                    return "short"
                else:
                    logger.info("⚠️ OBV看跌背离但强度不足")
                    return "hold"

            # 趋势加强信号
            elif obv_data.signal == OBVSignal.TREND_STRENGTHENING:
                if obv_data.obv_change > 0 and obv_data.trend_strength > 0.6:
                    logger.info("📊 OBV趋势加强做多信号")
                    return "long"
                elif obv_data.obv_change < 0 and obv_data.trend_strength > 0.6:
                    logger.info("📊 OBV趋势加强做空信号")
                    return "short"
                else:
                    logger.debug("📊 OBV趋势加强但强度不足")
                    return "hold"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: OBV信号={obv_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算OBV开仓方向异常: {e}")
            return "hold"

    def detect_obv_signal(self, closes: List[float], volumes: List[float],
                         min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测OBV信号

        Args:
            closes: 收盘价列表
            volumes: 成交量列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(closes) < self.period or len(volumes) < self.period:
                return False, "hold", 0.0

            # 计算OBV数据
            obv_data = self.calculate_obv(closes, volumes, min_periods)
            if not obv_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_obv_signal_direction(obv_data)

            if direction != "hold":
                # 计算信号强度
                if obv_data.signal in [OBVSignal.BULLISH_TREND, OBVSignal.BEARISH_TREND]:
                    signal_strength = 0.7 + min(0.3, obv_data.trend_strength)  # 趋势信号基础强度
                elif obv_data.signal in [OBVSignal.BULLISH_DIVERGENCE, OBVSignal.BEARISH_DIVERGENCE]:
                    signal_strength = 0.6 + min(0.4, abs(obv_data.divergence_strength))  # 背离信号基于背离强度
                elif obv_data.signal == OBVSignal.TREND_STRENGTHENING:
                    signal_strength = 0.5 + min(0.4, obv_data.trend_strength)  # 趋势加强基于趋势强度
                else:
                    signal_strength = 0.4  # 其他信号较低强度

                # 结合成交量比率调整强度
                volume_factor = min(1.2, obv_data.volume_ratio)
                signal_strength *= volume_factor

                # 结合可靠性调整强度
                signal_strength *= obv_data.reliability

                # 限制在0-1之间
                signal_strength = min(1.0, signal_strength)

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测OBV信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, closes: List[float], volumes: List[float],
                           signal_type: str, confirmation_periods: int = 2) -> bool:
        """
        确认OBV信号的有效性

        Args:
            closes: 收盘价列表
            volumes: 成交量列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if len(closes) < confirmation_periods + 1 or len(volumes) < confirmation_periods + 1:
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                if i == 0:
                    check_closes = closes
                    check_volumes = volumes
                else:
                    check_closes = closes[:-(i)]
                    check_volumes = volumes[:-(i)]

                has_signal, detected_type, strength = self.detect_obv_signal(
                    check_closes, check_volumes)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ OBV信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ OBV信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认OBV信号异常: {e}")
            return False

class OBVStrategy:
    """成交量平衡指标策略主类"""

    def __init__(self, config: OBVConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = OBVCalculator(period=config.period)

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"📊 OBV策略初始化完成: {config}")

    async def start(self):
        """启动OBV策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ OBV策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ OBV策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 OBV策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ OBV策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止OBV策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 OBV策略已停止")

        except Exception as e:
            logger.error(f"❌ OBV策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动OBV立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            price_data = await self._get_price_data()
            if not price_data or len(price_data['closes']) < self.config.get_required_data_count():
                logger.warning("⚠️ 价格数据不足，无法进行OBV分析")
                return

            # 检查成交量数据
            if 'volumes' not in price_data or len(price_data['volumes']) != len(price_data['closes']):
                logger.warning("⚠️ 成交量数据不足或不匹配，无法进行OBV分析")
                return

            # 检测OBV信号
            has_signal, signal_type, signal_strength = self.calculator.detect_obv_signal(
                price_data['closes'], price_data['volumes'])

            if not has_signal:
                logger.debug("未检测到OBV信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(
                    price_data['closes'], price_data['volumes'],
                    signal_type, self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ OBV信号未确认: {signal_type}")
                    return

            # 信号过滤
            if not self._filter_signal(price_data, signal_type, signal_strength):
                logger.warning(f"⚠️ OBV信号被过滤: {signal_type}")
                return

            logger.info(f"🚀 准备执行OBV开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    def _filter_signal(self, price_data: Dict, signal_type: str, signal_strength: float) -> bool:
        """
        过滤OBV信号

        Args:
            price_data: 价格数据字典
            signal_type: 信号类型
            signal_strength: 信号强度

        Returns:
            bool: 信号是否通过过滤
        """
        try:
            # 计算OBV数据用于过滤
            obv_data = self.calculator.calculate_obv(
                price_data['closes'], price_data['volumes'])
            if not obv_data:
                return False

            # 趋势过滤
            if self.config.enable_trend_filter:
                if obv_data.trend_strength < self.config.min_trend_strength:
                    logger.debug(f"趋势强度不足: {obv_data.trend_strength:.3f} < {self.config.min_trend_strength}")
                    return False

            # 背离强度过滤
            if self.config.enable_divergence_detection:
                if abs(obv_data.divergence_strength) < self.config.min_divergence_strength:
                    logger.debug(f"背离强度不足: {abs(obv_data.divergence_strength):.3f} < {self.config.min_divergence_strength}")
                    return False

            # 成交量确认过滤
            if self.config.enable_volume_confirmation:
                if obv_data.volume_ratio < self.config.volume_threshold:
                    logger.debug(f"成交量不足: {obv_data.volume_ratio:.3f} < {self.config.volume_threshold}")
                    return False

            return True

        except Exception as e:
            logger.error(f"过滤OBV信号异常: {e}")
            return False

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [OBV立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [OBV开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [OBV信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 [自定义设置] 交易对: {self.config.custom_symbol}, 杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [OBV参数] 周期: {self.config.period}, 确认周期: {self.config.signal_confirmation_periods}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [OBV立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'custom_symbol': self.config.custom_symbol,
                    'custom_leverage': self.config.custom_leverage,
                    'obv_period': self.config.period,
                    'confirmation_periods': self.config.signal_confirmation_periods
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [OBV立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [OBV立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.965 if direction == "long" else entry_price * 1.035  # 默认3.5%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")
            logger.info(f"🛡️ [止损订单] 交易对: {self.config.custom_symbol}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动OBV持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    # 检查平仓机会
                    await self._check_close_position_opportunity()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_close_position_opportunity(self):
        """检查平仓机会"""
        try:
            # 检查是否有持仓
            if not await self._has_position():
                return

            # 获取当前持仓信息
            positions = await self._get_current_positions()
            if not positions:
                return

            current_position = positions[0]

            # 获取当前价格和成交量数据
            price_data = await self._get_price_data()
            if not price_data or len(price_data['closes']) < self.config.get_required_data_count():
                logger.debug("价格数据不足，无法进行OBV平仓分析")
                return

            if 'volumes' not in price_data or len(price_data['volumes']) != len(price_data['closes']):
                logger.debug("成交量数据不足，无法进行OBV平仓分析")
                return

            # 检测OBV平仓信号
            should_close, close_reason = await self._detect_obv_close_signal(
                price_data, current_position
            )

            if should_close:
                logger.info(f"🎯 [OBV平仓] 检测到平仓信号: {close_reason}")
                await self._execute_close_position(current_position, close_reason)

        except Exception as e:
            logger.error(f"❌ 检查OBV平仓机会异常: {e}")

    async def _detect_obv_close_signal(self, price_data: Dict, position) -> Tuple[bool, str]:
        """
        检测OBV平仓信号

        Args:
            price_data: 价格和成交量数据
            position: 当前持仓信息

        Returns:
            Tuple[bool, str]: (是否应该平仓, 平仓原因)
        """
        try:
            # 计算当前OBV数据
            obv_data = self.calculator.calculate_obv(
                price_data['closes'], price_data['volumes']
            )

            if not obv_data:
                return False, "无法计算OBV数据"

            # 获取持仓方向
            position_side = position.side.value if hasattr(position.side, 'value') else str(position.side)

            # 平仓逻辑：
            # 多仓 + 看跌信号 = 平仓
            # 空仓 + 看涨信号 = 平仓

            # 检查趋势反转信号
            if position_side == "long":
                # 多仓平仓条件
                if obv_data.signal == OBVSignal.BEARISH_TREND:
                    return True, f"多仓遇到OBV空头趋势信号，趋势强度: {obv_data.trend_strength:.3f}"

                elif obv_data.signal == OBVSignal.BEARISH_DIVERGENCE:
                    if abs(obv_data.divergence_strength) > self.config.min_divergence_strength:
                        return True, f"多仓遇到OBV看跌背离信号，背离强度: {abs(obv_data.divergence_strength):.3f}"

                elif obv_data.signal == OBVSignal.TREND_WEAKENING:
                    if obv_data.trend_strength < 0.3:  # 趋势明显减弱
                        return True, f"多仓趋势明显减弱，趋势强度: {obv_data.trend_strength:.3f}"

            elif position_side == "short":
                # 空仓平仓条件
                if obv_data.signal == OBVSignal.BULLISH_TREND:
                    return True, f"空仓遇到OBV多头趋势信号，趋势强度: {obv_data.trend_strength:.3f}"

                elif obv_data.signal == OBVSignal.BULLISH_DIVERGENCE:
                    if obv_data.divergence_strength > self.config.min_divergence_strength:
                        return True, f"空仓遇到OBV看涨背离信号，背离强度: {obv_data.divergence_strength:.3f}"

                elif obv_data.signal == OBVSignal.TREND_WEAKENING:
                    if obv_data.trend_strength < 0.3:  # 趋势明显减弱
                        return True, f"空仓趋势明显减弱，趋势强度: {obv_data.trend_strength:.3f}"

            # 检查成交量异常
            if obv_data.volume_ratio < 0.5:  # 成交量大幅萎缩
                return True, f"成交量大幅萎缩，可能趋势结束，成交量比率: {obv_data.volume_ratio:.3f}"

            # 检查可靠性下降
            if obv_data.reliability < 0.6:  # 数据可靠性下降
                return True, f"OBV数据可靠性下降，建议平仓，可靠性: {obv_data.reliability:.3f}"

            return False, f"OBV信号与持仓方向匹配，继续持有: 持仓={position_side}, 信号={obv_data.signal.value}"

        except Exception as e:
            logger.error(f"❌ 检测OBV平仓信号异常: {e}")
            return False, f"检测异常: {str(e)}"

    async def _execute_close_position(self, position, reason: str):
        """
        执行平仓操作

        Args:
            position: 持仓信息
            reason: 平仓原因
        """
        try:
            logger.info(f"🚀 [OBV平仓] 开始执行平仓: {reason}")

            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            # 确定平仓方向
            position_side = position.side.value if hasattr(position.side, 'value') else str(position.side)

            if position_side == "long":
                close_side = "sell"
            elif position_side == "short":
                close_side = "buy"
            else:
                logger.error(f"❌ 未知的持仓方向: {position_side}")
                return False

            # 执行市价平仓
            logger.info(f"📤 [OBV平仓] 执行市价平仓: {position_side}仓, 数量: {position.size}")

            # 这里需要实际的交易所接口调用
            # 由于当前代码中exchange_manager可能不是真实的交易所实例
            # 我们先记录平仓意图，实际实现需要根据具体的交易所接口

            logger.info(f"📋 [OBV平仓] 平仓参数:")
            logger.info(f"   交易对: {self.config.custom_symbol}")
            logger.info(f"   方向: {close_side}")
            logger.info(f"   数量: {position.size}")
            logger.info(f"   原因: {reason}")

            # 模拟平仓成功（实际实现时需要调用真实的交易所API）
            logger.info(f"✅ [OBV平仓] 平仓操作完成")

            # 重置策略状态
            self.current_position = None

            # 禁用立即开仓功能（平仓后重新启用）
            self.config.immediate_open_enabled = True
            logger.info("🔓 [OBV平仓] 平仓后重新启用立即开仓功能")

            return True

        except Exception as e:
            logger.error(f"❌ [OBV平仓] 执行平仓异常: {e}")
            return False

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_data(self) -> Dict:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                return {}

            # 这里实现价格数据获取逻辑
            return {
                'closes': [],
                'volumes': []
            }  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return {}

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [OBV设置] 周期: {self.config.period}, 确认周期: {self.config.signal_confirmation_periods}")

            # 这里实现市价下单逻辑
            return True  # 临时返回

        except Exception as e:
            logger.error(f"❌ 市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
            "obv_period": self.config.period,
            "confirmation_periods": self.config.signal_confirmation_periods,
        }
