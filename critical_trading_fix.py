#!/usr/bin/env python3
"""
关键交易问题修复方案
修复平仓逻辑中的严重缺陷
"""

import asyncio
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CriticalTradingFix:
    """关键交易修复器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.verification_results = []
    
    async def apply_all_fixes(self):
        """应用所有修复"""
        print("🚨 开始应用关键交易修复")
        print("=" * 80)
        
        # 修复1：OKX交易所的posSide参数错误
        await self._fix_okx_pos_side_logic()
        
        # 修复2：平仓成功验证逻辑
        await self._fix_close_position_verification()
        
        # 修复3：增加订单成交验证
        await self._add_order_fill_verification()
        
        # 修复4：增加持仓状态交叉验证
        await self._add_position_cross_verification()
        
        # 生成修复报告
        self._generate_fix_report()
    
    async def _fix_okx_pos_side_logic(self):
        """修复OKX交易所的posSide参数逻辑"""
        print("\n🔧 修复1: OKX交易所posSide参数错误")
        
        try:
            # 读取当前文件
            with open('exchanges/okx_exchange.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找需要修复的代码
            old_code = '"posSide": "long" if side == OrderSide.BUY else "short"  # 合约必需的持仓方向'
            
            if old_code in content:
                print("❌ 发现错误的posSide逻辑")
                print("   当前逻辑会导致平仓变成开仓")
                
                # 这个修复需要更复杂的逻辑，因为需要区分开仓和平仓
                print("⚠️  此修复需要重构place_order方法以支持平仓参数")
                print("   建议添加reduce_only参数来明确区分开仓和平仓")
                
                self.fixes_applied.append({
                    'fix': 'OKX posSide参数',
                    'status': 'identified',
                    'description': '发现posSide参数设置错误，需要重构',
                    'impact': 'critical'
                })
            else:
                print("✅ 未发现该问题或已修复")
                
        except Exception as e:
            print(f"❌ 修复失败: {e}")
    
    async def _fix_close_position_verification(self):
        """修复平仓成功验证逻辑"""
        print("\n🔧 修复2: 平仓成功验证逻辑")
        
        try:
            # 读取当前文件
            with open('strategies/bollinger_strategy.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找需要修复的代码
            old_verification = 'if order_result and order_result.order_id:'
            
            if old_verification in content:
                print("❌ 发现不充分的平仓验证逻辑")
                print("   当前只检查订单ID存在，不验证实际成交")
                
                # 建议的新验证逻辑
                new_verification = '''
            # 验证订单是否真正成交
            if order_result and order_result.order_id:
                # 等待订单完成并验证成交
                max_wait = 30  # 最多等待30秒
                wait_interval = 1
                waited = 0
                
                while waited < max_wait:
                    # 查询最新订单状态
                    current_order = await exchange.get_order_status(symbol, order_result.order_id)
                    
                    if current_order:
                        if current_order.status == "filled":
                            # 验证成交数量
                            if current_order.filled >= position.size * 0.99:  # 允许0.01的误差
                                logger.info(f"✅ [风险控制] 平仓完全成交: {current_order.filled}/{position.size}")
                                
                                # 二次验证：检查持仓是否真正减少
                                await asyncio.sleep(2)  # 等待持仓更新
                                new_positions = await exchange.get_positions(symbol)
                                
                                if not new_positions or all(pos.size == 0 for pos in new_positions):
                                    logger.info("✅ [风险控制] 持仓验证通过，平仓成功")
                                    self.reset_strategy()
                                    return True
                                else:
                                    logger.error("❌ [风险控制] 持仓验证失败，平仓未完成")
                                    return False
                            else:
                                logger.warning(f"⚠️ [风险控制] 部分成交: {current_order.filled}/{position.size}")
                        elif current_order.status in ["canceled", "rejected", "failed"]:
                            logger.error(f"❌ [风险控制] 平仓订单失败: {current_order.status}")
                            return False
                    
                    await asyncio.sleep(wait_interval)
                    waited += wait_interval
                
                logger.error("❌ [风险控制] 平仓超时，验证失败")
                return False
            else:
                logger.error("❌ [风险控制] 平仓订单提交失败")
                return False
                '''
                
                print("✅ 建议的新验证逻辑已准备")
                
                self.fixes_applied.append({
                    'fix': '平仓验证逻辑',
                    'status': 'prepared',
                    'description': '准备了更严格的平仓验证逻辑',
                    'impact': 'high'
                })
            else:
                print("✅ 未发现该问题")
                
        except Exception as e:
            print(f"❌ 修复失败: {e}")
    
    async def _add_order_fill_verification(self):
        """增加订单成交验证"""
        print("\n🔧 修复3: 增加订单成交验证")
        
        verification_code = '''
    async def verify_order_execution(self, exchange, symbol: str, order_id: str, 
                                   expected_amount: float, max_wait: int = 30) -> Dict:
        """
        验证订单执行结果
        
        Args:
            exchange: 交易所实例
            symbol: 交易对
            order_id: 订单ID
            expected_amount: 期望成交数量
            max_wait: 最大等待时间（秒）
            
        Returns:
            Dict: 验证结果
        """
        try:
            wait_interval = 1
            waited = 0
            
            while waited < max_wait:
                order_info = await exchange.get_order_status(symbol, order_id)
                
                if order_info:
                    fill_ratio = order_info.filled / expected_amount if expected_amount > 0 else 0
                    
                    if order_info.status == "filled" and fill_ratio >= 0.99:
                        return {
                            'success': True,
                            'status': 'fully_filled',
                            'filled_amount': order_info.filled,
                            'fill_ratio': fill_ratio,
                            'message': f'订单完全成交: {order_info.filled}/{expected_amount}'
                        }
                    elif order_info.status in ["canceled", "rejected", "failed"]:
                        return {
                            'success': False,
                            'status': order_info.status,
                            'filled_amount': order_info.filled,
                            'fill_ratio': fill_ratio,
                            'message': f'订单执行失败: {order_info.status}'
                        }
                    elif order_info.status == "partially_filled":
                        logger.info(f"订单部分成交: {order_info.filled}/{expected_amount} ({fill_ratio*100:.1f}%)")
                
                await asyncio.sleep(wait_interval)
                waited += wait_interval
            
            # 超时
            final_order = await exchange.get_order_status(symbol, order_id)
            final_fill_ratio = final_order.filled / expected_amount if final_order and expected_amount > 0 else 0
            
            return {
                'success': False,
                'status': 'timeout',
                'filled_amount': final_order.filled if final_order else 0,
                'fill_ratio': final_fill_ratio,
                'message': f'订单验证超时，最终成交: {final_fill_ratio*100:.1f}%'
            }
            
        except Exception as e:
            return {
                'success': False,
                'status': 'error',
                'filled_amount': 0,
                'fill_ratio': 0,
                'message': f'验证异常: {str(e)}'
            }
        '''
        
        print("✅ 订单成交验证方法已准备")
        
        self.fixes_applied.append({
            'fix': '订单成交验证',
            'status': 'prepared',
            'description': '准备了详细的订单成交验证方法',
            'impact': 'high'
        })
    
    async def _add_position_cross_verification(self):
        """增加持仓状态交叉验证"""
        print("\n🔧 修复4: 增加持仓状态交叉验证")
        
        verification_code = '''
    async def verify_position_change(self, exchange, symbol: str, 
                                   before_positions: List, expected_change: float) -> Dict:
        """
        验证持仓变化
        
        Args:
            exchange: 交易所实例
            symbol: 交易对
            before_positions: 操作前的持仓
            expected_change: 期望的持仓变化（负数表示减少）
            
        Returns:
            Dict: 验证结果
        """
        try:
            # 等待持仓更新
            await asyncio.sleep(2)
            
            after_positions = await exchange.get_positions(symbol)
            
            before_total = sum(abs(pos.size) for pos in before_positions) if before_positions else 0
            after_total = sum(abs(pos.size) for pos in after_positions) if after_positions else 0
            
            actual_change = after_total - before_total
            change_diff = abs(actual_change - expected_change)
            
            # 允许小幅误差
            tolerance = abs(expected_change) * 0.01  # 1%误差
            
            if change_diff <= tolerance:
                return {
                    'success': True,
                    'before_total': before_total,
                    'after_total': after_total,
                    'expected_change': expected_change,
                    'actual_change': actual_change,
                    'message': f'持仓变化验证通过: {before_total} -> {after_total}'
                }
            else:
                return {
                    'success': False,
                    'before_total': before_total,
                    'after_total': after_total,
                    'expected_change': expected_change,
                    'actual_change': actual_change,
                    'message': f'持仓变化异常: 期望{expected_change}，实际{actual_change}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'持仓验证异常: {str(e)}'
            }
        '''
        
        print("✅ 持仓状态交叉验证方法已准备")
        
        self.fixes_applied.append({
            'fix': '持仓状态交叉验证',
            'status': 'prepared',
            'description': '准备了持仓状态交叉验证方法',
            'impact': 'high'
        })
    
    def _generate_fix_report(self):
        """生成修复报告"""
        print("\n" + "=" * 80)
        print("📋 关键交易问题修复报告")
        print("=" * 80)
        
        print(f"🎯 修复总结:")
        print(f"   发现问题: {len(self.fixes_applied)} 个")
        print(f"   修复时间: {datetime.now()}")
        
        print(f"\n🔧 修复详情:")
        for i, fix in enumerate(self.fixes_applied, 1):
            print(f"   {i}. {fix['fix']} - {fix['status'].upper()}")
            print(f"      描述: {fix['description']}")
            print(f"      影响: {fix['impact']}")
        
        print(f"\n🚨 关键发现:")
        print("   1. OKX交易所posSide参数设置错误，导致平仓变成开仓")
        print("   2. 平仓验证逻辑不充分，只检查订单ID不验证成交")
        print("   3. 缺少订单成交状态的详细验证")
        print("   4. 缺少持仓状态的交叉验证机制")
        
        print(f"\n🔧 立即修复建议:")
        print("   1. 紧急修复OKX交易所的place_order方法")
        print("   2. 增加reduce_only参数明确区分开仓和平仓")
        print("   3. 重写平仓验证逻辑，增加多重验证")
        print("   4. 实现订单-持仓状态交叉验证")
        print("   5. 增加平仓操作的回滚机制")

async def main():
    """主函数"""
    try:
        fixer = CriticalTradingFix()
        await fixer.apply_all_fixes()
        return True
    except Exception as e:
        print(f"❌ 修复过程异常: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(main())
