"""
资金流量指标(MFI)策略实现
基于资金流量指标的超买超卖和背离信号进行交易
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，创建空装饰器
    def monitor_task(task_type):
        def decorator(func):
            return func
        return decorator

    class TaskType:
        OPENING_CHECK = "opening_check"
        PRICE_FETCH = "price_fetch"
        ORDER_EXECUTION = "order_execution"
        POSITION_CHECK = "position_check"

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class MFISignal(Enum):
    """MFI信号"""
    OVERSOLD = "oversold"              # 超卖信号 (MFI < 20)
    OVERBOUGHT = "overbought"          # 超买信号 (MFI > 80)
    BULLISH_DIVERGENCE = "bullish_divergence"    # 看涨背离
    BEARISH_DIVERGENCE = "bearish_divergence"    # 看跌背离
    EXTREME_OVERSOLD = "extreme_oversold"        # 极度超卖 (MFI < 10)
    EXTREME_OVERBOUGHT = "extreme_overbought"    # 极度超买 (MFI > 90)
    NEUTRAL = "neutral"                # 中性区域
    RISING_TREND = "rising_trend"      # 上升趋势
    FALLING_TREND = "falling_trend"    # 下降趋势

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class MFIData:
    """MFI数据（包含可靠性评估）"""
    mfi_value: float          # MFI值
    typical_price: float      # 典型价格
    money_flow: float         # 资金流量
    positive_flow: float      # 正向资金流
    negative_flow: float      # 负向资金流
    signal: MFISignal         # MFI信号
    trend_direction: str      # 趋势方向
    divergence_strength: float # 背离强度 (0.0-1.0)
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 14  # 需要的数据点数
    calculation_period: int = 14  # 实际计算周期

class MFIConfig:
    """资金流量指标策略配置"""
    
    def __init__(self):
        # MFI指标参数
        self.period = 14                 # MFI计算周期
        self.overbought_threshold = 80   # 超买阈值
        self.oversold_threshold = 20     # 超卖阈值
        self.extreme_overbought = 90     # 极度超买阈值
        self.extreme_oversold = 10       # 极度超卖阈值
        self.signal_confirmation_periods = 2  # 信号确认周期
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "BNB/USDT"  # 自定义交易对
        self.custom_leverage = 18        # 自定义杠杆倍数
        self.initial_margin = 600.0      # 初始保证金 (USDT)
        self.take_profit_percent = 1.5   # 止盈百分比
        self.stop_loss_percent = 3.8     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 60    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.0  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 4              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [2.0, 3.5, 5.5, 8.0]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 12.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.20     # 最大投入资金比例 (20%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # MFI信号过滤
        self.enable_divergence_detection = True  # 启用背离检测
        self.min_divergence_strength = 0.3      # 最小背离强度
        self.enable_extreme_levels = True       # 启用极值水平
        self.enable_trend_filter = True         # 启用趋势过滤
        self.min_trend_periods = 3              # 最小趋势周期
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 65.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 30             # 下单冷却时间(秒)
        self.position_check_interval = 10    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 100              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证MFI参数
            if self.period <= 0 or self.period > 100:
                logger.error("MFI周期必须在1-100之间")
                return False
            
            if not (0 <= self.oversold_threshold <= 50):
                logger.error("超卖阈值必须在0-50之间")
                return False
            
            if not (50 <= self.overbought_threshold <= 100):
                logger.error("超买阈值必须在50-100之间")
                return False
            
            if self.oversold_threshold >= self.overbought_threshold:
                logger.error("超卖阈值必须小于超买阈值")
                return False
            
            if not (0 <= self.extreme_oversold <= self.oversold_threshold):
                logger.error("极度超卖阈值设置无效")
                return False
            
            if not (self.overbought_threshold <= self.extreme_overbought <= 100):
                logger.error("极度超买阈值设置无效")
                return False
            
            if self.signal_confirmation_periods < 1 or self.signal_confirmation_periods > 10:
                logger.error("信号确认周期必须在1-10之间")
                return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            # 验证信号过滤参数
            if self.min_divergence_strength < 0 or self.min_divergence_strength > 1:
                logger.error("最小背离强度必须在0-1之间")
                return False
            
            if self.min_trend_periods < 1 or self.min_trend_periods > 10:
                logger.error("最小趋势周期必须在1-10之间")
                return False
            
            logger.info("✅ MFI策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ MFI策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # MFI需要的最小数据点数
        return max(self.period, 14) + 10  # 额外10个点用于背离检测
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # MFI参数
            "period": self.period,
            "overbought_threshold": self.overbought_threshold,
            "oversold_threshold": self.oversold_threshold,
            "extreme_overbought": self.extreme_overbought,
            "extreme_oversold": self.extreme_oversold,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_divergence_detection": self.enable_divergence_detection,
            "min_divergence_strength": self.min_divergence_strength,
            "enable_extreme_levels": self.enable_extreme_levels,
            "enable_trend_filter": self.enable_trend_filter,
            "min_trend_periods": self.min_trend_periods,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""MFI策略配置:
        MFI参数: 周期={self.period}, 超买={self.overbought_threshold}, 超卖={self.oversold_threshold}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class MFICalculator:
    """资金流量指标计算器 - 核心计算逻辑"""

    def __init__(self, period: int = 14):
        self.period = period

    def calculate_mfi(self, highs: List[float], lows: List[float], closes: List[float],
                     volumes: List[float], min_periods: int = None) -> Optional[MFIData]:
        """
        计算MFI指标（支持部分数据计算）

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            volumes: 成交量列表
            min_periods: 最小计算周期，默认为period

        Returns:
            Optional[MFIData]: MFI数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.period, 10)  # 至少需要period个数据点

        data_count = len(closes)
        required_count = self.period + 10  # 额外数据用于背离检测

        # 数据完全不足
        if data_count < min_periods or len(highs) != data_count or len(lows) != data_count or len(volumes) != data_count:
            logger.warning(f"❌ MFI数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ MFI数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ MFI数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_highs = highs[-calculation_period:] if calculation_period < data_count else highs
            calc_lows = lows[-calculation_period:] if calculation_period < data_count else lows
            calc_closes = closes[-calculation_period:] if calculation_period < data_count else closes
            calc_volumes = volumes[-calculation_period:] if calculation_period < data_count else volumes

            # 转换为pandas Series进行计算
            high_series = pd.Series(calc_highs)
            low_series = pd.Series(calc_lows)
            close_series = pd.Series(calc_closes)
            volume_series = pd.Series(calc_volumes)

            # 计算典型价格 (Typical Price)
            typical_price = (high_series + low_series + close_series) / 3

            # 计算资金流量 (Money Flow)
            money_flow = typical_price * volume_series

            # 计算正向和负向资金流
            positive_flow, negative_flow = self._calculate_money_flow_direction(typical_price, money_flow)

            # 计算MFI值
            mfi_value = self._calculate_mfi_value(positive_flow, negative_flow)

            # 分析MFI信号
            signal = self._analyze_mfi_signal(mfi_value, typical_price.tolist())

            # 计算趋势方向
            trend_direction = self._calculate_trend_direction(typical_price.tolist())

            # 计算背离强度
            divergence_strength = self._calculate_divergence_strength(typical_price.tolist(),
                                                                    self._get_mfi_history(calc_highs, calc_lows, calc_closes, calc_volumes))

            # 记录详细信息
            logger.info(f"📊 MFI计算完成: MFI={mfi_value:.2f}, 典型价格={typical_price.iloc[-1]:.4f}")
            logger.info(f"📊 资金流: 正向={positive_flow[-1]:.0f}, 负向={negative_flow[-1]:.0f}")
            logger.info(f"📊 信号: {signal.value}, 趋势: {trend_direction}, 背离强度: {divergence_strength:.3f}")
            logger.info(f"📊 数据统计: 使用{len(calc_closes)}个数据点, 可靠性={reliability:.2f}")

            return MFIData(
                mfi_value=round(mfi_value, 2),
                typical_price=round(typical_price.iloc[-1], 4),
                money_flow=round(money_flow.iloc[-1], 2),
                positive_flow=round(positive_flow[-1], 2),
                negative_flow=round(negative_flow[-1], 2),
                signal=signal,
                trend_direction=trend_direction,
                divergence_strength=divergence_strength,
                reliability=reliability,
                data_count=len(calc_closes),
                required_count=required_count,
                calculation_period=len(calc_closes)
            )

        except Exception as e:
            logger.error(f"计算MFI异常: {e}")
            return None

    def _calculate_money_flow_direction(self, typical_price: pd.Series, money_flow: pd.Series) -> Tuple[List[float], List[float]]:
        """
        计算正向和负向资金流

        Args:
            typical_price: 典型价格序列
            money_flow: 资金流量序列

        Returns:
            Tuple[List[float], List[float]]: (正向资金流, 负向资金流)
        """
        try:
            positive_flow = []
            negative_flow = []

            for i in range(len(typical_price)):
                if i == 0:
                    # 第一个数据点，无法比较，设为0
                    positive_flow.append(0.0)
                    negative_flow.append(0.0)
                else:
                    if typical_price.iloc[i] > typical_price.iloc[i-1]:
                        # 典型价格上涨，正向资金流
                        positive_flow.append(money_flow.iloc[i])
                        negative_flow.append(0.0)
                    elif typical_price.iloc[i] < typical_price.iloc[i-1]:
                        # 典型价格下跌，负向资金流
                        positive_flow.append(0.0)
                        negative_flow.append(money_flow.iloc[i])
                    else:
                        # 典型价格不变，无资金流
                        positive_flow.append(0.0)
                        negative_flow.append(0.0)

            return positive_flow, negative_flow

        except Exception as e:
            logger.error(f"计算资金流方向异常: {e}")
            return [0.0] * len(typical_price), [0.0] * len(typical_price)

    def _calculate_mfi_value(self, positive_flow: List[float], negative_flow: List[float]) -> float:
        """
        计算MFI值

        Args:
            positive_flow: 正向资金流列表
            negative_flow: 负向资金流列表

        Returns:
            float: MFI值 (0-100)
        """
        try:
            # 计算指定周期内的正向和负向资金流总和
            period_positive = sum(positive_flow[-self.period:])
            period_negative = sum(negative_flow[-self.period:])

            # 避免除零错误
            if period_negative == 0:
                return 100.0  # 全部为正向资金流

            if period_positive == 0:
                return 0.0    # 全部为负向资金流

            # 计算资金流比率 (Money Flow Ratio)
            money_flow_ratio = period_positive / period_negative

            # 计算MFI值
            mfi_value = 100 - (100 / (1 + money_flow_ratio))

            return max(0.0, min(100.0, mfi_value))  # 确保在0-100范围内

        except Exception as e:
            logger.error(f"计算MFI值异常: {e}")
            return 50.0  # 默认中性值

    def _analyze_mfi_signal(self, mfi_value: float, typical_prices: List[float]) -> MFISignal:
        """
        分析MFI信号

        Args:
            mfi_value: MFI值
            typical_prices: 典型价格历史

        Returns:
            MFISignal: MFI信号类型
        """
        try:
            # 极值水平检测
            if mfi_value <= 10:
                logger.info(f"🟢 MFI极度超卖信号: {mfi_value:.2f} <= 10")
                return MFISignal.EXTREME_OVERSOLD
            elif mfi_value >= 90:
                logger.info(f"🔴 MFI极度超买信号: {mfi_value:.2f} >= 90")
                return MFISignal.EXTREME_OVERBOUGHT

            # 标准超买超卖检测
            elif mfi_value <= 20:
                logger.info(f"🟢 MFI超卖信号: {mfi_value:.2f} <= 20")
                return MFISignal.OVERSOLD
            elif mfi_value >= 80:
                logger.info(f"🔴 MFI超买信号: {mfi_value:.2f} >= 80")
                return MFISignal.OVERBOUGHT

            # 趋势检测
            elif len(typical_prices) >= 5:
                recent_trend = (typical_prices[-1] - typical_prices[-5]) / typical_prices[-5]
                if recent_trend > 0.02:  # 上涨超过2%
                    logger.debug(f"📈 MFI上升趋势: {mfi_value:.2f}")
                    return MFISignal.RISING_TREND
                elif recent_trend < -0.02:  # 下跌超过2%
                    logger.debug(f"📉 MFI下降趋势: {mfi_value:.2f}")
                    return MFISignal.FALLING_TREND

            # 中性区域
            logger.debug(f"⚪ MFI中性区域: {mfi_value:.2f}")
            return MFISignal.NEUTRAL

        except Exception as e:
            logger.error(f"分析MFI信号异常: {e}")
            return MFISignal.NEUTRAL

    def _calculate_trend_direction(self, typical_prices: List[float]) -> str:
        """
        计算趋势方向

        Args:
            typical_prices: 典型价格历史

        Returns:
            str: 趋势方向 ("上升", "下降", "震荡")
        """
        try:
            if len(typical_prices) < 5:
                return "震荡"

            # 计算短期和长期趋势
            short_trend = (typical_prices[-1] - typical_prices[-3]) / typical_prices[-3]
            long_trend = (typical_prices[-1] - typical_prices[-5]) / typical_prices[-5]

            # 趋势判断
            if short_trend > 0.01 and long_trend > 0.01:
                return "上升"
            elif short_trend < -0.01 and long_trend < -0.01:
                return "下降"
            else:
                return "震荡"

        except Exception as e:
            logger.error(f"计算趋势方向异常: {e}")
            return "震荡"

    def _calculate_divergence_strength(self, typical_prices: List[float], mfi_history: List[float]) -> float:
        """
        计算背离强度

        Args:
            typical_prices: 典型价格历史
            mfi_history: MFI历史值

        Returns:
            float: 背离强度 (0.0-1.0)
        """
        try:
            if len(typical_prices) < 10 or len(mfi_history) < 10:
                return 0.0

            # 简化的背离检测：比较价格和MFI的趋势方向
            price_trend = (typical_prices[-1] - typical_prices[-5]) / typical_prices[-5]
            mfi_trend = (mfi_history[-1] - mfi_history[-5]) / mfi_history[-5] if mfi_history[-5] != 0 else 0

            # 背离强度计算
            if price_trend * mfi_trend < 0:  # 方向相反
                divergence_strength = min(1.0, abs(price_trend - mfi_trend) * 10)
                return divergence_strength
            else:
                return 0.0

        except Exception as e:
            logger.error(f"计算背离强度异常: {e}")
            return 0.0

    def _get_mfi_history(self, highs: List[float], lows: List[float],
                        closes: List[float], volumes: List[float]) -> List[float]:
        """
        获取MFI历史值

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            volumes: 成交量列表

        Returns:
            List[float]: MFI历史值列表
        """
        try:
            mfi_history = []

            # 计算每个时间点的MFI值
            for i in range(self.period, len(closes)):
                period_highs = highs[i-self.period+1:i+1]
                period_lows = lows[i-self.period+1:i+1]
                period_closes = closes[i-self.period+1:i+1]
                period_volumes = volumes[i-self.period+1:i+1]

                # 计算典型价格
                typical_prices = [(h + l + c) / 3 for h, l, c in zip(period_highs, period_lows, period_closes)]

                # 计算资金流量
                money_flows = [tp * v for tp, v in zip(typical_prices, period_volumes)]

                # 计算正向和负向资金流
                positive_flow = []
                negative_flow = []

                for j in range(len(typical_prices)):
                    if j == 0:
                        positive_flow.append(0.0)
                        negative_flow.append(0.0)
                    else:
                        if typical_prices[j] > typical_prices[j-1]:
                            positive_flow.append(money_flows[j])
                            negative_flow.append(0.0)
                        elif typical_prices[j] < typical_prices[j-1]:
                            positive_flow.append(0.0)
                            negative_flow.append(money_flows[j])
                        else:
                            positive_flow.append(0.0)
                            negative_flow.append(0.0)

                # 计算MFI值
                period_positive = sum(positive_flow)
                period_negative = sum(negative_flow)

                if period_negative == 0:
                    mfi_value = 100.0
                elif period_positive == 0:
                    mfi_value = 0.0
                else:
                    money_flow_ratio = period_positive / period_negative
                    mfi_value = 100 - (100 / (1 + money_flow_ratio))

                mfi_history.append(mfi_value)

            return mfi_history

        except Exception as e:
            logger.error(f"获取MFI历史异常: {e}")
            return []

    def calculate_mfi_signal_direction(self, mfi_data: MFIData) -> str:
        """
        基于MFI数据计算开仓方向

        Args:
            mfi_data: MFI数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 极度超卖信号 - 强烈做多
            if mfi_data.signal == MFISignal.EXTREME_OVERSOLD:
                logger.info("🚀 MFI极度超卖做多信号")
                return "long"

            # 极度超买信号 - 强烈做空
            elif mfi_data.signal == MFISignal.EXTREME_OVERBOUGHT:
                logger.info("💥 MFI极度超买做空信号")
                return "short"

            # 标准超卖信号 - 做多
            elif mfi_data.signal == MFISignal.OVERSOLD:
                logger.info("📈 MFI超卖做多信号")
                return "long"

            # 标准超买信号 - 做空
            elif mfi_data.signal == MFISignal.OVERBOUGHT:
                logger.info("📉 MFI超买做空信号")
                return "short"

            # 看涨背离信号 - 做多
            elif mfi_data.signal == MFISignal.BULLISH_DIVERGENCE:
                if mfi_data.divergence_strength > 0.3:  # 背离强度足够
                    logger.info("🔄 MFI看涨背离做多信号")
                    return "long"
                else:
                    logger.info("⚠️ MFI看涨背离但强度不足")
                    return "hold"

            # 看跌背离信号 - 做空
            elif mfi_data.signal == MFISignal.BEARISH_DIVERGENCE:
                if mfi_data.divergence_strength > 0.3:  # 背离强度足够
                    logger.info("🔄 MFI看跌背离做空信号")
                    return "short"
                else:
                    logger.info("⚠️ MFI看跌背离但强度不足")
                    return "hold"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: MFI={mfi_data.mfi_value:.2f}, 信号={mfi_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算MFI开仓方向异常: {e}")
            return "hold"

    def detect_mfi_signal(self, highs: List[float], lows: List[float],
                         closes: List[float], volumes: List[float],
                         min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测MFI信号

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            volumes: 成交量列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(closes) < self.period:
                return False, "hold", 0.0

            # 计算MFI数据
            mfi_data = self.calculate_mfi(highs, lows, closes, volumes, min_periods)
            if not mfi_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_mfi_signal_direction(mfi_data)

            if direction != "hold":
                # 计算信号强度
                if mfi_data.signal in [MFISignal.EXTREME_OVERSOLD, MFISignal.EXTREME_OVERBOUGHT]:
                    signal_strength = 0.9  # 极值信号高强度
                elif mfi_data.signal in [MFISignal.OVERSOLD, MFISignal.OVERBOUGHT]:
                    signal_strength = 0.7  # 标准信号中等强度
                elif mfi_data.signal in [MFISignal.BULLISH_DIVERGENCE, MFISignal.BEARISH_DIVERGENCE]:
                    signal_strength = 0.6 + mfi_data.divergence_strength * 0.3  # 背离信号基于强度
                else:
                    signal_strength = 0.4  # 其他信号较低强度

                # 结合可靠性调整强度
                signal_strength *= mfi_data.reliability

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测MFI信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, highs: List[float], lows: List[float],
                           closes: List[float], volumes: List[float],
                           signal_type: str, confirmation_periods: int = 2) -> bool:
        """
        确认MFI信号的有效性

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            volumes: 成交量列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if len(closes) < confirmation_periods + 1:
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                if i == 0:
                    check_highs = highs
                    check_lows = lows
                    check_closes = closes
                    check_volumes = volumes
                else:
                    check_highs = highs[:-(i)]
                    check_lows = lows[:-(i)]
                    check_closes = closes[:-(i)]
                    check_volumes = volumes[:-(i)]

                has_signal, detected_type, strength = self.detect_mfi_signal(
                    check_highs, check_lows, check_closes, check_volumes)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ MFI信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ MFI信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认MFI信号异常: {e}")
            return False

class MFIStrategy:
    """资金流量指标策略主类"""

    def __init__(self, config: MFIConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = MFICalculator(period=config.period)

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"🎯 MFI策略初始化完成: {config}")

    async def start(self):
        """启动MFI策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ MFI策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ MFI策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 MFI策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ MFI策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止MFI策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 MFI策略已停止")

        except Exception as e:
            logger.error(f"❌ MFI策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动MFI立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据（包含成交量）
            price_data = await self._get_price_volume_data()
            if not price_data or len(price_data['closes']) < self.config.get_required_data_count():
                logger.warning("⚠️ 价格数据不足，无法进行MFI分析")
                return

            # 检测MFI信号
            has_signal, signal_type, signal_strength = self.calculator.detect_mfi_signal(
                price_data['highs'], price_data['lows'],
                price_data['closes'], price_data['volumes'])

            if not has_signal:
                logger.debug("未检测到MFI信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(
                    price_data['highs'], price_data['lows'],
                    price_data['closes'], price_data['volumes'],
                    signal_type, self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ MFI信号未确认: {signal_type}")
                    return

            # 信号过滤
            if not self._filter_signal(price_data, signal_type, signal_strength):
                logger.warning(f"⚠️ MFI信号被过滤: {signal_type}")
                return

            logger.info(f"🚀 准备执行MFI开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    def _filter_signal(self, price_data: Dict, signal_type: str, signal_strength: float) -> bool:
        """
        过滤MFI信号

        Args:
            price_data: 价格数据字典
            signal_type: 信号类型
            signal_strength: 信号强度

        Returns:
            bool: 信号是否通过过滤
        """
        try:
            # 计算MFI数据用于过滤
            mfi_data = self.calculator.calculate_mfi(
                price_data['highs'], price_data['lows'],
                price_data['closes'], price_data['volumes'])
            if not mfi_data:
                return False

            # 背离检测过滤
            if self.config.enable_divergence_detection:
                if mfi_data.divergence_strength < self.config.min_divergence_strength:
                    logger.debug(f"背离强度不足: {mfi_data.divergence_strength:.3f} < {self.config.min_divergence_strength}")
                    # 对于非背离信号，不需要背离强度
                    if mfi_data.signal in [MFISignal.BULLISH_DIVERGENCE, MFISignal.BEARISH_DIVERGENCE]:
                        return False

            # 趋势过滤
            if self.config.enable_trend_filter:
                if len(price_data['closes']) >= self.config.min_trend_periods:
                    # 检查趋势一致性
                    recent_trend = (price_data['closes'][-1] - price_data['closes'][-self.config.min_trend_periods]) / price_data['closes'][-self.config.min_trend_periods]

                    # 做多信号需要非强烈下跌趋势
                    if signal_type == "long" and recent_trend < -0.05:
                        logger.debug(f"做多信号但趋势下跌: {recent_trend:.3f}")
                        return False

                    # 做空信号需要非强烈上涨趋势
                    if signal_type == "short" and recent_trend > 0.05:
                        logger.debug(f"做空信号但趋势上涨: {recent_trend:.3f}")
                        return False

            return True

        except Exception as e:
            logger.error(f"过滤MFI信号异常: {e}")
            return False

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [MFI立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [MFI开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [MFI信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 [自定义设置] 交易对: {self.config.custom_symbol}, 杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [MFI参数] 周期: {self.config.period}, 超买: {self.config.overbought_threshold}, 超卖: {self.config.oversold_threshold}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [MFI立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'custom_symbol': self.config.custom_symbol,
                    'custom_leverage': self.config.custom_leverage,
                    'mfi_period': self.config.period,
                    'overbought_threshold': self.config.overbought_threshold,
                    'oversold_threshold': self.config.oversold_threshold
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [MFI立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [MFI立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.96 if direction == "long" else entry_price * 1.04  # 默认4%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")
            logger.info(f"🛡️ [止损订单] 交易对: {self.config.custom_symbol}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动MFI持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_volume_data(self) -> Dict:
        """获取价格和成交量数据"""
        try:
            if not self.exchange_manager:
                return {}

            # 这里实现价格和成交量数据获取逻辑
            return {
                'highs': [],
                'lows': [],
                'closes': [],
                'volumes': []
            }  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格成交量数据异常: {e}")
            return {}

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [MFI设置] 周期: {self.config.period}, 超买: {self.config.overbought_threshold}, 超卖: {self.config.oversold_threshold}")

            # 这里实现市价下单逻辑
            return True  # 临时返回

        except Exception as e:
            logger.error(f"❌ 市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
            "mfi_period": self.config.period,
            "overbought_threshold": self.config.overbought_threshold,
            "oversold_threshold": self.config.oversold_threshold,
        }
