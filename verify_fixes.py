#!/usr/bin/env python3
"""
快速验证并发控制修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_fixes():
    """验证修复"""
    print("🔧 验证并发控制修复")
    print("=" * 40)
    
    fixes = []
    
    # 检查1：AsyncTimer的clear_interval方法
    try:
        with open('utils/async_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'def clear_interval(self, task):' in content:
                fixes.append("✅ AsyncTimer.clear_interval方法已添加")
            else:
                fixes.append("❌ AsyncTimer.clear_interval方法缺失")
    except Exception as e:
        fixes.append(f"❌ 检查AsyncTimer失败: {e}")
    
    # 检查2：停止监控机制
    try:
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
            if '_opening_monitor_stopping' in content:
                fixes.append("✅ 停止标志_opening_monitor_stopping已添加")
            else:
                fixes.append("❌ 停止标志_opening_monitor_stopping缺失")
                
            if '_stop_opening_monitor()' in content and 'if success:' in content:
                fixes.append("✅ 成功下单后调用停止监控已添加")
            else:
                fixes.append("❌ 成功下单后停止监控逻辑缺失")
                
            if 'auto_opening_enabled = False' in content:
                fixes.append("✅ 禁用自动开仓逻辑已添加")
            else:
                fixes.append("❌ 禁用自动开仓逻辑缺失")
                
            if 'opening_records' in content and 'get_positions' in content:
                fixes.append("✅ 持仓预检查机制已添加")
            else:
                fixes.append("❌ 持仓预检查机制缺失")
                
    except Exception as e:
        fixes.append(f"❌ 检查主窗口失败: {e}")
    
    # 显示结果
    for fix in fixes:
        print(fix)
    
    success_count = sum(1 for fix in fixes if fix.startswith("✅"))
    total_count = len(fixes)
    
    print(f"\n📊 修复完成度: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count >= total_count * 0.8:
        print("🎉 并发控制问题修复基本完成！")
        return True
    else:
        print("⚠️ 还需要完善部分修复")
        return False

if __name__ == "__main__":
    success = verify_fixes()
    sys.exit(0 if success else 1)
