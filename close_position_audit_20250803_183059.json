{"total_issues": 3, "critical_issues": 1, "high_issues": 2, "medium_issues": 0, "issues_by_file": {"okx_exchange.py": ["ClosePositionIssue(severity='critical', issue_type='pos_side_logic_error', file_path='exchanges\\\\okx_exchange.py', line_number=997, method_name='unknown', code_snippet='order_data[\"posSide\"] = \"long\" if side == OrderSide.BUY else \"short\"', description='posSide参数设置逻辑错误，会导致平仓变成开仓', fix_suggestion='区分开仓和平仓，使用reduce_only参数', risk_level='极高')"], "bollinger_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\bollinger_strategy.py', line_number=1903, method_name='unknown', code_snippet='order_result = await exchange.place_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "opening_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\opening_strategy.py', line_number=330, method_name='unknown', code_snippet='order_info = await self.exchange.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"]}, "all_issues": ["ClosePositionIssue(severity='critical', issue_type='pos_side_logic_error', file_path='exchanges\\\\okx_exchange.py', line_number=997, method_name='unknown', code_snippet='order_data[\"posSide\"] = \"long\" if side == OrderSide.BUY else \"short\"', description='posSide参数设置逻辑错误，会导致平仓变成开仓', fix_suggestion='区分开仓和平仓，使用reduce_only参数', risk_level='极高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\bollinger_strategy.py', line_number=1903, method_name='unknown', code_snippet='order_result = await exchange.place_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\opening_strategy.py', line_number=330, method_name='unknown', code_snippet='order_info = await self.exchange.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"]}