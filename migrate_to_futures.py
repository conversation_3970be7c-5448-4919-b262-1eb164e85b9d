"""
配置迁移脚本：从现货交易对迁移到期货合约
自动将现有配置文件中的现货交易对转换为期货合约格式
"""
import sys
import os
import json
import shutil
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.config_manager import ConfigManager

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_section(title):
    """打印章节"""
    print(f"\n📊 {title}")
    print("-" * 40)

def backup_config_file(config_path):
    """备份配置文件"""
    try:
        if os.path.exists(config_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{config_path}.backup_{timestamp}"
            shutil.copy2(config_path, backup_path)
            print(f"✅ 配置文件已备份到: {backup_path}")
            return backup_path
        else:
            print("ℹ️ 配置文件不存在，无需备份")
            return None
    except Exception as e:
        print(f"❌ 备份配置文件失败: {e}")
        return None

def load_existing_config(config_path):
    """加载现有配置"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 成功加载现有配置文件")
            return config
        else:
            print("ℹ️ 配置文件不存在，将使用默认配置")
            return None
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return None

def migrate_symbol_format(config, config_manager):
    """迁移交易对格式"""
    if not config:
        return None
    
    print_section("交易对格式迁移")
    
    # 获取当前交易对和交易所
    current_symbol = config.get("symbol", "BTC/USDT")
    current_exchange = config.get("exchange", "okx")
    
    print(f"当前配置:")
    print(f"   交易对: {current_symbol}")
    print(f"   交易所: {current_exchange}")
    
    # 检查是否需要迁移
    if current_exchange == "okx" and "-SWAP" in current_symbol:
        print("✅ 交易对已经是期货合约格式，无需迁移")
        return config
    elif current_exchange in ["gateio", "gate"] and "_" in current_symbol and "/" not in current_symbol:
        print("✅ 交易对已经是期货合约格式，无需迁移")
        return config
    
    # 执行格式转换
    new_symbol = config_manager.convert_symbol_format(current_symbol, current_exchange)
    
    if new_symbol != current_symbol:
        print(f"🔄 迁移交易对格式:")
        print(f"   原格式: {current_symbol}")
        print(f"   新格式: {new_symbol}")
        
        # 更新配置
        config["symbol"] = new_symbol
        print("✅ 交易对格式迁移完成")
    else:
        print("ℹ️ 交易对格式无需更改")
    
    return config

def update_default_values(config, config_manager):
    """更新默认值"""
    print_section("默认值更新")
    
    if not config:
        config = {}
    
    # 获取最新的默认配置
    default_config = config_manager.default_config
    
    # 需要更新的字段
    updates = []
    
    # 检查并更新关键字段
    key_fields = {
        "symbol": "交易对",
        "bb_max_total_loss_percent": "布林带最大总亏损",
        "bb_max_investment_ratio": "布林带最大投入比例", 
        "bb_take_profit_percent": "布林带止盈百分比",
        "bb_stop_loss_percent": "布林带止损百分比"
    }
    
    for key, description in key_fields.items():
        if key in default_config:
            old_value = config.get(key)
            new_value = default_config[key]
            
            if old_value != new_value:
                config[key] = new_value
                updates.append((description, old_value, new_value))
    
    if updates:
        print("🔄 更新的默认值:")
        for description, old_val, new_val in updates:
            print(f"   {description}: {old_val} → {new_val}")
        print("✅ 默认值更新完成")
    else:
        print("ℹ️ 默认值无需更新")
    
    return config

def validate_migrated_config(config, config_manager):
    """验证迁移后的配置"""
    print_section("配置验证")
    
    if not config:
        print("❌ 配置为空，验证失败")
        return False
    
    try:
        # 验证基础配置
        is_valid, message = config_manager.validate_config(config)
        if not is_valid:
            print(f"❌ 基础配置验证失败: {message}")
            return False
        
        print("✅ 基础配置验证通过")
        
        # 验证交易对格式
        symbol = config.get("symbol")
        exchange = config.get("exchange")
        
        if symbol and exchange:
            # 创建模拟交易所实例进行验证
            if exchange == "okx":
                from exchanges.okx_exchange import OKXExchange
                exchange_instance = OKXExchange("test", "test", "test", sandbox=True)
            elif exchange in ["gateio", "gate"]:
                from exchanges.gateio_exchange import GateIOExchange
                exchange_instance = GateIOExchange("test", "test", sandbox=True)
            else:
                print(f"⚠️ 未知交易所: {exchange}")
                return True
            
            is_valid_symbol = exchange_instance.validate_symbol_format(symbol)
            if is_valid_symbol:
                print(f"✅ 交易对格式验证通过: {symbol}")
            else:
                print(f"❌ 交易对格式验证失败: {symbol}")
                return False
        
        print("✅ 配置验证全部通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证异常: {e}")
        return False

def save_migrated_config(config, config_path):
    """保存迁移后的配置"""
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 迁移后的配置已保存到: {config_path}")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def show_migration_summary(original_config, migrated_config):
    """显示迁移摘要"""
    print_section("迁移摘要")
    
    if not original_config:
        print("📋 这是首次配置，使用最新的期货合约默认值")
        return
    
    # 比较关键字段的变化
    key_fields = ["symbol", "exchange", "bb_max_total_loss_percent", 
                  "bb_take_profit_percent", "bb_stop_loss_percent"]
    
    changes = []
    for field in key_fields:
        old_val = original_config.get(field)
        new_val = migrated_config.get(field)
        if old_val != new_val:
            changes.append((field, old_val, new_val))
    
    if changes:
        print("📊 配置变更摘要:")
        for field, old_val, new_val in changes:
            print(f"   {field}: {old_val} → {new_val}")
    else:
        print("ℹ️ 配置无重大变更")
    
    print(f"\n✅ 迁移完成，系统已准备好使用期货合约交易")

def main():
    """主函数"""
    print_header("配置迁移：现货 → 期货合约")
    
    print("🎯 迁移目标:")
    print("   • 将现有配置中的现货交易对转换为期货合约格式")
    print("   • 更新相关的默认参数值")
    print("   • 确保配置与新的期货合约系统兼容")
    print("   • 备份原始配置以防需要回滚")
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    config_path = "config.json"  # 默认配置文件路径
    
    # 1. 备份现有配置
    backup_path = backup_config_file(config_path)
    
    # 2. 加载现有配置
    original_config = load_existing_config(config_path)
    
    # 3. 执行迁移
    migrated_config = migrate_symbol_format(original_config, config_manager)
    migrated_config = update_default_values(migrated_config, config_manager)
    
    # 4. 验证迁移结果
    if not validate_migrated_config(migrated_config, config_manager):
        print("\n❌ 配置验证失败，迁移中止")
        if backup_path and original_config:
            print(f"💡 可以从备份恢复: {backup_path}")
        return False
    
    # 5. 保存迁移后的配置
    if not save_migrated_config(migrated_config, config_path):
        print("\n❌ 保存配置失败，迁移中止")
        return False
    
    # 6. 显示迁移摘要
    show_migration_summary(original_config, migrated_config)
    
    print_header("迁移完成")
    
    print("🎉 配置迁移成功完成！")
    print("\n📋 迁移结果:")
    print("   ✅ 交易对已转换为期货合约格式")
    print("   ✅ 默认参数已更新为最新值")
    print("   ✅ 配置验证全部通过")
    print("   ✅ 系统已准备好进行期货合约交易")
    
    if backup_path:
        print(f"\n💾 备份信息:")
        print(f"   原始配置备份: {backup_path}")
        print(f"   如需回滚，请将备份文件重命名为 config.json")
    
    print(f"\n🚀 下一步:")
    print(f"   • 启动MACD智能加仓交易系统")
    print(f"   • 检查交易对和交易所设置")
    print(f"   • 配置API密钥和交易参数")
    print(f"   • 开始期货合约交易")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 迁移过程异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
