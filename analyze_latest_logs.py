#!/usr/bin/env python3
"""
最新日志分析工具 - 专门诊断立即开仓功能
"""

import sys
import os
import re
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_latest_log_lines(log_file_path: str, hours_back: int = 1) -> List[str]:
    """获取最近指定小时内的日志"""
    try:
        if not os.path.exists(log_file_path):
            return []
        
        # 计算时间范围
        now = datetime.now()
        start_time = now - timedelta(hours=hours_back)
        
        # 读取日志
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 过滤时间范围内的日志
        filtered_lines = []
        for line in lines:
            # 提取时间戳 (HH:MM:SS)
            time_match = re.search(r'(\d{2}:\d{2}:\d{2})', line)
            if time_match:
                time_str = time_match.group(1)
                try:
                    log_time = datetime.strptime(f"{now.strftime('%Y-%m-%d')} {time_str}", '%Y-%m-%d %H:%M:%S')
                    # 处理跨日情况
                    if log_time > now:
                        log_time = log_time - timedelta(days=1)
                    if log_time >= start_time:
                        filtered_lines.append(line.strip())
                except:
                    continue
        
        return filtered_lines[-200:]  # 最近200条
        
    except Exception as e:
        print(f"读取日志文件失败: {e}")
        return []

def analyze_immediate_open_status(log_lines: List[str]) -> Dict[str, Any]:
    """分析立即开仓状态"""
    status = {
        "monitor_started": False,
        "timer_created": False,
        "timer_running": False,
        "check_executed": False,
        "position_detected": False,
        "signal_analyzed": False,
        "balance_checked": False,
        "execution_attempted": False,
        "success": False,
        "errors": [],
        "warnings": [],
        "key_logs": []
    }
    
    # 关键日志模式
    patterns = {
        "monitor_started": r"🎯.*立即开仓.*监控已启动",
        "timer_created": r"定时器ID|AsyncTimer.*set_interval",
        "timer_running": r"定时器运行状态.*True",
        "check_executed": r"🔄.*立即开仓.*开始检查开仓机会",
        "position_detected": r"✅.*交易对无持仓.*自动开仓功能可用",
        "signal_analyzed": r"📊.*立即开仓.*%B指标",
        "balance_checked": r"余额验证|账户余额",
        "execution_attempted": r"🚀.*立即开仓.*正在执行开仓交易",
        "success": r"✅.*立即开仓.*开仓成功"
    }
    
    # 分析日志
    for line in log_lines:
        # 检查立即开仓相关日志
        if "立即开仓" in line:
            status["key_logs"].append(line)
        
        # 检查各种状态
        for key, pattern in patterns.items():
            if re.search(pattern, line, re.IGNORECASE):
                status[key] = True
        
        # 收集错误和警告
        if re.search(r"ERROR.*立即开仓|❌.*立即开仓", line):
            status["errors"].append(line)
        elif re.search(r"WARNING.*立即开仓|⚠️.*立即开仓", line):
            status["warnings"].append(line)
    
    return status

def analyze_balance_issues(log_lines: List[str]) -> Dict[str, Any]:
    """分析账户余额问题"""
    balance_info = {
        "balance_logs": [],
        "insufficient_balance": False,
        "balance_values": [],
        "margin_requirements": [],
        "validation_results": []
    }
    
    for line in log_lines:
        # 收集余额相关日志
        if re.search(r"余额|balance|保证金|margin", line, re.IGNORECASE):
            balance_info["balance_logs"].append(line)
        
        # 检查余额不足警告
        if re.search(r"余额不足|insufficient.*balance|账户余额不足", line, re.IGNORECASE):
            balance_info["insufficient_balance"] = True
        
        # 提取余额数值
        balance_match = re.search(r"可用余额[:\s]*(\d+\.?\d*)", line)
        if balance_match:
            balance_info["balance_values"].append({
                "value": float(balance_match.group(1)),
                "line": line
            })
        
        # 提取保证金要求
        margin_match = re.search(r"所需保证金[:\s]*(\d+\.?\d*)", line)
        if margin_match:
            balance_info["margin_requirements"].append({
                "value": float(margin_match.group(1)),
                "line": line
            })
        
        # 余额验证结果
        if re.search(r"余额.*充足|余额.*验证.*通过", line):
            balance_info["validation_results"].append({"result": "pass", "line": line})
        elif re.search(r"余额.*不足|余额.*验证.*失败", line):
            balance_info["validation_results"].append({"result": "fail", "line": line})
    
    return balance_info

def analyze_bollinger_signals(log_lines: List[str]) -> Dict[str, Any]:
    """分析布林带信号"""
    signal_info = {
        "percent_b_values": [],
        "signal_triggers": [],
        "band_data": [],
        "current_prices": [],
        "signal_decisions": []
    }
    
    for line in log_lines:
        # 提取%B指标
        percent_b_match = re.search(r'%B指标[=:\s]*(\d+\.?\d*)%', line)
        if percent_b_match:
            signal_info["percent_b_values"].append({
                "value": float(percent_b_match.group(1)),
                "line": line,
                "timestamp": extract_timestamp(line)
            })
        
        # 提取信号触发
        if re.search(r'触发.*做多信号|触发.*做空信号', line):
            signal_info["signal_triggers"].append(line)
        
        # 提取布林带数据
        band_match = re.search(r'上轨[=:\s]*(\d+\.?\d+).*中轨[=:\s]*(\d+\.?\d+).*下轨[=:\s]*(\d+\.?\d+)', line)
        if band_match:
            signal_info["band_data"].append({
                "upper": float(band_match.group(1)),
                "middle": float(band_match.group(2)),
                "lower": float(band_match.group(3)),
                "line": line
            })
        
        # 提取当前价格
        price_match = re.search(r'当前价格[=:\s]*(\d+\.?\d+)', line)
        if price_match:
            signal_info["current_prices"].append({
                "price": float(price_match.group(1)),
                "line": line
            })
        
        # 信号决策
        if re.search(r'中性区域|不开仓', line):
            signal_info["signal_decisions"].append({"decision": "hold", "line": line})
        elif re.search(r'做多信号|做空信号', line):
            signal_info["signal_decisions"].append({"decision": "open", "line": line})
    
    return signal_info

def extract_timestamp(line: str) -> Optional[str]:
    """提取时间戳"""
    time_match = re.search(r'(\d{2}:\d{2}:\d{2})', line)
    return time_match.group(1) if time_match else None

def generate_detailed_report(immediate_status: Dict, balance_info: Dict, signal_info: Dict) -> str:
    """生成详细报告"""
    report = []
    report.append("🔍 立即开仓功能最新日志深度分析报告")
    report.append("=" * 70)
    
    # 立即开仓状态分析
    report.append("\n📊 立即开仓监控状态:")
    status_items = [
        ("监控启动", immediate_status["monitor_started"]),
        ("定时器创建", immediate_status["timer_created"]),
        ("定时器运行", immediate_status["timer_running"]),
        ("检查执行", immediate_status["check_executed"]),
        ("持仓检测", immediate_status["position_detected"]),
        ("信号分析", immediate_status["signal_analyzed"]),
        ("余额检查", immediate_status["balance_checked"]),
        ("执行尝试", immediate_status["execution_attempted"]),
        ("执行成功", immediate_status["success"])
    ]
    
    for item, status in status_items:
        icon = "✅" if status else "❌"
        report.append(f"   {icon} {item}: {status}")
    
    # 账户余额分析
    report.append(f"\n💰 账户余额分析:")
    if balance_info["balance_values"]:
        latest_balance = balance_info["balance_values"][-1]
        report.append(f"   最新可用余额: {latest_balance['value']:.2f} USDT")
    
    if balance_info["margin_requirements"]:
        latest_margin = balance_info["margin_requirements"][-1]
        report.append(f"   所需保证金: {latest_margin['value']:.2f} USDT")
    
    if balance_info["insufficient_balance"]:
        report.append("   🚨 检测到余额不足警告")
    
    if balance_info["validation_results"]:
        latest_validation = balance_info["validation_results"][-1]
        result_icon = "✅" if latest_validation["result"] == "pass" else "❌"
        report.append(f"   {result_icon} 余额验证结果: {latest_validation['result']}")
    
    # 布林带信号分析
    report.append(f"\n📈 布林带信号分析:")
    if signal_info["percent_b_values"]:
        latest_percent_b = signal_info["percent_b_values"][-1]
        report.append(f"   最新%B指标: {latest_percent_b['value']:.1f}%")
        
        # 判断信号
        if latest_percent_b["value"] < 30:
            report.append("   🔵 应触发做多信号 (%B < 30%)")
        elif latest_percent_b["value"] > 70:
            report.append("   🔴 应触发做空信号 (%B > 70%)")
        else:
            report.append("   ⚪ 处于中性区域 (30% ≤ %B ≤ 70%)")
    
    if signal_info["signal_triggers"]:
        report.append(f"   📊 信号触发次数: {len(signal_info['signal_triggers'])}")
    
    # 关键日志摘要
    report.append(f"\n📋 关键日志摘要:")
    if immediate_status["key_logs"]:
        report.append("   🔍 立即开仓相关日志:")
        for log in immediate_status["key_logs"][-5:]:  # 最近5条
            report.append(f"      {log}")
    
    # 错误和警告
    if immediate_status["errors"]:
        report.append("   ❌ 错误日志:")
        for error in immediate_status["errors"][-3:]:
            report.append(f"      {error}")
    
    if immediate_status["warnings"]:
        report.append("   ⚠️ 警告日志:")
        for warning in immediate_status["warnings"][-3:]:
            report.append(f"      {warning}")
    
    # 余额相关日志
    if balance_info["balance_logs"]:
        report.append("   💰 余额相关日志:")
        for log in balance_info["balance_logs"][-3:]:
            report.append(f"      {log}")
    
    # 问题诊断
    report.append(f"\n🔧 问题诊断:")
    issues = []
    
    if not immediate_status["monitor_started"]:
        issues.append("立即开仓监控未启动")
    if not immediate_status["check_executed"]:
        issues.append("检查流程未执行")
    if balance_info["insufficient_balance"]:
        issues.append("账户余额不足")
    if not signal_info["signal_triggers"] and signal_info["percent_b_values"]:
        latest_b = signal_info["percent_b_values"][-1]["value"]
        if latest_b < 30 or latest_b > 70:
            issues.append("满足信号条件但未触发")
    
    if issues:
        for issue in issues:
            report.append(f"   🚨 {issue}")
    else:
        report.append("   ✅ 未发现明显问题")
    
    # 修复建议
    report.append(f"\n💡 修复建议:")
    if not immediate_status["monitor_started"]:
        report.append("   1. 重新勾选'无持仓时立即开仓'功能")
        report.append("   2. 检查异步定时器是否正常工作")
    
    if balance_info["insufficient_balance"]:
        report.append("   1. 检查账户实际余额")
        report.append("   2. 验证保证金计算是否正确")
        report.append("   3. 确认杠杆设置是否合理")
    
    if not immediate_status["check_executed"]:
        report.append("   1. 使用'立即检查'按钮手动测试")
        report.append("   2. 检查定时器是否正常运行")
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🔍 开始分析最新日志...")
    
    # 查找日志文件
    log_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".log"):
                log_files.append(os.path.join(root, file))
    
    if not log_files:
        print("❌ 未找到日志文件")
        return
    
    print(f"📁 找到 {len(log_files)} 个日志文件")
    
    # 收集最近1小时的日志
    all_lines = []
    for log_file in log_files:
        lines = get_latest_log_lines(log_file, hours_back=1)
        all_lines.extend(lines)
        print(f"📖 {log_file}: {len(lines)} 条相关日志")
    
    if not all_lines:
        print("❌ 未找到最近1小时的日志内容")
        return
    
    print(f"📊 总共分析 {len(all_lines)} 条最新日志")
    
    # 分析各个方面
    immediate_status = analyze_immediate_open_status(all_lines)
    balance_info = analyze_balance_issues(all_lines)
    signal_info = analyze_bollinger_signals(all_lines)
    
    # 生成报告
    report = generate_detailed_report(immediate_status, balance_info, signal_info)
    
    print("\n" + report)
    
    # 保存报告
    with open("latest_immediate_open_analysis.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 分析报告已保存到: latest_immediate_open_analysis.txt")

if __name__ == "__main__":
    main()
