#!/usr/bin/env python3
"""
检查异步语法错误的脚本
"""

import re
import sys

def check_async_syntax(file_path):
    """检查文件中的异步语法错误"""
    print(f"🔍 检查文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    errors = []
    current_method = None
    current_method_line = 0
    is_async_method = False
    
    for i, line in enumerate(lines, 1):
        # 检查方法定义
        method_match = re.match(r'\s*(async\s+)?def\s+(\w+)', line)
        if method_match:
            current_method = method_match.group(2)
            current_method_line = i
            is_async_method = method_match.group(1) is not None
            continue
        
        # 检查await调用
        if 'await ' in line and not line.strip().startswith('#'):
            if not is_async_method:
                errors.append({
                    'line': i,
                    'method': current_method,
                    'method_line': current_method_line,
                    'content': line.strip(),
                    'error': f"在非async方法 '{current_method}' 中使用了await"
                })
    
    return errors

def main():
    file_path = "strategies/bollinger_strategy.py"
    errors = check_async_syntax(file_path)
    
    if not errors:
        print("✅ 未发现异步语法错误")
        return True
    
    print(f"❌ 发现 {len(errors)} 个异步语法错误:")
    print("=" * 60)
    
    for error in errors:
        print(f"错误 #{error['line']}: {error['error']}")
        print(f"  方法定义: 第{error['method_line']}行")
        print(f"  错误代码: {error['content']}")
        print("-" * 40)
    
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
