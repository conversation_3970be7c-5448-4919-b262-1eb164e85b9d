#!/usr/bin/env python3
"""
紧急订单诊断工具
专门用于诊断订单ID 2741538988979052544 的真实状态
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmergencyOrderDiagnosis:
    """紧急订单诊断器"""
    
    def __init__(self):
        self.target_order_id = "2741538988979052544"
        self.symbol = "ETH/USDT"  # 根据日志推测
        self.diagnosis_results = {}
        
    async def run_diagnosis(self) -> Dict[str, Any]:
        """运行完整诊断"""
        print("🚨 紧急订单诊断开始")
        print("=" * 80)
        print(f"🎯 目标订单ID: {self.target_order_id}")
        print(f"📊 交易对: {self.symbol}")
        print(f"⏰ 诊断时间: {datetime.now()}")
        print("=" * 80)
        
        # 阶段1：连接交易所并查询订单
        await self._diagnose_order_status()
        
        # 阶段2：检查持仓状态
        await self._diagnose_position_status()
        
        # 阶段3：分析可能的问题原因
        await self._analyze_potential_issues()
        
        # 阶段4：生成诊断报告
        return self._generate_diagnosis_report()
    
    async def _diagnose_order_status(self):
        """诊断订单状态"""
        print("\n🔍 阶段1: 订单状态诊断")
        
        try:
            # 创建OKX交易所实例
            from exchanges.okx_exchange import OKXExchange
            from config import TradingConfig
            
            config = TradingConfig()
            exchange = OKXExchange(
                api_key=config.OKX_API_KEY,
                api_secret=config.OKX_API_SECRET,
                passphrase=config.OKX_PASSPHRASE,
                sandbox=config.SANDBOX
            )
            
            # 连接交易所
            await exchange.connect()
            
            if not exchange.is_connected:
                self.diagnosis_results['order_status'] = {
                    'status': 'error',
                    'error': '无法连接到OKX交易所'
                }
                return
            
            print("✅ 成功连接到OKX交易所")
            
            # 查询订单状态
            print(f"🔍 查询订单状态: {self.target_order_id}")
            
            try:
                order_info = await exchange.get_order_status(self.symbol, self.target_order_id)
                
                if order_info:
                    print(f"📊 订单信息:")
                    print(f"   订单ID: {order_info.order_id}")
                    print(f"   状态: {order_info.status}")
                    print(f"   方向: {order_info.side.value}")
                    print(f"   类型: {order_info.type.value}")
                    print(f"   数量: {order_info.amount}")
                    print(f"   价格: {order_info.price}")
                    print(f"   成交数量: {order_info.filled}")
                    print(f"   剩余数量: {order_info.remaining}")
                    print(f"   时间戳: {order_info.timestamp}")
                    
                    # 关键分析
                    is_fully_filled = order_info.filled >= order_info.amount
                    fill_percentage = (order_info.filled / order_info.amount * 100) if order_info.amount > 0 else 0
                    
                    print(f"\n🎯 关键分析:")
                    print(f"   完全成交: {'是' if is_fully_filled else '否'}")
                    print(f"   成交比例: {fill_percentage:.2f}%")
                    print(f"   状态解释: {self._explain_order_status(order_info.status)}")
                    
                    self.diagnosis_results['order_status'] = {
                        'status': 'success',
                        'order_info': {
                            'order_id': order_info.order_id,
                            'status': order_info.status,
                            'side': order_info.side.value,
                            'type': order_info.type.value,
                            'amount': order_info.amount,
                            'price': order_info.price,
                            'filled': order_info.filled,
                            'remaining': order_info.remaining,
                            'timestamp': order_info.timestamp
                        },
                        'analysis': {
                            'is_fully_filled': is_fully_filled,
                            'fill_percentage': fill_percentage,
                            'status_explanation': self._explain_order_status(order_info.status)
                        }
                    }
                    
                else:
                    print("❌ 无法获取订单信息")
                    self.diagnosis_results['order_status'] = {
                        'status': 'error',
                        'error': '无法获取订单信息'
                    }
                    
            except Exception as e:
                print(f"❌ 查询订单状态失败: {e}")
                self.diagnosis_results['order_status'] = {
                    'status': 'error',
                    'error': f'查询订单状态失败: {str(e)}'
                }
            
            # 断开连接
            await exchange.disconnect()
            
        except Exception as e:
            print(f"❌ 订单状态诊断异常: {e}")
            self.diagnosis_results['order_status'] = {
                'status': 'error',
                'error': f'诊断异常: {str(e)}'
            }
    
    async def _diagnose_position_status(self):
        """诊断持仓状态"""
        print("\n🔍 阶段2: 持仓状态诊断")
        
        try:
            # 创建OKX交易所实例
            from exchanges.okx_exchange import OKXExchange
            from config import TradingConfig
            
            config = TradingConfig()
            exchange = OKXExchange(
                api_key=config.OKX_API_KEY,
                api_secret=config.OKX_API_SECRET,
                passphrase=config.OKX_PASSPHRASE,
                sandbox=config.SANDBOX
            )
            
            # 连接交易所
            await exchange.connect()
            
            if not exchange.is_connected:
                self.diagnosis_results['position_status'] = {
                    'status': 'error',
                    'error': '无法连接到OKX交易所'
                }
                return
            
            # 查询持仓状态
            print(f"🔍 查询持仓状态: {self.symbol}")
            
            try:
                positions = await exchange.get_positions(self.symbol)
                
                if positions:
                    print(f"📊 发现 {len(positions)} 个持仓:")
                    
                    total_position_size = 0
                    for i, position in enumerate(positions, 1):
                        print(f"   持仓 {i}:")
                        print(f"     方向: {position.side.value}")
                        print(f"     数量: {position.size}")
                        print(f"     入场价: {position.entry_price}")
                        print(f"     标记价: {position.mark_price}")
                        print(f"     未实现盈亏: {position.unrealized_pnl}")
                        print(f"     杠杆: {position.leverage}x")
                        
                        total_position_size += abs(position.size)
                    
                    print(f"\n🎯 持仓分析:")
                    print(f"   总持仓数量: {total_position_size}")
                    print(f"   持仓状态: {'有持仓' if total_position_size > 0 else '无持仓'}")
                    
                    self.diagnosis_results['position_status'] = {
                        'status': 'success',
                        'has_positions': len(positions) > 0,
                        'total_positions': len(positions),
                        'total_size': total_position_size,
                        'positions': [
                            {
                                'side': pos.side.value,
                                'size': pos.size,
                                'entry_price': pos.entry_price,
                                'mark_price': pos.mark_price,
                                'unrealized_pnl': pos.unrealized_pnl,
                                'leverage': pos.leverage
                            } for pos in positions
                        ]
                    }
                    
                else:
                    print("✅ 无持仓")
                    self.diagnosis_results['position_status'] = {
                        'status': 'success',
                        'has_positions': False,
                        'total_positions': 0,
                        'total_size': 0,
                        'positions': []
                    }
                    
            except Exception as e:
                print(f"❌ 查询持仓状态失败: {e}")
                self.diagnosis_results['position_status'] = {
                    'status': 'error',
                    'error': f'查询持仓状态失败: {str(e)}'
                }
            
            # 断开连接
            await exchange.disconnect()
            
        except Exception as e:
            print(f"❌ 持仓状态诊断异常: {e}")
            self.diagnosis_results['position_status'] = {
                'status': 'error',
                'error': f'诊断异常: {str(e)}'
            }
    
    async def _analyze_potential_issues(self):
        """分析可能的问题原因"""
        print("\n🔍 阶段3: 问题原因分析")
        
        issues = []
        
        # 分析订单状态
        order_status = self.diagnosis_results.get('order_status', {})
        position_status = self.diagnosis_results.get('position_status', {})
        
        if order_status.get('status') == 'success':
            order_info = order_status.get('order_info', {})
            analysis = order_status.get('analysis', {})
            
            # 检查订单是否真正完全成交
            if not analysis.get('is_fully_filled', False):
                issues.append({
                    'type': 'critical',
                    'issue': '订单未完全成交',
                    'description': f"订单状态为{order_info.get('status')}，但成交比例仅为{analysis.get('fill_percentage', 0):.2f}%",
                    'impact': '平仓未完成，持仓仍然存在'
                })
            
            # 检查订单方向是否正确
            if order_info.get('side') not in ['sell', 'buy']:
                issues.append({
                    'type': 'critical',
                    'issue': '订单方向异常',
                    'description': f"订单方向为{order_info.get('side')}，可能不是有效的平仓方向",
                    'impact': '可能执行了错误的操作'
                })
        
        # 分析持仓状态
        if position_status.get('status') == 'success':
            if position_status.get('has_positions', False):
                issues.append({
                    'type': 'critical',
                    'issue': '持仓仍然存在',
                    'description': f"发现{position_status.get('total_positions', 0)}个持仓，总数量{position_status.get('total_size', 0)}",
                    'impact': '平仓操作未成功，资金仍有风险'
                })
        
        # 交叉验证
        if (order_status.get('status') == 'success' and 
            position_status.get('status') == 'success'):
            
            order_analysis = order_status.get('analysis', {})
            if (order_analysis.get('is_fully_filled', False) and 
                position_status.get('has_positions', False)):
                issues.append({
                    'type': 'critical',
                    'issue': '状态不一致',
                    'description': '订单显示完全成交，但持仓仍然存在',
                    'impact': '系统状态与实际状态不符，存在严重问题'
                })
        
        self.diagnosis_results['potential_issues'] = issues
        
        print(f"🎯 发现 {len(issues)} 个潜在问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. [{issue['type'].upper()}] {issue['issue']}")
            print(f"      描述: {issue['description']}")
            print(f"      影响: {issue['impact']}")
    
    def _explain_order_status(self, status: str) -> str:
        """解释订单状态"""
        status_explanations = {
            'live': '等待成交',
            'partially_filled': '部分成交',
            'filled': '完全成交',
            'canceled': '已撤销',
            'mmp_canceled': '做市商保护撤销',
            'rejected': '被拒绝',
            'failed': '失败'
        }
        return status_explanations.get(status, f'未知状态: {status}')
    
    def _generate_diagnosis_report(self) -> Dict[str, Any]:
        """生成诊断报告"""
        print("\n" + "=" * 80)
        print("📋 紧急诊断报告")
        print("=" * 80)
        
        # 总结关键发现
        critical_issues = [issue for issue in self.diagnosis_results.get('potential_issues', []) 
                          if issue['type'] == 'critical']
        
        print(f"🎯 诊断总结:")
        print(f"   订单ID: {self.target_order_id}")
        print(f"   关键问题: {len(critical_issues)} 个")
        
        if critical_issues:
            print(f"\n🚨 关键问题:")
            for i, issue in enumerate(critical_issues, 1):
                print(f"   {i}. {issue['issue']}")
                print(f"      {issue['description']}")
        
        # 建议的修复措施
        print(f"\n🔧 建议的修复措施:")
        if critical_issues:
            print("   1. 立即手动检查交易所账户实际持仓状态")
            print("   2. 如果持仓仍存在，手动执行平仓操作")
            print("   3. 检查订单执行逻辑，确保平仓方向正确")
            print("   4. 增加订单成交验证机制")
            print("   5. 实现持仓状态与订单状态的交叉验证")
        else:
            print("   未发现关键问题，但建议加强监控")
        
        return self.diagnosis_results

async def main():
    """主函数"""
    try:
        diagnosis = EmergencyOrderDiagnosis()
        results = await diagnosis.run_diagnosis()
        
        # 保存诊断结果
        with open(f'emergency_diagnosis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n✅ 诊断完成，结果已保存")
        return True
        
    except Exception as e:
        print(f"❌ 诊断异常: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(main())
