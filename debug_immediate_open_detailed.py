#!/usr/bin/env python3
"""
立即开仓功能详细调试脚本
"""

import sys
import os
import asyncio
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_position_detection():
    """测试持仓检测逻辑"""
    print("🔍 测试持仓检测逻辑")
    print("-" * 50)
    
    try:
        # 导入必要的模块
        from exchanges.okx_exchange import OKXExchange
        from config.config import Config
        
        # 创建配置和交易所实例
        config = Config()
        exchange = OKXExchange(config)
        
        # 连接交易所
        print("📡 连接交易所...")
        await exchange.connect()
        
        if not exchange.is_connected:
            print("❌ 交易所连接失败")
            return False
        
        print("✅ 交易所连接成功")
        
        # 测试获取持仓
        symbol = "BNB-USDT-SWAP"
        print(f"📊 获取 {symbol} 持仓信息...")
        
        positions = await exchange.get_positions(symbol)
        print(f"📋 持仓数量: {len(positions) if positions else 0}")
        
        if positions:
            for i, pos in enumerate(positions):
                print(f"   持仓 {i+1}: 方向={pos.side.value}, 数量={pos.size}, 入场价={pos.entry_price}")
                if abs(pos.size) > 0:
                    print(f"   ⚠️ 检测到非零持仓: {pos.size}")
                else:
                    print(f"   ✅ 持仓数量为0")
        else:
            print("   ✅ 无持仓信息")
        
        # 测试获取所有持仓
        print("\n📊 获取所有持仓信息...")
        all_positions = await exchange.get_positions()
        print(f"📋 总持仓数量: {len(all_positions) if all_positions else 0}")
        
        has_any_position = False
        if all_positions:
            for i, pos in enumerate(all_positions):
                if abs(pos.size) > 0:
                    print(f"   持仓 {i+1}: 交易对={pos.symbol}, 方向={pos.side.value}, 数量={pos.size}")
                    has_any_position = True
        
        if has_any_position:
            print("❌ 检测到其他交易对有持仓，这可能阻止立即开仓功能")
        else:
            print("✅ 所有交易对均无持仓")
        
        await exchange.disconnect()
        return not has_any_position
        
    except Exception as e:
        print(f"❌ 测试持仓检测失败: {e}")
        return False

async def test_immediate_open_conditions():
    """测试立即开仓条件"""
    print("\n🔍 测试立即开仓条件")
    print("-" * 50)
    
    try:
        from strategies.bollinger_strategy import BollingerBandStrategy, BollingerBandConfig
        from exchanges.okx_exchange import OKXExchange
        from config.config import Config
        
        # 创建实例
        config_obj = Config()
        exchange = OKXExchange(config_obj)
        bb_config = BollingerBandConfig()
        strategy = BollingerBandStrategy(bb_config)
        
        # 连接交易所
        await exchange.connect()
        
        if not exchange.is_connected:
            print("❌ 交易所连接失败")
            return False
        
        # 测试持仓状态检查
        symbol = "BNB-USDT-SWAP"
        print(f"📊 检查 {symbol} 持仓状态...")
        
        position_status = await strategy.check_position_status(exchange, symbol)
        
        print(f"📋 持仓状态结果:")
        print(f"   有持仓: {position_status['has_position']}")
        print(f"   持仓方向: {position_status['position_side']}")
        print(f"   持仓数量: {position_status['position_size']}")
        print(f"   本地记录: {position_status['local_records']}")
        print(f"   交易所验证: {position_status['exchange_verified']}")
        print(f"   冲突检测: {position_status['conflict_detected']}")
        print(f"   错误信息: {position_status['error']}")
        
        # 测试开仓方向验证
        if not position_status['has_position']:
            print("\n✅ 无持仓，测试开仓方向验证...")
            
            # 测试做多方向
            can_long, reason_long = strategy.validate_opening_direction("long", position_status)
            print(f"   做多验证: {can_long} - {reason_long}")
            
            # 测试做空方向
            can_short, reason_short = strategy.validate_opening_direction("short", position_status)
            print(f"   做空验证: {can_short} - {reason_short}")
            
            if can_long and can_short:
                print("✅ 开仓方向验证通过")
                await exchange.disconnect()
                return True
            else:
                print("❌ 开仓方向验证失败")
        else:
            print("❌ 检测到持仓，无法进行立即开仓")
        
        await exchange.disconnect()
        return False
        
    except Exception as e:
        print(f"❌ 测试立即开仓条件失败: {e}")
        return False

async def test_bollinger_signals():
    """测试布林带信号"""
    print("\n🔍 测试布林带信号")
    print("-" * 50)
    
    try:
        from strategies.bollinger_strategy import BollingerBandStrategy, BollingerBandConfig
        from exchanges.okx_exchange import OKXExchange
        from config.config import Config
        
        # 创建实例
        config_obj = Config()
        exchange = OKXExchange(config_obj)
        bb_config = BollingerBandConfig()
        strategy = BollingerBandStrategy(bb_config)
        
        # 连接交易所
        await exchange.connect()
        
        if not exchange.is_connected:
            print("❌ 交易所连接失败")
            return False
        
        # 获取当前价格
        symbol = "BNB-USDT-SWAP"
        current_price = await exchange.get_current_price(symbol)
        print(f"💰 当前价格: {current_price}")
        
        # 模拟收集价格数据
        print("📊 模拟收集价格数据...")
        for i in range(20):
            # 模拟价格数据
            price = current_price + (i - 10) * 0.1
            strategy.price_history.append(price)
        
        print(f"📈 价格历史数据: {len(strategy.price_history)} 个点")
        
        # 计算布林带（异步版本）
        bb_history = await strategy.get_bb_history_copy()
        bb_data = await strategy.calculate_bollinger_bands(strategy.price_history, bb_history=bb_history)
        
        if bb_data:
            print(f"📊 布林带数据:")
            print(f"   上轨: {bb_data.upper_band:.4f}")
            print(f"   中轨: {bb_data.middle_band:.4f}")
            print(f"   下轨: {bb_data.lower_band:.4f}")
            
            # 测试立即开仓方向判断
            direction = strategy._determine_immediate_opening_direction(current_price, bb_data)
            
            if direction:
                print(f"🎯 立即开仓信号: {direction}")
                await exchange.disconnect()
                return True
            else:
                print("⚪ 当前处于中性区域，无立即开仓信号")
        else:
            print("❌ 布林带计算失败")
        
        await exchange.disconnect()
        return False
        
    except Exception as e:
        print(f"❌ 测试布林带信号失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🔍 测试GUI集成")
    print("-" * 50)
    
    try:
        import inspect
        from gui.main_window import MainWindow
        
        # 检查关键方法
        methods_to_check = [
            '_start_immediate_open_monitor',
            '_stop_immediate_open_monitor', 
            '_check_immediate_open_opportunity',
            '_execute_immediate_opening',
            'manual_trigger_immediate_check',
            'check_immediate_open_status',
            'force_restart_immediate_monitor'
        ]
        
        all_methods_exist = True
        for method_name in methods_to_check:
            if hasattr(MainWindow, method_name):
                print(f"✅ {method_name}: 方法存在")
            else:
                print(f"❌ {method_name}: 方法不存在")
                all_methods_exist = False
        
        # 检查启动逻辑
        if hasattr(MainWindow, '_start_immediate_open_monitor'):
            method_source = inspect.getsource(MainWindow._start_immediate_open_monitor)
            
            logic_checks = [
                ('AsyncTimer' in method_source, 'AsyncTimer导入'),
                ('set_interval' in method_source, '设置定时器'),
                ('监控已启动' in method_source, '启动成功日志'),
                ('定时器创建失败' in method_source, '失败处理'),
            ]
            
            for result, description in logic_checks:
                status = "✅" if result else "❌"
                print(f"{status} {description}: {result}")
                if not result:
                    all_methods_exist = False
        
        return all_methods_exist
        
    except Exception as e:
        print(f"❌ 测试GUI集成失败: {e}")
        return False

async def main():
    """主函数"""
    print("🧪 立即开仓功能详细调试")
    print("=" * 60)
    
    # 运行所有测试
    test_results = {}
    
    # 测试1：持仓检测
    test_results["持仓检测"] = await test_position_detection()
    
    # 测试2：立即开仓条件
    test_results["立即开仓条件"] = await test_immediate_open_conditions()
    
    # 测试3：布林带信号
    test_results["布林带信号"] = await test_bollinger_signals()
    
    # 测试4：GUI集成
    test_results["GUI集成"] = test_gui_integration()
    
    # 统计结果
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print("\n" + "=" * 60)
    print("📊 详细调试结果:")
    print(f"   总测试项: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    print()
    
    # 详细结果
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print()
    
    # 问题分析
    if not test_results.get("持仓检测", False):
        print("🚨 关键问题: 持仓检测失败")
        print("   可能原因:")
        print("   1. 其他交易对有持仓")
        print("   2. 交易所API返回异常")
        print("   3. 持仓检测逻辑过于严格")
    
    if not test_results.get("立即开仓条件", False):
        print("🚨 关键问题: 立即开仓条件不满足")
        print("   可能原因:")
        print("   1. 持仓状态检查失败")
        print("   2. 开仓方向验证失败")
        print("   3. 策略状态异常")
    
    if not test_results.get("布林带信号", False):
        print("🚨 关键问题: 布林带信号异常")
        print("   可能原因:")
        print("   1. 价格数据不足")
        print("   2. %B指标处于中性区域")
        print("   3. 布林带计算失败")
    
    if not test_results.get("GUI集成", False):
        print("🚨 关键问题: GUI集成异常")
        print("   可能原因:")
        print("   1. 关键方法缺失")
        print("   2. 定时器创建失败")
        print("   3. 异步架构问题")
    
    print("\n💡 建议的修复步骤:")
    print("   1. 检查所有交易对的持仓状态")
    print("   2. 修复持仓检测逻辑（只检查当前交易对）")
    print("   3. 验证立即开仓监控启动流程")
    print("   4. 确认%B指标计算和信号判断")
    print("   5. 测试定时器和异步执行机制")

if __name__ == "__main__":
    asyncio.run(main())
