{"total_issues": 19, "critical_issues": 2, "high_issues": 17, "medium_issues": 0, "issues_by_file": {"okx_exchange.py": ["ClosePositionIssue(severity='critical', issue_type='pos_side_logic_error', file_path='exchanges\\\\okx_exchange.py', line_number=343, method_name='unknown', code_snippet='order_data[\"posSide\"] = \"long\" if side == OrderSide.BUY else \"short\"', description='posSide参数设置逻辑错误，会导致平仓变成开仓', fix_suggestion='区分开仓和平仓，使用reduce_only参数', risk_level='极高')", "ClosePositionIssue(severity='critical', issue_type='pos_side_logic_error', file_path='exchanges\\\\okx_exchange.py', line_number=1006, method_name='unknown', code_snippet='order_data[\"posSide\"] = \"long\" if side == OrderSide.BUY else \"short\"', description='posSide参数设置逻辑错误，会导致平仓变成开仓', fix_suggestion='区分开仓和平仓，使用reduce_only参数', risk_level='极高')"], "adx_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\adx_strategy.py', line_number=1317, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "bollinger_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\bollinger_strategy.py', line_number=1874, method_name='unknown', code_snippet='order_result = await exchange.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "fibonacci_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\fibonacci_strategy.py', line_number=1669, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "ichimoku_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\ichimoku_strategy.py', line_number=339, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\ichimoku_strategy.py', line_number=570, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "kdj_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\kdj_strategy.py', line_number=1157, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "macd_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\macd_strategy.py', line_number=1025, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\macd_strategy.py', line_number=1162, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "ma_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\ma_strategy.py', line_number=1195, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "mfi_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\mfi_strategy.py', line_number=1295, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "obv_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\obv_strategy.py', line_number=1338, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "opening_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\opening_strategy.py', line_number=330, method_name='unknown', code_snippet='order_info = await self.exchange.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "pinbar_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\pinbar_strategy.py', line_number=342, method_name='_place_market_order', code_snippet='async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\pinbar_strategy.py', line_number=363, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\pinbar_strategy.py', line_number=589, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "rsi_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\rsi_strategy.py', line_number=1033, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"], "williams_strategy.py": ["ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\williams_strategy.py', line_number=1454, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"]}, "all_issues": ["ClosePositionIssue(severity='critical', issue_type='pos_side_logic_error', file_path='exchanges\\\\okx_exchange.py', line_number=343, method_name='unknown', code_snippet='order_data[\"posSide\"] = \"long\" if side == OrderSide.BUY else \"short\"', description='posSide参数设置逻辑错误，会导致平仓变成开仓', fix_suggestion='区分开仓和平仓，使用reduce_only参数', risk_level='极高')", "ClosePositionIssue(severity='critical', issue_type='pos_side_logic_error', file_path='exchanges\\\\okx_exchange.py', line_number=1006, method_name='unknown', code_snippet='order_data[\"posSide\"] = \"long\" if side == OrderSide.BUY else \"short\"', description='posSide参数设置逻辑错误，会导致平仓变成开仓', fix_suggestion='区分开仓和平仓，使用reduce_only参数', risk_level='极高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\adx_strategy.py', line_number=1317, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\bollinger_strategy.py', line_number=1874, method_name='unknown', code_snippet='order_result = await exchange.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\fibonacci_strategy.py', line_number=1669, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\ichimoku_strategy.py', line_number=339, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\ichimoku_strategy.py', line_number=570, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\kdj_strategy.py', line_number=1157, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\macd_strategy.py', line_number=1025, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\macd_strategy.py', line_number=1162, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\ma_strategy.py', line_number=1195, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\mfi_strategy.py', line_number=1295, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\obv_strategy.py', line_number=1338, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\opening_strategy.py', line_number=330, method_name='unknown', code_snippet='order_info = await self.exchange.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\pinbar_strategy.py', line_number=342, method_name='_place_market_order', code_snippet='async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\pinbar_strategy.py', line_number=363, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\pinbar_strategy.py', line_number=589, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\rsi_strategy.py', line_number=1033, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')", "ClosePositionIssue(severity='high', issue_type='missing_reduce_only', file_path='strategies\\\\williams_strategy.py', line_number=1454, method_name='unknown', code_snippet='order_result = await self.exchange_manager.place_market_order(', description='平仓调用缺少reduce_only=True参数', fix_suggestion='在place_order调用中添加reduce_only=True', risk_level='高')"]}