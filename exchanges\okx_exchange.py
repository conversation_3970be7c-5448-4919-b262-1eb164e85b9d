"""
OKX交易所异步适配器
严格遵守交易所完全分离原则
"""
import asyncio
import aiohttp
import hmac
import hashlib
import base64
import json
import time
import threading
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging
import websockets

from .base_exchange import (
    BaseExchange, KlineData, PositionInfo, OrderInfo, 
    OrderSide, OrderType, PositionSide
)

logger = logging.getLogger(__name__)

def _safe_log_params(params: dict) -> dict:
    """
    安全地记录参数，过滤敏感信息

    Args:
        params: 原始参数字典

    Returns:
        dict: 过滤敏感信息后的参数字典
    """
    safe_params = params.copy()

    # 敏感字段列表
    sensitive_fields = [
        'apiKey', 'api_key', 'sign', 'signature', 'passphrase',
        'secret', 'password', 'token', 'auth', 'key'
    ]

    # 过滤敏感信息
    for field in sensitive_fields:
        if field in safe_params:
            safe_params[field] = "***HIDDEN***"

    # 对于嵌套字典也进行过滤
    for key, value in safe_params.items():
        if isinstance(value, dict):
            safe_params[key] = _safe_log_params(value)

    return safe_params


# 移除SimpleAsyncRunner类，不再使用多线程


class OKXExchange(BaseExchange):
    """OKX交易所异步适配器"""
    
    def __init__(self, api_key: str, api_secret: str, passphrase: str, sandbox: bool = True):
        """
        初始化OKX交易所连接
        
        Args:
            api_key: API密钥
            api_secret: API密钥
            passphrase: API密码短语
            sandbox: 是否使用沙盒环境
        """
        super().__init__(api_key, api_secret, passphrase, sandbox)
        
        # OKX API端点
        if sandbox:
            self.base_url = "https://www.okx.com"
            self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
            self.ws_private_url = "wss://ws.okx.com:8443/ws/v5/private"
        else:
            self.base_url = "https://www.okx.com"
            self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
            self.ws_private_url = "wss://ws.okx.com:8443/ws/v5/private"
        
        self._price_callbacks = {}
        self._position_callbacks = []
        
    def _generate_signature(self, timestamp: str, method: str, request_path: str, body: str = "") -> str:
        """生成OKX API签名"""
        message = timestamp + method + request_path + body
        mac = hmac.new(
            bytes(self.api_secret, encoding='utf8'),
            bytes(message, encoding='utf-8'),
            digestmod=hashlib.sha256
        )
        return base64.b64encode(mac.digest()).decode()
    
    def _get_headers(self, method: str, request_path: str, body: str = "") -> Dict[str, str]:
        """获取请求头"""
        # OKX要求的时间戳格式：ISO8601格式，精确到毫秒
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        signature = self._generate_signature(timestamp, method, request_path, body)
        
        return {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }
    
    async def connect(self) -> bool:
        """建立异步连接"""
        try:
            # 创建新的会话，使用连接器配置
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self._session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )

            # 测试连接
            headers = self._get_headers('GET', '/api/v5/account/balance')
            async with self._session.get(
                f"{self.base_url}/api/v5/account/balance",
                headers=headers
            ) as response:
                if response.status == 200:
                    self._is_connected = True
                    logger.info("OKX连接成功")
                    return True
                else:
                    logger.error(f"OKX连接失败: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"OKX连接异常: {e}")
            return False
    
    async def disconnect(self) -> None:
        """断开连接"""
        try:
            if self._session and not self._session.closed:
                await self._session.close()
                # 等待连接器完全关闭
                await asyncio.sleep(0.1)

            if self._ws_connection:
                await self._ws_connection.close()

            self._is_connected = False
            logger.info("OKX连接已断开")

        except Exception as e:
            logger.error(f"OKX断开连接异常: {e}")
            self._is_connected = False

    # 移除_run_async_safe方法，不再使用多线程包装
    
    def get_supported_timeframes(self) -> List[str]:
        """获取OKX支持的时间周期列表"""
        return ["1m", "5m", "10m", "15m", "30m", "1h", "4h", "1d", "15d", "1M"]

    async def get_klines(self, symbol: str, timeframe: str, limit: int = 100) -> List[KlineData]:
        """获取K线数据"""
        try:
            # 验证时间周期
            if not self.validate_timeframe(timeframe):
                logger.error(f"OKX不支持的时间周期: {timeframe}")
                return []

            # 转换时间周期格式 - OKX API格式映射
            tf_map = {
                '1m': '1m',     # 1分钟
                '5m': '5m',     # 5分钟
                '10m': '10m',   # 10分钟
                '15m': '15m',   # 15分钟
                '30m': '30m',   # 30分钟
                '1h': '1H',     # 1小时
                '4h': '4H',     # 4小时
                '1d': '1D',     # 1日
                '15d': '15D',   # 15日
                '1M': '1M'      # 1月
            }
            okx_timeframe = tf_map.get(timeframe, '30m')

            logger.debug(f"OKX时间周期转换: {timeframe} -> {okx_timeframe}")
            
            # 转换交易对格式
            okx_symbol = symbol.replace('/', '-')
            
            params = {
                'instId': okx_symbol,
                'bar': okx_timeframe,
                'limit': str(limit)
            }

            # 构建包含查询参数的请求路径用于签名
            request_path = f'/api/v5/market/candles?instId={okx_symbol}&bar={okx_timeframe}&limit={limit}'
            headers = self._get_headers('GET', request_path)
            async with self._session.get(
                f"{self.base_url}/api/v5/market/candles",
                headers=headers,
                params=params
            ) as response:
                data = await response.json()
                
                if data['code'] == '0':
                    klines = []
                    for item in data['data']:
                        klines.append(KlineData(
                            timestamp=int(item[0]),
                            open=float(item[1]),
                            high=float(item[2]),
                            low=float(item[3]),
                            close=float(item[4]),
                            volume=float(item[5])
                        ))
                    return klines
                else:
                    logger.error(f"获取K线数据失败: {data}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取K线数据异常: {e}")
            return []
    
    async def _get_current_price_async(self, symbol: str) -> float:
        """异步获取当前价格"""
        try:
            # 检查会话状态
            if not self._session or self._session.closed:
                logger.error("会话已关闭，无法获取价格")
                return 0.0

            okx_symbol = symbol.replace('/', '-')

            # 构建包含查询参数的请求路径用于签名
            request_path = f'/api/v5/market/ticker?instId={okx_symbol}'
            headers = self._get_headers('GET', request_path)
            async with self._session.get(
                f"{self.base_url}/api/v5/market/ticker",
                headers=headers,
                params={'instId': okx_symbol}
            ) as response:
                data = await response.json()

                if data['code'] == '0' and data['data']:
                    return float(data['data'][0]['last'])
                else:
                    logger.error(f"获取价格失败: {data}")
                    return 0.0

        except Exception as e:
            logger.error(f"获取价格异常: {e}")
            return 0.0

    async def get_current_price(self, symbol: str) -> float:
        """获取当前价格（异步版本）"""
        return await self._get_current_price_async(symbol)

    # 移除同步版本，只保留异步版本
    
    async def get_positions(self, symbol: Optional[str] = None) -> List[PositionInfo]:
        """获取持仓信息"""
        try:
            # 构建请求路径，包含查询参数用于签名
            request_path = '/api/v5/account/positions'
            params = {}
            if symbol:
                instId = symbol.replace('/', '-')
                params['instId'] = instId
                request_path += f'?instId={instId}'

            headers = self._get_headers('GET', request_path)
            async with self._session.get(
                f"{self.base_url}/api/v5/account/positions",
                headers=headers,
                params=params
            ) as response:
                data = await response.json()
                
                if data['code'] == '0':
                    positions = []
                    for item in data['data']:
                        if float(item['pos']) != 0:  # 只返回有持仓的
                            positions.append(PositionInfo(
                                symbol=item['instId'].replace('-', '/'),
                                side=PositionSide.LONG if item['posSide'] == 'long' else PositionSide.SHORT,
                                size=float(item['pos']),
                                entry_price=float(item['avgPx']) if item['avgPx'] else 0.0,
                                mark_price=float(item['markPx']) if item['markPx'] else 0.0,
                                unrealized_pnl=float(item['upl']) if item['upl'] else 0.0,
                                liquidation_price=float(item['liqPx']) if item['liqPx'] else 0.0,
                                margin=float(item['margin']) if item['margin'] else 0.0,
                                leverage=int(float(item['lever'])) if item['lever'] else 1
                            ))
                    return positions
                else:
                    # 特殊处理API签名错误
                    if data.get('code') == '50113':
                        logger.warning(f"API签名验证失败，请检查API配置: {data}")
                    else:
                        logger.error(f"获取持仓失败: {data}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取持仓异常: {e}")
            return []
    
    async def place_market_order(self, symbol: str, side: OrderSide, amount: float, 
                                position_side: Optional[PositionSide] = None) -> OrderInfo:
        """下市价单"""
        try:
            okx_symbol = symbol.replace('/', '-')
            
            order_data = {
                'instId': okx_symbol,
                'tdMode': 'cross',  # 全仓模式
                'side': side.value,
                'ordType': 'market',
                'sz': str(amount)
            }
            
            if position_side:
                order_data['posSide'] = position_side.value
            
            body = json.dumps(order_data)
            headers = self._get_headers('POST', '/api/v5/trade/order', body)
            
            async with self._session.post(
                f"{self.base_url}/api/v5/trade/order",
                headers=headers,
                data=body
            ) as response:
                data = await response.json()
                
                if data['code'] == '0' and data['data']:
                    order_info = data['data'][0]
                    return OrderInfo(
                        order_id=order_info['ordId'],
                        symbol=symbol,
                        side=side,
                        type=OrderType.MARKET,
                        amount=amount,
                        price=None,
                        status=order_info['sCode'],
                        filled=0.0,
                        remaining=amount,
                        timestamp=int(time.time() * 1000)
                    )
                else:
                    logger.error(f"下单失败: {data}")
                    raise Exception(f"下单失败: {data}")
                    
        except Exception as e:
            logger.error(f"下单异常: {e}")
            raise e

    async def place_limit_order(self, symbol: str, side: OrderSide, amount: float,
                               price: float, position_side: Optional[PositionSide] = None) -> OrderInfo:
        """下限价单"""
        try:
            okx_symbol = symbol.replace('/', '-')

            order_data = {
                'instId': okx_symbol,
                'tdMode': 'cross',
                'side': side.value,
                'ordType': 'limit',
                'sz': str(amount),
                'px': str(price)
            }

            if position_side:
                order_data['posSide'] = position_side.value

            body = json.dumps(order_data)
            headers = self._get_headers('POST', '/api/v5/trade/order', body)

            async with self._session.post(
                f"{self.base_url}/api/v5/trade/order",
                headers=headers,
                data=body
            ) as response:
                data = await response.json()

                if data['code'] == '0' and data['data']:
                    order_info = data['data'][0]
                    return OrderInfo(
                        order_id=order_info['ordId'],
                        symbol=symbol,
                        side=side,
                        type=OrderType.LIMIT,
                        amount=amount,
                        price=price,
                        status=order_info['sCode'],
                        filled=0.0,
                        remaining=amount,
                        timestamp=int(time.time() * 1000)
                    )
                else:
                    logger.error(f"下限价单失败: {data}")
                    raise Exception(f"下限价单失败: {data}")

        except Exception as e:
            logger.error(f"下限价单异常: {e}")
            raise e

    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """取消订单"""
        try:
            okx_symbol = symbol.replace('/', '-')

            order_data = {
                'instId': okx_symbol,
                'ordId': order_id
            }

            body = json.dumps(order_data)
            headers = self._get_headers('POST', '/api/v5/trade/cancel-order', body)

            async with self._session.post(
                f"{self.base_url}/api/v5/trade/cancel-order",
                headers=headers,
                data=body
            ) as response:
                data = await response.json()
                return data['code'] == '0'

        except Exception as e:
            logger.error(f"取消订单异常: {e}")
            return False

    async def get_order_status(self, symbol: str, order_id: str) -> OrderInfo:
        """获取订单状态"""
        try:
            okx_symbol = symbol.replace('/', '-')

            params = {
                'instId': okx_symbol,
                'ordId': order_id
            }

            # 对于GET请求，查询参数需要包含在签名的request_path中
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            request_path = f'/api/v5/trade/order?{query_string}'

            headers = self._get_headers('GET', request_path)
            async with self._session.get(
                f"{self.base_url}/api/v5/trade/order",
                headers=headers,
                params=params
            ) as response:
                data = await response.json()

                if data['code'] == '0' and data['data']:
                    order_info = data['data'][0]
                    return OrderInfo(
                        order_id=order_info['ordId'],
                        symbol=symbol,
                        side=OrderSide.BUY if order_info['side'] == 'buy' else OrderSide.SELL,
                        type=OrderType.MARKET if order_info['ordType'] == 'market' else OrderType.LIMIT,
                        amount=float(order_info['sz']),
                        price=float(order_info['px']) if order_info['px'] else None,
                        status=order_info['state'],
                        filled=float(order_info['fillSz']) if order_info['fillSz'] else 0.0,
                        remaining=float(order_info['sz']) - float(order_info['fillSz']) if order_info['fillSz'] else float(order_info['sz']),
                        timestamp=int(order_info['cTime'])
                    )
                else:
                    logger.error(f"获取订单状态失败: {data}")
                    raise Exception(f"获取订单状态失败: {data}")

        except Exception as e:
            logger.error(f"获取订单状态异常: {e}")
            raise e

    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆"""
        try:
            okx_symbol = symbol.replace('/', '-')

            order_data = {
                'instId': okx_symbol,
                'lever': str(leverage),
                'mgnMode': 'cross'
            }

            body = json.dumps(order_data)
            headers = self._get_headers('POST', '/api/v5/account/set-leverage', body)

            async with self._session.post(
                f"{self.base_url}/api/v5/account/set-leverage",
                headers=headers,
                data=body
            ) as response:
                data = await response.json()
                return data['code'] == '0'

        except Exception as e:
            logger.error(f"设置杠杆异常: {e}")
            return False

    async def get_account_balance(self) -> Dict[str, float]:
        """获取账户余额"""
        try:
            headers = self._get_headers('GET', '/api/v5/account/balance')
            async with self._session.get(
                f"{self.base_url}/api/v5/account/balance",
                headers=headers
            ) as response:
                data = await response.json()

                if data['code'] == '0' and data['data']:
                    balances = {}
                    for account in data['data']:
                        for detail in account['details']:
                            currency = detail['ccy']
                            balance = float(detail['availBal']) if detail['availBal'] else 0.0
                            balances[currency] = balance
                    return balances
                else:
                    logger.error(f"获取账户余额失败: {data}")
                    return {}

        except Exception as e:
            logger.error(f"获取账户余额异常: {e}")
            return {}

    async def subscribe_price_stream(self, symbol: str, callback) -> None:
        """订阅价格流"""
        try:
            okx_symbol = symbol.replace('/', '-')

            async def price_handler():
                uri = self.ws_url
                async with websockets.connect(uri) as websocket:
                    # 订阅ticker数据
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [{"channel": "tickers", "instId": okx_symbol}]
                    }
                    await websocket.send(json.dumps(subscribe_msg))

                    async for message in websocket:
                        data = json.loads(message)
                        if 'data' in data:
                            for item in data['data']:
                                if 'last' in item:
                                    price = float(item['last'])
                                    await callback(symbol, price)

            # 启动价格监听任务
            task = asyncio.create_task(price_handler())
            self.add_background_task(task)

        except Exception as e:
            logger.error(f"订阅价格流异常: {e}")

    async def subscribe_position_stream(self, callback) -> None:
        """订阅持仓流"""
        try:
            async def position_handler():
                uri = self.ws_private_url

                # 生成认证信息
                timestamp = str(int(time.time()))
                message = timestamp + 'GET' + '/users/self/verify'
                signature = self._generate_signature(timestamp, 'GET', '/users/self/verify')

                auth_msg = {
                    "op": "login",
                    "args": [{
                        "apiKey": self.api_key,
                        "passphrase": self.passphrase,
                        "timestamp": timestamp,
                        "sign": signature
                    }]
                }

                async with websockets.connect(uri) as websocket:
                    # 认证
                    await websocket.send(json.dumps(auth_msg))

                    # 订阅持仓数据
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [{"channel": "positions", "instType": "SWAP"}]
                    }
                    await websocket.send(json.dumps(subscribe_msg))

                    async for message in websocket:
                        data = json.loads(message)
                        if 'data' in data:
                            positions = []
                            for item in data['data']:
                                if float(item['pos']) != 0:
                                    position = PositionInfo(
                                        symbol=item['instId'].replace('-', '/'),
                                        side=PositionSide.LONG if item['posSide'] == 'long' else PositionSide.SHORT,
                                        size=float(item['pos']),
                                        entry_price=float(item['avgPx']) if item['avgPx'] else 0.0,
                                        mark_price=float(item['markPx']) if item['markPx'] else 0.0,
                                        unrealized_pnl=float(item['upl']) if item['upl'] else 0.0,
                                        liquidation_price=float(item['liqPx']) if item['liqPx'] else 0.0,
                                        margin=float(item['margin']) if item['margin'] else 0.0,
                                        leverage=int(float(item['lever'])) if item['lever'] else 1
                                    )
                                    positions.append(position)

                            if positions:
                                await callback(positions)

            # 启动持仓监听任务
            task = asyncio.create_task(position_handler())
            self.add_background_task(task)

        except Exception as e:
            logger.error(f"订阅持仓流异常: {e}")

    def normalize_symbol(self, symbol: str) -> str:
        """
        标准化OKX交易对符号格式

        Args:
            symbol: 原始交易对符号

        Returns:
            str: OKX格式的交易对符号 (如: BTC-USDT-SWAP)
        """
        try:
            symbol = symbol.upper().strip()

            # 如果已经是OKX格式，直接返回
            if '-SWAP' in symbol or '-' in symbol:
                return symbol

            # 处理不同格式的输入
            if '/' in symbol:
                # BTC/USDT -> BTC-USDT-SWAP
                base_quote = symbol.split('/')
                if len(base_quote) == 2:
                    return f"{base_quote[0]}-{base_quote[1]}-SWAP"
            elif '_' in symbol:
                # BTC_USDT -> BTC-USDT-SWAP (Gate.io格式转换)
                base_quote = symbol.split('_')
                if len(base_quote) == 2:
                    return f"{base_quote[0]}-{base_quote[1]}-SWAP"

            # 如果无法识别格式，假设是基础货币，添加USDT和SWAP
            if symbol and not any(sep in symbol for sep in ['/', '-', '_']):
                return f"{symbol}-USDT-SWAP"

            return symbol

        except Exception as e:
            logger.error(f"OKX符号格式化失败: {e}")
            return symbol

    def validate_symbol_format(self, symbol: str) -> bool:
        """
        验证OKX交易对格式

        Args:
            symbol: 交易对符号

        Returns:
            bool: 格式是否正确
        """
        try:
            if not super().validate_symbol_format(symbol):
                return False

            symbol = symbol.upper().strip()

            # OKX期货合约格式: BASE-QUOTE-SWAP
            if '-SWAP' in symbol:
                parts = symbol.replace('-SWAP', '').split('-')
                return len(parts) == 2 and all(len(part) >= 2 for part in parts)

            # OKX现货格式: BASE-QUOTE
            if '-' in symbol and '-SWAP' not in symbol:
                parts = symbol.split('-')
                return len(parts) == 2 and all(len(part) >= 2 for part in parts)

            return False

        except Exception as e:
            logger.error(f"OKX符号格式验证失败: {e}")
            return False

    async def _get_position_stop_loss_price_async(self, symbol: str) -> dict:
        """
        异步获取OKX持仓的止损价格

        Args:
            symbol: 交易对符号

        Returns:
            dict: 止损价格信息
        """
        try:
            from datetime import datetime

            if not self.is_connected:
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': datetime.now(),
                    'error': 'OKX交易所未连接'
                }

            # 标准化交易对格式
            normalized_symbol = self.normalize_symbol(symbol)

            if not self._session or self._session.closed:
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': datetime.now(),
                    'error': '会话已关闭'
                }

            # 获取持仓信息
            try:
                headers = self._get_headers('GET', f'/api/v5/account/positions?instId={normalized_symbol}')
                async with self._session.get(
                    f"{self.base_url}/api/v5/account/positions?instId={normalized_symbol}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        positions = await response.json()
                    else:
                        logger.error(f"OKX API调用失败: {response.status}")
                        return {
                            'success': False,
                            'stop_loss_price': 0.0,
                            'timestamp': datetime.now(),
                            'error': f'OKX API调用失败: {response.status}'
                        }

                if not positions or 'data' not in positions:
                    return {
                        'success': False,
                        'stop_loss_price': 0.0,
                        'timestamp': datetime.now(),
                        'error': 'OKX未返回持仓数据'
                    }

                # 查找有效持仓
                for position in positions['data']:
                    if position.get('pos') and float(position.get('pos', 0)) != 0:
                        # 获取止损价格
                        stop_loss_price = position.get('slTriggerPx', '0')

                        if stop_loss_price and stop_loss_price != '0':
                            return {
                                'success': True,
                                'stop_loss_price': float(stop_loss_price),
                                'timestamp': datetime.now(),
                                'error': None
                            }
                        else:
                            # 如果没有设置止损，根据持仓信息计算
                            entry_price = float(position.get('avgPx', 0))
                            position_side = position.get('posSide', 'long')

                            if entry_price > 0:
                                # 假设5%的止损比例（可以从配置获取）
                                stop_loss_ratio = 0.05
                                if position_side == 'long':
                                    calculated_stop_loss = entry_price * (1 - stop_loss_ratio)
                                else:
                                    calculated_stop_loss = entry_price * (1 + stop_loss_ratio)

                                return {
                                    'success': True,
                                    'stop_loss_price': calculated_stop_loss,
                                    'timestamp': datetime.now(),
                                    'error': None,
                                    'calculated': True  # 标记为计算值
                                }

                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': datetime.now(),
                    'error': 'OKX未找到有效持仓'
                }

            except Exception as api_error:
                logger.error(f"OKX API调用失败: {api_error}")
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': datetime.now(),
                    'error': f'OKX API调用失败: {str(api_error)}'
                }

        except Exception as e:
            logger.error(f"OKX获取止损价格失败: {e}")
            return {
                'success': False,
                'stop_loss_price': 0.0,
                'timestamp': datetime.now(),
                'error': f'OKX获取止损价格异常: {str(e)}'
            }

    async def get_position_stop_loss_price(self, symbol: str) -> dict:
        """获取持仓止损价格（异步版本）"""
        return await self._get_position_stop_loss_price_async(symbol)

    # 移除同步版本，只保留异步版本

    async def place_algo_order(self, symbol: str, side: str, order_type: str,
                              quantity: float, trigger_price: float, order_price: float = -1,
                              reduce_only: bool = True) -> Optional[OrderInfo]:
        """
        OKX策略委托下单（止盈止损单）

        Args:
            symbol: 交易对
            side: 订单方向 ("buy" 或 "sell")
            order_type: 订单类型 ("conditional" - 单向止盈止损)
            quantity: 数量
            trigger_price: 触发价格
            order_price: 委托价格 (-1表示市价)
            reduce_only: 是否只减仓
        """
        try:
            okx_symbol = symbol.replace('/', '-')
            logger.info(f"🔧 [OKX策略委托] 下单: {okx_symbol}, 方向: {side}, 类型: {order_type}")

            # OKX策略委托参数
            params = {
                "instId": okx_symbol,
                "tdMode": "cross",  # 全仓模式
                "side": side,
                "ordType": order_type,
                "sz": str(quantity),
                "triggerPx": str(trigger_price),
                "orderPx": str(order_price) if order_price != -1 else "-1",
                "reduceOnly": str(reduce_only).lower()
            }

            logger.info(f"📊 [OKX策略委托] 请求参数: {_safe_log_params(params)}")

            # 调用OKX策略委托接口
            response = await self._request_okx_algo_order(params)

            if response and response.get('code') == '0':
                data = response.get('data', [{}])[0]
                order_id = data.get('algoId')

                logger.info(f"✅ [OKX策略委托] 下单成功: {order_id}")

                # 返回订单信息
                return OrderInfo(
                    order_id=order_id,
                    symbol=symbol,
                    side=OrderSide.BUY if side == "buy" else OrderSide.SELL,
                    order_type=order_type,
                    quantity=quantity,
                    price=trigger_price,
                    filled=0,
                    status="pending",
                    timestamp=datetime.now()
                )
            else:
                error_msg = response.get('msg', '未知错误') if response else '无响应'
                logger.error(f"❌ [OKX策略委托] 下单失败: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"❌ [OKX策略委托] 下单异常: {e}")
            return None

    async def _request_okx_algo_order(self, params: dict) -> dict:
        """
        发送OKX策略委托请求
        """
        try:
            endpoint = "/api/v5/trade/order-algo"
            method = "POST"

            # 生成签名
            timestamp = str(int(time.time() * 1000))
            body = json.dumps(params)

            # 创建签名字符串
            sign_str = timestamp + method + endpoint + body
            signature = base64.b64encode(
                hmac.new(
                    self.api_secret.encode('utf-8'),
                    sign_str.encode('utf-8'),
                    hashlib.sha256
                ).digest()
            ).decode('utf-8')

            # 请求头
            headers = {
                'OK-ACCESS-KEY': self.api_key,
                'OK-ACCESS-SIGN': signature,
                'OK-ACCESS-TIMESTAMP': timestamp,
                'OK-ACCESS-PASSPHRASE': self.passphrase,
                'Content-Type': 'application/json'
            }

            # 发送请求
            url = f"{self.base_url}{endpoint}"

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=body) as response:
                    result = await response.json()
                    logger.info(f"📊 [OKX策略委托] API响应: {_safe_log_params(result)}")
                    return result

        except Exception as e:
            logger.error(f"❌ [OKX策略委托] 请求异常: {e}")
            return {}

    async def place_order(self, symbol: str, side: OrderSide, order_type: str,
                         quantity: float, price: Optional[float] = None) -> Optional[OrderInfo]:
        """
        下单

        Args:
            symbol: 交易对符号
            side: 订单方向
            order_type: 订单类型 ("market", "limit", "stop_market")
            quantity: 订单数量
            price: 订单价格（限价单和止损单需要）

        Returns:
            Optional[OrderInfo]: 订单信息，失败时返回None
        """
        try:
            if not self.is_connected:
                logger.error("OKX交易所未连接")
                return None

            # 标准化交易对格式
            normalized_symbol = self.normalize_symbol(symbol)

            # 构建订单参数
            order_data = {
                "instId": normalized_symbol,
                "tdMode": "cross",  # 全仓模式
                "side": "buy" if side == OrderSide.BUY else "sell",
                "ordType": order_type,
                "sz": str(quantity),
                "posSide": "long" if side == OrderSide.BUY else "short"  # 合约必需的持仓方向
            }

            # 添加价格参数
            if order_type in ["limit", "stop_market"] and price:
                if order_type == "limit":
                    order_data["px"] = str(price)
                elif order_type == "stop_market":
                    order_data["slTriggerPx"] = str(price)

            # 发送订单请求
            body = json.dumps(order_data)
            headers = self._get_headers('POST', '/api/v5/trade/order', body)

            async with self._session.post(
                f"{self.base_url}/api/v5/trade/order",
                headers=headers,
                data=body
            ) as response:
                if response.status == 200:
                    result = await response.json()

                    if result.get('code') == '0' and result.get('data'):
                        order_data = result['data'][0]

                        # 创建OrderInfo对象
                        order_info = OrderInfo(
                            order_id=order_data.get('ordId', ''),
                            symbol=symbol,
                            side=side,
                            type=OrderType.MARKET if order_type == "market" else OrderType.LIMIT,
                            amount=quantity,
                            price=price or 0.0,
                            status="submitted",
                            filled=0.0,  # 初始为0，需要后续查询
                            remaining=quantity,  # 初始为全部数量
                            timestamp=int(datetime.now().timestamp())
                        )

                        logger.info(f"OKX订单提交成功: {order_info.order_id}")

                        # 立即查询订单状态以获取最新信息
                        try:
                            updated_order = await self.get_order_status(symbol, order_info.order_id)
                            if updated_order:
                                logger.info(f"订单状态更新: {updated_order.status}, 成交数量: {updated_order.filled}")
                                return updated_order
                            else:
                                logger.warning("无法获取订单状态，返回初始订单信息")
                                return order_info
                        except Exception as e:
                            logger.warning(f"查询订单状态失败: {e}，返回初始订单信息")
                            return order_info
                    else:
                        logger.error(f"OKX下单失败: {result}")
                        return None
                else:
                    logger.error(f"OKX API调用失败: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"OKX下单异常: {e}")
            return None

    async def get_trading_rules(self, symbol: str) -> Optional[Dict]:
        """
        获取交易对的交易规则（公共接口，无需签名）

        Args:
            symbol: 交易对符号

        Returns:
            Optional[Dict]: 交易规则信息
        """
        try:
            if not self._session:
                await self.connect()

            # 标准化交易对符号
            normalized_symbol = self._normalize_symbol(symbol)

            # 公共接口，无需签名
            url = f"{self.base_url}/api/v5/public/instruments?instType=SWAP&instId={normalized_symbol}"

            async with self._session.get(url) as response:
                if response.status == 200:
                    result = await response.json()

                    if result.get('code') == '0' and result.get('data'):
                        instrument_data = result['data'][0]

                        trading_rules = {
                            'symbol': symbol,
                            'normalized_symbol': normalized_symbol,
                            'min_size': float(instrument_data.get('minSz', '0')),  # 最小下单数量
                            'lot_size': float(instrument_data.get('lotSz', '1')),  # 下单数量精度
                            'tick_size': float(instrument_data.get('tickSz', '0.1')),  # 价格精度
                            'contract_val': float(instrument_data.get('ctVal', '1')),  # 合约面值
                            'contract_mult': float(instrument_data.get('ctMult', '1')),  # 合约乘数
                            'max_leverage': float(instrument_data.get('maxLever', '1')),  # 最大杠杆
                            'state': instrument_data.get('state', ''),  # 交易状态
                            'base_ccy': instrument_data.get('baseCcy', ''),  # 基础货币
                            'quote_ccy': instrument_data.get('quoteCcy', ''),  # 计价货币
                            'settle_ccy': instrument_data.get('settleCcy', ''),  # 结算货币
                            'inst_type': instrument_data.get('instType', ''),  # 产品类型
                        }

                        logger.info(f"✅ 获取交易规则成功: {symbol}")
                        logger.info(f"📊 最小下单数量: {trading_rules['min_size']}")
                        logger.info(f"📊 下单数量精度: {trading_rules['lot_size']}")
                        logger.info(f"📊 价格精度: {trading_rules['tick_size']}")
                        logger.info(f"📊 合约状态: {trading_rules['state']}")

                        return trading_rules
                    else:
                        logger.error(f"❌ 获取交易规则失败: {result}")
                        return None
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 获取交易规则API调用失败: {response.status}, 响应: {error_text}")
                    return None

        except Exception as e:
            logger.error(f"❌ 获取交易规则异常: {e}")
            return None

    def calculate_valid_quantity(self, raw_quantity: float, trading_rules: Dict) -> float:
        """
        根据交易规则计算有效的下单数量

        Args:
            raw_quantity: 原始数量
            trading_rules: 交易规则

        Returns:
            float: 调整后的有效数量
        """
        try:
            min_size = trading_rules.get('min_size', 1.0)
            lot_size = trading_rules.get('lot_size', 1.0)
            symbol = trading_rules.get('symbol', '')

            logger.info(f"🔢 开始计算有效数量: 原始={raw_quantity:.6f}")
            logger.info(f"📋 交易规则: 最小数量={min_size}, 精度={lot_size}")

            # 确保数量不小于最小下单数量
            if raw_quantity < min_size:
                quantity = min_size
                logger.warning(f"⚠️ 数量小于最小值，调整为最小下单数量: {quantity}")
            else:
                # 按照lot_size精度调整数量（向下取整到最近的lot_size倍数）
                quantity = int(raw_quantity / lot_size) * lot_size

                # 确保调整后的数量不小于最小下单数量
                if quantity < min_size:
                    quantity = min_size
                    logger.warning(f"⚠️ 调整后数量小于最小值，使用最小下单数量: {quantity}")

            # 最终验证
            if quantity <= 0:
                quantity = min_size
                logger.error(f"❌ 计算出的数量无效，使用最小下单数量: {quantity}")

            logger.info(f"✅ 数量计算完成: 原始={raw_quantity:.6f} → 调整后={quantity:.6f}")
            logger.info(f"📊 验证: 数量={quantity} >= 最小值={min_size}: {quantity >= min_size}")
            logger.info(f"📊 验证: 数量={quantity} 是精度={lot_size}的倍数: {abs(quantity % lot_size) < 1e-8}")

            return quantity

        except Exception as e:
            logger.error(f"❌ 计算有效数量异常: {e}")
            # 发生异常时返回最小下单数量
            min_size = trading_rules.get('min_size', 1.0)
            logger.warning(f"⚠️ 异常处理: 返回最小下单数量 {min_size}")
            return min_size

    def _normalize_symbol(self, symbol: str) -> str:
        """
        标准化交易对符号为OKX格式

        Args:
            symbol: 原始交易对符号

        Returns:
            str: 标准化后的符号
        """
        try:
            # 如果已经是OKX格式，直接返回
            if symbol.count('-') == 2:
                return symbol

            # 处理常见格式转换
            if symbol.upper() == 'BNB-USDT-SWAP' or symbol.upper() == 'BNBUSDT':
                return 'BNB-USDT-SWAP'
            elif symbol.upper() == 'BTC-USDT-SWAP' or symbol.upper() == 'BTCUSDT':
                return 'BTC-USDT-SWAP'
            elif symbol.upper() == 'ETH-USDT-SWAP' or symbol.upper() == 'ETHUSDT':
                return 'ETH-USDT-SWAP'
            else:
                # 默认处理：如果包含USDT，转换为SWAP格式
                if 'USDT' in symbol.upper():
                    base = symbol.upper().replace('USDT', '').replace('-', '').replace('_', '')
                    return f'{base}-USDT-SWAP'
                else:
                    return symbol

        except Exception as e:
            logger.error(f"标准化交易对符号失败: {e}")
            return symbol

    # 移除同步版本，只保留异步版本
