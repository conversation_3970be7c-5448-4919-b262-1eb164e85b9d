"""
RSI策略实现
基于RSI指标的超买超卖信号进行交易
"""
import asyncio
import logging
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，创建空装饰器
    def monitor_task(task_type):
        def decorator(func):
            return func
        return decorator

    class TaskType:
        OPENING_CHECK = "opening_check"
        PRICE_FETCH = "price_fetch"
        ORDER_EXECUTION = "order_execution"
        POSITION_CHECK = "position_check"

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class RSISignal(Enum):
    """RSI信号"""
    OVERSOLD = "oversold"              # 超卖信号 (RSI < 30)
    OVERBOUGHT = "overbought"          # 超买信号 (RSI > 70)
    NEUTRAL = "neutral"                # 中性区域 (30 <= RSI <= 70)
    BULLISH_DIVERGENCE = "bullish_divergence"    # 牛市背离
    BEARISH_DIVERGENCE = "bearish_divergence"    # 熊市背离
    EXTREME_OVERSOLD = "extreme_oversold"        # 极度超卖 (RSI < 20)
    EXTREME_OVERBOUGHT = "extreme_overbought"    # 极度超买 (RSI > 80)

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class RSIData:
    """RSI数据（包含可靠性评估）"""
    rsi_value: float          # RSI值
    signal: RSISignal         # RSI信号
    price_trend: str          # 价格趋势 ("up", "down", "sideways")
    divergence_strength: float # 背离强度 (0.0-1.0)
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 14  # 需要的数据点数
    calculation_period: int = 14  # 实际计算周期

class RSIConfig:
    """RSI策略配置"""
    
    def __init__(self):
        # RSI指标参数
        self.rsi_period = 14             # RSI周期
        self.oversold_threshold = 30     # 超卖阈值
        self.overbought_threshold = 70   # 超买阈值
        self.extreme_oversold = 20       # 极度超卖阈值
        self.extreme_overbought = 80     # 极度超买阈值
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "BNB/USDT"  # 自定义交易对
        self.custom_leverage = 50        # 自定义杠杆倍数
        self.initial_margin = 500.0      # 初始保证金 (USDT)
        self.take_profit_percent = 1.5   # 止盈百分比
        self.stop_loss_percent = 5.6     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 50    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.0  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 3              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [5.0, 8.0, 12.0, 20.0]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 12.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.15     # 最大投入资金比例 (15%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # RSI信号过滤
        self.enable_divergence_detection = True  # 启用背离检测
        self.min_divergence_strength = 0.3      # 最小背离强度
        self.signal_confirmation_periods = 2     # 信号确认周期数
        self.enable_extreme_levels = True       # 启用极值水平
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 80.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 30             # 下单冷却时间(秒)
        self.position_check_interval = 10    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 100              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证RSI参数
            if self.rsi_period <= 0 or self.rsi_period > 100:
                logger.error("RSI周期必须在1-100之间")
                return False
            
            if not (0 <= self.oversold_threshold <= 50):
                logger.error("超卖阈值必须在0-50之间")
                return False
            
            if not (50 <= self.overbought_threshold <= 100):
                logger.error("超买阈值必须在50-100之间")
                return False
            
            if self.oversold_threshold >= self.overbought_threshold:
                logger.error("超卖阈值必须小于超买阈值")
                return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            logger.info("✅ RSI策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ RSI策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # RSI需要的最小数据点数
        return self.rsi_period + 10  # 额外10个点用于背离检测
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # RSI参数
            "rsi_period": self.rsi_period,
            "oversold_threshold": self.oversold_threshold,
            "overbought_threshold": self.overbought_threshold,
            "extreme_oversold": self.extreme_oversold,
            "extreme_overbought": self.extreme_overbought,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_divergence_detection": self.enable_divergence_detection,
            "min_divergence_strength": self.min_divergence_strength,
            "signal_confirmation_periods": self.signal_confirmation_periods,
            "enable_extreme_levels": self.enable_extreme_levels,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""RSI策略配置:
        RSI参数: 周期={self.rsi_period}, 超卖={self.oversold_threshold}, 超买={self.overbought_threshold}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class RSICalculator:
    """RSI计算器 - 核心计算逻辑"""

    def __init__(self, period: int = 14, oversold: float = 30, overbought: float = 70):
        self.period = period
        self.oversold = oversold
        self.overbought = overbought

    def calculate_rsi(self, prices: List[float], min_periods: int = None) -> Optional[RSIData]:
        """
        计算RSI指标（支持部分数据计算）

        Args:
            prices: 价格列表
            min_periods: 最小计算周期，默认为period

        Returns:
            Optional[RSIData]: RSI数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.period, 10)  # 至少需要period个数据点

        data_count = len(prices)
        required_count = self.period + 10  # 额外数据用于背离检测

        # 数据完全不足
        if data_count < min_periods:
            logger.warning(f"❌ 价格数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ RSI数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ RSI数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_prices = prices[-calculation_period:] if calculation_period < data_count else prices

            # 转换为pandas Series进行计算
            price_series = pd.Series(calc_prices)

            # 计算价格变化
            delta = price_series.diff()

            # 分离上涨和下跌
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)

            # 计算平均涨幅和跌幅（使用指数移动平均）
            avg_gain = gain.ewm(span=self.period, adjust=False).mean()
            avg_loss = loss.ewm(span=self.period, adjust=False).mean()

            # 计算相对强度指数
            rs = avg_gain / avg_loss
            rsi_series = 100 - (100 / (1 + rs))

            # 获取最新RSI值
            rsi_value = rsi_series.iloc[-1]

            # 分析RSI信号
            signal = self._analyze_rsi_signal(rsi_value, calc_prices)

            # 分析价格趋势
            price_trend = self._analyze_price_trend(calc_prices)

            # 检测背离
            divergence_strength = self._detect_divergence(calc_prices, rsi_series.tolist())

            # 记录详细信息
            logger.info(f"📊 RSI计算完成: RSI={rsi_value:.2f}, 信号={signal.value}")
            logger.info(f"📊 价格趋势: {price_trend}, 背离强度: {divergence_strength:.3f}")
            logger.info(f"📊 数据统计: 使用{len(calc_prices)}个数据点, 可靠性={reliability:.2f}")

            return RSIData(
                rsi_value=round(rsi_value, 2),
                signal=signal,
                price_trend=price_trend,
                divergence_strength=divergence_strength,
                reliability=reliability,
                data_count=len(calc_prices),
                required_count=required_count,
                calculation_period=len(calc_prices)
            )

        except Exception as e:
            logger.error(f"计算RSI异常: {e}")
            return None

    def _analyze_rsi_signal(self, rsi_value: float, prices: List[float]) -> RSISignal:
        """
        分析RSI信号

        Args:
            rsi_value: RSI值
            prices: 价格列表

        Returns:
            RSISignal: RSI信号类型
        """
        try:
            # 极值水平检测
            if rsi_value <= 20:
                logger.info(f"🔵 极度超卖信号: RSI={rsi_value:.2f}")
                return RSISignal.EXTREME_OVERSOLD
            elif rsi_value >= 80:
                logger.info(f"🔴 极度超买信号: RSI={rsi_value:.2f}")
                return RSISignal.EXTREME_OVERBOUGHT

            # 标准超买超卖检测
            elif rsi_value <= self.oversold:
                logger.info(f"🟢 超卖信号: RSI={rsi_value:.2f}")
                return RSISignal.OVERSOLD
            elif rsi_value >= self.overbought:
                logger.info(f"🔴 超买信号: RSI={rsi_value:.2f}")
                return RSISignal.OVERBOUGHT

            # 中性区域
            else:
                logger.debug(f"⚪ 中性区域: RSI={rsi_value:.2f}")
                return RSISignal.NEUTRAL

        except Exception as e:
            logger.error(f"分析RSI信号异常: {e}")
            return RSISignal.NEUTRAL

    def _analyze_price_trend(self, prices: List[float]) -> str:
        """
        分析价格趋势

        Args:
            prices: 价格列表

        Returns:
            str: 趋势方向 ("up", "down", "sideways")
        """
        try:
            if len(prices) < 10:
                return "sideways"

            # 使用简单移动平均判断趋势
            short_ma = sum(prices[-5:]) / 5
            long_ma = sum(prices[-10:]) / 10

            trend_strength = abs(short_ma - long_ma) / long_ma

            if trend_strength < 0.01:  # 1%以内认为是横盘
                return "sideways"
            elif short_ma > long_ma:
                return "up"
            else:
                return "down"

        except Exception as e:
            logger.error(f"分析价格趋势异常: {e}")
            return "sideways"

    def _detect_divergence(self, prices: List[float], rsi_values: List[float]) -> float:
        """
        检测价格与RSI的背离

        Args:
            prices: 价格列表
            rsi_values: RSI值列表

        Returns:
            float: 背离强度 (0.0-1.0)
        """
        try:
            if len(prices) < 20 or len(rsi_values) < 20:
                return 0.0

            # 简化的背离检测：比较最近的高点和低点
            recent_prices = prices[-20:]
            recent_rsi = rsi_values[-20:]

            # 找到价格和RSI的高点、低点
            price_max_idx = recent_prices.index(max(recent_prices))
            price_min_idx = recent_prices.index(min(recent_prices))
            rsi_max_idx = recent_rsi.index(max(recent_rsi))
            rsi_min_idx = recent_rsi.index(min(recent_rsi))

            # 计算背离强度（简化版本）
            divergence_strength = 0.0

            # 牛市背离：价格创新低，RSI不创新低
            if price_min_idx > rsi_min_idx:
                divergence_strength += 0.3

            # 熊市背离：价格创新高，RSI不创新高
            if price_max_idx > rsi_max_idx:
                divergence_strength += 0.3

            return min(divergence_strength, 1.0)

        except Exception as e:
            logger.error(f"检测背离异常: {e}")
            return 0.0

    def calculate_rsi_signal_direction(self, rsi_data: RSIData) -> str:
        """
        基于RSI数据计算开仓方向

        Args:
            rsi_data: RSI数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 极度超卖 - 强烈做多信号
            if rsi_data.signal == RSISignal.EXTREME_OVERSOLD:
                logger.info("🚀 极度超卖做多信号: RSI < 20")
                return "long"

            # 极度超买 - 强烈做空信号
            elif rsi_data.signal == RSISignal.EXTREME_OVERBOUGHT:
                logger.info("💥 极度超买做空信号: RSI > 80")
                return "short"

            # 标准超卖 - 做多信号
            elif rsi_data.signal == RSISignal.OVERSOLD:
                # 结合趋势判断
                if rsi_data.price_trend == "down":
                    logger.info("📈 超卖做多信号: RSI < 30 + 下跌趋势")
                    return "long"
                else:
                    logger.info("⚠️ 超卖信号但趋势不明确")
                    return "hold"

            # 标准超买 - 做空信号
            elif rsi_data.signal == RSISignal.OVERBOUGHT:
                # 结合趋势判断
                if rsi_data.price_trend == "up":
                    logger.info("📉 超买做空信号: RSI > 70 + 上涨趋势")
                    return "short"
                else:
                    logger.info("⚠️ 超买信号但趋势不明确")
                    return "hold"

            # 背离信号
            elif rsi_data.divergence_strength > 0.5:
                if rsi_data.price_trend == "down" and rsi_data.rsi_value < 50:
                    logger.info("🔄 牛市背离做多信号")
                    return "long"
                elif rsi_data.price_trend == "up" and rsi_data.rsi_value > 50:
                    logger.info("🔄 熊市背离做空信号")
                    return "short"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: RSI={rsi_data.rsi_value:.2f}, 信号={rsi_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算RSI开仓方向异常: {e}")
            return "hold"

    def detect_rsi_signal(self, prices: List[float], min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测RSI信号

        Args:
            prices: 价格列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(prices) < self.period:
                return False, "hold", 0.0

            # 计算RSI数据
            rsi_data = self.calculate_rsi(prices, min_periods)
            if not rsi_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_rsi_signal_direction(rsi_data)

            if direction != "hold":
                # 计算信号强度
                if rsi_data.signal in [RSISignal.EXTREME_OVERSOLD, RSISignal.EXTREME_OVERBOUGHT]:
                    signal_strength = 1.0  # 极值信号强度最高
                elif rsi_data.signal in [RSISignal.OVERSOLD, RSISignal.OVERBOUGHT]:
                    signal_strength = 0.7  # 标准信号中等强度
                else:
                    signal_strength = 0.5  # 背离信号较低强度

                # 结合背离强度
                signal_strength = min(1.0, signal_strength + rsi_data.divergence_strength * 0.3)

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测RSI信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, prices: List[float], signal_type: str, confirmation_periods: int = 2) -> bool:
        """
        确认RSI信号的有效性

        Args:
            prices: 价格列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if len(prices) < confirmation_periods + 1:
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                check_prices = prices[:-(i+1)] if i > 0 else prices
                has_signal, detected_type, strength = self.detect_rsi_signal(check_prices)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ RSI信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ RSI信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认RSI信号异常: {e}")
            return False

class RSIStrategy:
    """RSI策略主类"""

    def __init__(self, config: RSIConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = RSICalculator(
            period=config.rsi_period,
            oversold=config.oversold_threshold,
            overbought=config.overbought_threshold
        )

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"🎯 RSI策略初始化完成: {config}")

    async def start(self):
        """启动RSI策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ RSI策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ RSI策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 RSI策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ RSI策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止RSI策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 RSI策略已停止")

        except Exception as e:
            logger.error(f"❌ RSI策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动RSI立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            prices = await self._get_price_data()
            if not prices or len(prices) < self.config.get_required_data_count():
                logger.warning("⚠️ 价格数据不足，无法进行RSI分析")
                return

            # 检测RSI信号
            has_signal, signal_type, signal_strength = self.calculator.detect_rsi_signal(prices)

            if not has_signal:
                logger.debug("未检测到RSI信号")
                return

            # 信号确认（如果启用）
            if self.config.signal_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(prices, signal_type, self.config.signal_confirmation_periods):
                    logger.warning(f"⚠️ RSI信号未确认: {signal_type}")
                    return

            # 背离检测（如果启用）
            if self.config.enable_divergence_detection:
                rsi_data = self.calculator.calculate_rsi(prices)
                if rsi_data and rsi_data.divergence_strength < self.config.min_divergence_strength:
                    # 如果不是极值信号且背离强度不足，跳过
                    if signal_strength < 0.8:  # 非极值信号
                        logger.warning(f"背离强度不足，跳过开仓: {rsi_data.divergence_strength:.3f} < {self.config.min_divergence_strength}")
                        return

            logger.info(f"🚀 准备执行RSI开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [RSI立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [RSI开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [RSI信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [RSI立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [RSI立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [RSI立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.95 if direction == "long" else entry_price * 1.05  # 默认5%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动RSI持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_data(self) -> List[float]:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                return []

            # 这里实现价格数据获取逻辑
            return []  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return []

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [RSI市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")

            # 确定订单方向
            from exchanges.base_exchange import OrderSide
            if direction == "long":
                order_side = OrderSide.BUY
            elif direction == "short":
                order_side = OrderSide.SELL
            else:
                logger.error(f"❌ 未知的开仓方向: {direction}")
                return False

            # 调用交易所API下单
            order_result = await self.exchange_manager.place_market_order(
                symbol=self.config.custom_symbol,
                side=order_side,
                amount=quantity,
                reduce_only=False  # 开仓单，不是平仓单
            )

            if order_result and hasattr(order_result, 'order_id') and order_result.order_id:
                logger.info(f"✅ [RSI开仓] 开仓订单已提交: 订单ID {order_result.order_id}")

                # 等待一段时间让订单执行
                await asyncio.sleep(1)

                # 验证开仓是否成功
                positions_after = await self.exchange_manager.get_positions(self.config.custom_symbol)
                if positions_after and any(p.size > 0 for p in positions_after):
                    logger.info(f"✅ [RSI开仓] 开仓验证成功：已建立持仓")
                    return True
                else:
                    logger.warning(f"⚠️ [RSI开仓] 开仓可能未成功，未检测到持仓")
                    return False
            else:
                logger.error(f"❌ [RSI开仓] 开仓订单提交失败")
                return False

        except Exception as e:
            logger.error(f"❌ RSI市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
        }
