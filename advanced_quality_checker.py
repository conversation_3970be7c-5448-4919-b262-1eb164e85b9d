#!/usr/bin/env python3
"""
高级质量检查引擎
对BUG修复工作进行全面的质量检查和验证
"""

import ast
import os
import re
import sys
import inspect
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict
from dataclasses import dataclass

@dataclass
class QualityIssue:
    """质量问题"""
    severity: str  # 'critical', 'high', 'medium', 'low'
    category: str  # 'syntax', 'logic', 'concurrency', 'performance', 'style'
    file_path: str
    line_number: Optional[int]
    method_name: Optional[str]
    description: str
    suggestion: str
    impact: str

class AdvancedQualityChecker:
    """高级质量检查器"""
    
    def __init__(self):
        self.issues = []
        self.modified_files = [
            'strategies/bollinger_strategy.py',
            'exchanges/okx_exchange.py', 
            'debug_immediate_open_detailed.py',
            'gui/main_window.py'
        ]
        self.method_definitions = {}
        self.method_calls = defaultdict(list)
        self.async_methods = set()
        self.sync_methods = set()
        
    def run_comprehensive_check(self) -> Dict[str, Any]:
        """运行全面质量检查"""
        print("🔍 启动高级质量检查引擎")
        print("=" * 80)
        
        # 阶段1：遗漏BUG检测
        self._detect_missed_bugs()
        
        # 阶段2：新引入BUG检测
        self._detect_newly_introduced_bugs()
        
        # 阶段3：异步一致性深度检查
        self._deep_async_consistency_check()
        
        # 阶段4：并发安全完整性验证
        self._verify_concurrency_safety_completeness()
        
        # 阶段5：性能和死锁风险分析
        self._analyze_performance_and_deadlock_risks()
        
        # 阶段6：功能回归检测
        self._detect_functional_regression()
        
        # 阶段7：边界条件和异常处理检查
        self._check_boundary_conditions_and_error_handling()
        
        return self._generate_comprehensive_report()
    
    def _detect_missed_bugs(self):
        """检测遗漏的BUG"""
        print("🔍 阶段1: 遗漏BUG检测")
        
        for file_path in self.modified_files:
            if os.path.exists(file_path):
                self._check_file_for_missed_bugs(file_path)
    
    def _check_file_for_missed_bugs(self, file_path: str):
        """检查单个文件的遗漏BUG"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 检查1：直接访问共享数据结构
            self._check_direct_shared_data_access(file_path, lines)
            
            # 检查2：缺少await的异步调用
            self._check_missing_await_calls(file_path, lines)
            
            # 检查3：异步方法在同步上下文中被调用
            self._check_async_in_sync_context(file_path, lines)
            
            # 检查4：资源泄漏
            self._check_resource_leaks(file_path, lines)
            
        except Exception as e:
            self._add_issue('medium', 'analysis', file_path, None, None,
                          f"文件分析失败: {e}", "检查文件编码和语法", "可能影响分析完整性")
    
    def _check_direct_shared_data_access(self, file_path: str, lines: List[str]):
        """检查直接访问共享数据结构"""
        dangerous_patterns = [
            (r'self\.price_history(?!\w)', '直接访问price_history'),
            (r'self\.add_records(?!\w)', '直接访问add_records'),
            (r'self\.bb_history(?!\w)', '直接访问bb_history'),
            (r'\.price_history\[', '直接索引访问price_history'),
            (r'\.add_records\[', '直接索引访问add_records'),
            (r'\.bb_history\[', '直接索引访问bb_history'),
            (r'len\(self\.price_history\)', '直接获取price_history长度'),
            (r'len\(self\.add_records\)', '直接获取add_records长度'),
            (r'len\(self\.bb_history\)', '直接获取bb_history长度')
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern, description in dangerous_patterns:
                if re.search(pattern, line):
                    # 检查是否在锁保护的上下文中
                    context_start = max(0, i-10)
                    context_end = min(len(lines), i+5)
                    context = '\n'.join(lines[context_start:context_end])
                    
                    if 'async with self._' not in context and '_lock' not in context:
                        self._add_issue('critical', 'concurrency', file_path, i, None,
                                      f"{description}: {line.strip()}",
                                      "使用对应的线程安全方法",
                                      "可能导致数据竞争和不一致状态")
    
    def _check_missing_await_calls(self, file_path: str, lines: List[str]):
        """检查缺少await的异步调用"""
        async_method_patterns = [
            r'\.add_price_to_history\(',
            r'\.get_price_history_copy\(',
            r'\.get_price_history_length\(',
            r'\.add_position_record\(',
            r'\.get_add_records_copy\(',
            r'\.get_add_records_count\(',
            r'\.add_bb_history\(',
            r'\.get_bb_history_copy\(',
            r'\.get_latest_bb_data\(',
            r'\.calculate_bollinger_bands\(',
            r'\.should_add_position\(',
            r'\.execute_auto_opening\(',
            r'\.analyze_opening_opportunity\(',
            r'\.get_current_bollinger_data\(',
            r'\.validate_symbol\(',
            r'\.place_stop_order\(',
            r'\.close_position\(',
            r'\.close_all_positions\('
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern in async_method_patterns:
                if re.search(pattern, line) and 'await' not in line and 'def ' not in line:
                    # 排除方法定义行
                    if not line.strip().startswith('def ') and not line.strip().startswith('async def '):
                        self._add_issue('critical', 'async', file_path, i, None,
                                      f"异步方法调用缺少await: {line.strip()}",
                                      "在方法调用前添加await关键字",
                                      "可能导致方法不正确执行或返回Promise对象")
    
    def _detect_newly_introduced_bugs(self):
        """检测新引入的BUG"""
        print("🔍 阶段2: 新引入BUG检测")
        
        for file_path in self.modified_files:
            if os.path.exists(file_path):
                self._check_syntax_and_logic_errors(file_path)
    
    def _check_syntax_and_logic_errors(self, file_path: str):
        """检查语法和逻辑错误"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 语法检查
            try:
                ast.parse(content)
            except SyntaxError as e:
                self._add_issue('critical', 'syntax', file_path, e.lineno, None,
                              f"语法错误: {e.msg}",
                              "修复语法错误",
                              "阻止代码执行")
            
            # 逻辑错误检查
            self._check_logic_errors(file_path, content)
            
        except Exception as e:
            self._add_issue('high', 'analysis', file_path, None, None,
                          f"文件检查失败: {e}", "检查文件可访问性", "影响质量分析")
    
    def _check_logic_errors(self, file_path: str, content: str):
        """检查逻辑错误"""
        lines = content.split('\n')
        
        # 检查包装器方法的参数传递
        for i, line in enumerate(lines, 1):
            # 检查execute_auto_opening_async调用
            if 'execute_auto_opening_async(' in line:
                if 'exchange=' not in line or 'force_execute=' not in line:
                    self._add_issue('high', 'logic', file_path, i, None,
                                  "包装器方法可能缺少必要参数",
                                  "确保所有必要参数都被传递",
                                  "可能导致方法调用失败")
            
            # 检查analyze_opening_opportunity调用
            if 'analyze_opening_opportunity(' in line and 'def ' not in line:
                if 'exchange=' not in line and 'symbol=' not in line:
                    # 检查是否是包装器调用
                    if 'analyze_opening_opportunity_sync' not in lines[max(0, i-3):i+3]:
                        self._add_issue('medium', 'logic', file_path, i, None,
                                      "可能缺少exchange或symbol参数",
                                      "检查参数传递的完整性",
                                      "可能影响方法功能")
    
    def _deep_async_consistency_check(self):
        """深度异步一致性检查"""
        print("🔍 阶段3: 异步一致性深度检查")
        
        # 构建方法调用图
        self._build_method_call_graph()
        
        # 检查调用链的异步一致性
        self._check_call_chain_consistency()
    
    def _build_method_call_graph(self):
        """构建方法调用图"""
        for file_path in self.modified_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    tree = ast.parse(content)
                    
                    for node in ast.walk(tree):
                        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                            method_name = f"{file_path}:{node.name}"
                            is_async = isinstance(node, ast.AsyncFunctionDef)
                            
                            self.method_definitions[method_name] = {
                                'is_async': is_async,
                                'file': file_path,
                                'line': node.lineno
                            }
                            
                            if is_async:
                                self.async_methods.add(method_name)
                            else:
                                self.sync_methods.add(method_name)
                
                except Exception as e:
                    self._add_issue('medium', 'analysis', file_path, None, None,
                                  f"方法调用图构建失败: {e}", "检查代码结构", "影响调用链分析")
    
    def _verify_concurrency_safety_completeness(self):
        """验证并发安全完整性"""
        print("🔍 阶段4: 并发安全完整性验证")
        
        # 检查所有锁的使用是否正确
        self._check_lock_usage_correctness()
        
        # 检查是否有潜在的死锁风险
        self._check_potential_deadlocks()
    
    def _check_lock_usage_correctness(self):
        """检查锁使用的正确性"""
        bollinger_file = 'strategies/bollinger_strategy.py'
        if os.path.exists(bollinger_file):
            with open(bollinger_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 检查锁的定义
            locks_defined = []
            for i, line in enumerate(lines, 1):
                if '_lock = asyncio.Lock()' in line:
                    lock_name = line.split('=')[0].strip().split('.')[-1]
                    locks_defined.append(lock_name)
            
            # 检查锁的使用
            for i, line in enumerate(lines, 1):
                if 'async with self._' in line and '_lock:' in line:
                    # 提取锁名称
                    lock_match = re.search(r'self\.(_\w+_lock)', line)
                    if lock_match:
                        lock_name = lock_match.group(1)
                        if lock_name not in locks_defined:
                            self._add_issue('critical', 'concurrency', bollinger_file, i, None,
                                          f"使用了未定义的锁: {lock_name}",
                                          "确保锁已正确定义",
                                          "可能导致运行时错误")
    
    def _analyze_performance_and_deadlock_risks(self):
        """分析性能和死锁风险"""
        print("🔍 阶段5: 性能和死锁风险分析")
        
        # 检查嵌套锁的使用
        self._check_nested_lock_usage()
        
        # 检查长时间持有锁的情况
        self._check_long_lock_holding()
    
    def _check_nested_lock_usage(self):
        """检查嵌套锁使用"""
        bollinger_file = 'strategies/bollinger_strategy.py'
        if os.path.exists(bollinger_file):
            with open(bollinger_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            in_lock_context = False
            lock_depth = 0
            
            for i, line in enumerate(lines, 1):
                if 'async with self._' in line and '_lock:' in line:
                    lock_depth += 1
                    if lock_depth > 1:
                        self._add_issue('high', 'performance', bollinger_file, i, None,
                                      "检测到嵌套锁使用",
                                      "考虑重构以避免嵌套锁",
                                      "可能增加死锁风险和降低性能")
                
                # 简单的锁上下文结束检测
                if lock_depth > 0 and line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                    lock_depth = max(0, lock_depth - 1)
    
    def _detect_functional_regression(self):
        """检测功能回归"""
        print("🔍 阶段6: 功能回归检测")
        
        # 检查关键方法的签名是否改变
        self._check_method_signature_changes()
        
        # 检查返回值类型是否一致
        self._check_return_type_consistency()
    
    def _check_method_signature_changes(self):
        """检查方法签名变化"""
        # 检查关键的包装器方法
        bollinger_file = 'strategies/bollinger_strategy.py'
        if os.path.exists(bollinger_file):
            with open(bollinger_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查execute_auto_opening方法签名
            if 'def execute_auto_opening(self, current_price: float, direction: str)' not in content:
                self._add_issue('high', 'regression', bollinger_file, None, 'execute_auto_opening',
                              "execute_auto_opening方法签名可能已改变",
                              "确保向后兼容性",
                              "可能破坏现有调用")
    
    def _check_boundary_conditions_and_error_handling(self):
        """检查边界条件和异常处理"""
        print("🔍 阶段7: 边界条件和异常处理检查")
        
        for file_path in self.modified_files:
            if os.path.exists(file_path):
                self._check_error_handling_completeness(file_path)
    
    def _check_error_handling_completeness(self, file_path: str):
        """检查异常处理完整性"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 检查异步方法是否有适当的异常处理
            in_async_method = False
            method_name = None
            has_try_except = False
            
            for i, line in enumerate(lines, 1):
                if line.strip().startswith('async def '):
                    in_async_method = True
                    method_name = line.split('(')[0].split()[-1]
                    has_try_except = False
                elif line.strip().startswith('def ') and not line.strip().startswith('async def '):
                    in_async_method = False
                elif in_async_method and 'try:' in line:
                    has_try_except = True
                elif in_async_method and line.strip() == '' and method_name:
                    # 方法结束
                    if not has_try_except and 'await' in content[content.find(f'async def {method_name}'):]:
                        self._add_issue('medium', 'error_handling', file_path, i, method_name,
                                      f"异步方法{method_name}缺少异常处理",
                                      "添加try-except块处理潜在异常",
                                      "可能导致未捕获的异常")
                    in_async_method = False
                    method_name = None
        
        except Exception as e:
            self._add_issue('low', 'analysis', file_path, None, None,
                          f"异常处理检查失败: {e}", "检查代码结构", "影响错误处理分析")
    
    def _add_issue(self, severity: str, category: str, file_path: str, 
                   line_number: Optional[int], method_name: Optional[str],
                   description: str, suggestion: str, impact: str):
        """添加质量问题"""
        issue = QualityIssue(
            severity=severity,
            category=category,
            file_path=file_path,
            line_number=line_number,
            method_name=method_name,
            description=description,
            suggestion=suggestion,
            impact=impact
        )
        self.issues.append(issue)
    
    def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成全面报告"""
        print("=" * 80)
        print("📋 生成全面质量检查报告")
        
        # 按严重程度分类
        critical_issues = [i for i in self.issues if i.severity == 'critical']
        high_issues = [i for i in self.issues if i.severity == 'high']
        medium_issues = [i for i in self.issues if i.severity == 'medium']
        low_issues = [i for i in self.issues if i.severity == 'low']
        
        # 按类别分类
        categories = defaultdict(list)
        for issue in self.issues:
            categories[issue.category].append(issue)
        
        report = {
            'total_issues': len(self.issues),
            'critical_issues': len(critical_issues),
            'high_issues': len(high_issues),
            'medium_issues': len(medium_issues),
            'low_issues': len(low_issues),
            'issues_by_category': dict(categories),
            'all_issues': self.issues
        }
        
        # 打印报告
        print(f"🎯 质量检查总结:")
        print(f"   🚨 关键问题: {len(critical_issues)} 个")
        print(f"   ⚠️  高级问题: {len(high_issues)} 个")
        print(f"   💡 中等问题: {len(medium_issues)} 个")
        print(f"   📝 低级问题: {len(low_issues)} 个")
        print(f"   📊 总计: {len(self.issues)} 个问题")
        
        if critical_issues:
            print(f"\n🚨 关键问题详情:")
            for i, issue in enumerate(critical_issues, 1):
                print(f"   {i}. [{issue.category}] {issue.description}")
                print(f"      文件: {issue.file_path}")
                if issue.line_number:
                    print(f"      行号: {issue.line_number}")
                print(f"      建议: {issue.suggestion}")
                print(f"      影响: {issue.impact}")
                print()
        
        if high_issues:
            print(f"\n⚠️  高级问题详情:")
            for i, issue in enumerate(high_issues, 1):
                print(f"   {i}. [{issue.category}] {issue.description}")
                print(f"      文件: {issue.file_path}")
                if issue.line_number:
                    print(f"      行号: {issue.line_number}")
                print(f"      建议: {issue.suggestion}")
                print()
        
        # 质量评估
        if len(critical_issues) == 0:
            print(f"\n✅ 质量评估: 优秀 - 无关键问题")
        elif len(critical_issues) <= 2:
            print(f"\n⚠️  质量评估: 良好 - 少量关键问题需要修复")
        else:
            print(f"\n❌ 质量评估: 需要改进 - 多个关键问题需要立即修复")
        
        return report

def main():
    """主函数"""
    checker = AdvancedQualityChecker()
    report = checker.run_comprehensive_check()
    
    # 根据检查结果决定退出码
    if report['critical_issues'] > 0:
        print("\n❌ 发现关键问题，需要立即修复")
        return False
    elif report['high_issues'] > 0:
        print("\n⚠️  发现高级问题，建议修复")
        return True
    else:
        print("\n✅ 质量检查通过")
        return True

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ 质量检查异常: {e}")
        sys.exit(1)
