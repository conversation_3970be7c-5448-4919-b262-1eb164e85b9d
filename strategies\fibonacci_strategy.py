"""
斐波那契回撤(Fibonacci Retracement)策略实现
基于斐波那契回撤位的支撑阻力和突破信号进行交易
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime

# 导入并发监控工具
try:
    from utils.concurrency_monitor import monitor_task, TaskType
except ImportError:
    # 如果导入失败，创建空装饰器
    def monitor_task(task_type):
        def decorator(func):
            return func
        return decorator

    class TaskType:
        OPENING_CHECK = "opening_check"
        PRICE_FETCH = "price_fetch"
        ORDER_EXECUTION = "order_execution"
        POSITION_CHECK = "position_check"

logger = logging.getLogger(__name__)

class AddPositionType(Enum):
    """加仓类型"""
    EQUAL = "equal"      # 等量加仓
    HALF = "half"        # 半量加仓
    QUARTER = "quarter"  # 四分之一量加仓

class FibonacciSignal(Enum):
    """斐波那契信号"""
    SUPPORT_BOUNCE = "support_bounce"          # 支撑反弹 (价格在斐波那契位获得支撑)
    RESISTANCE_REJECTION = "resistance_rejection"  # 阻力回落 (价格在斐波那契位遇到阻力)
    BREAKOUT_BULLISH = "breakout_bullish"      # 向上突破 (突破斐波那契阻力位)
    BREAKOUT_BEARISH = "breakout_bearish"      # 向下突破 (跌破斐波那契支撑位)
    RETRACEMENT_ENTRY = "retracement_entry"    # 回撤入场 (回撤至关键位置)
    EXTENSION_TARGET = "extension_target"      # 延伸目标 (价格达到延伸位)
    CONFLUENCE_SIGNAL = "confluence_signal"    # 汇聚信号 (多个斐波那契位重合)
    TREND_CONTINUATION = "trend_continuation"  # 趋势延续
    REVERSAL_SIGNAL = "reversal_signal"       # 反转信号
    NEUTRAL = "neutral"                        # 中性
    NO_SIGNAL = "no_signal"                   # 无信号

@dataclass
class AddPositionRecord:
    """加仓记录"""
    timestamp: datetime
    price: float
    quantity: float
    margin: float
    add_type: AddPositionType
    trigger_reason: str
    new_avg_cost: float
    total_margin: float
    add_count: int

@dataclass
class FibonacciLevel:
    """斐波那契水平位"""
    level: float          # 斐波那契比例 (如 0.382)
    price: float          # 对应的价格
    level_type: str       # 水平位类型 ("retracement", "extension")
    strength: float       # 水平位强度 (0.0-1.0)
    hit_count: int = 0    # 触及次数
    last_hit_time: Optional[datetime] = None  # 最后触及时间

@dataclass
class FibonacciData:
    """斐波那契数据（包含可靠性评估）"""
    swing_high: float         # 摆动高点
    swing_low: float          # 摆动低点
    trend_direction: str      # 趋势方向 ("up", "down", "sideways")
    retracement_levels: List[FibonacciLevel]  # 回撤位列表
    extension_levels: List[FibonacciLevel]    # 延伸位列表
    current_price: float      # 当前价格
    nearest_level: Optional[FibonacciLevel]   # 最近的斐波那契位
    signal: FibonacciSignal   # 斐波那契信号
    signal_strength: float    # 信号强度 (0.0-1.0)
    confluence_count: int     # 汇聚数量
    reliability: float = 1.0  # 可靠性评分 (0.0-1.0)
    data_count: int = 0       # 实际数据点数
    required_count: int = 50  # 需要的数据点数
    calculation_period: int = 50  # 实际计算周期

class FibonacciConfig:
    """斐波那契回撤策略配置"""
    
    def __init__(self):
        # 斐波那契指标参数
        self.period = 50                 # 斐波那契计算周期
        self.swing_detection_period = 20 # 摆动点检测周期
        self.breakout_confirmation_periods = 3  # 突破确认周期
        self.level_tolerance = 0.5       # 水平位容差百分比
        self.min_swing_size = 2.0        # 最小摆动幅度百分比
        
        # 斐波那契回撤位 (标准比例)
        self.retracement_levels = [0.236, 0.382, 0.500, 0.618, 0.786]
        
        # 斐波那契延伸位
        self.extension_levels = [1.272, 1.414, 1.618, 2.000, 2.618]
        
        # 自定义交易参数（不使用全局配置）
        self.custom_symbol = "ETH/USDT"  # 自定义交易对
        self.custom_leverage = 15        # 自定义杠杆倍数
        self.initial_margin = 800.0      # 初始保证金 (USDT)
        self.take_profit_percent = 2.5   # 止盈百分比
        self.stop_loss_percent = 4.2     # 止损百分比
        
        # 加仓触发参数
        self.trigger_distance_points = 60    # 触发加仓的距离(点数)
        self.trigger_distance_percent = 1.0  # 触发加仓的距离(百分比)
        self.use_points_trigger = True       # 是否使用点数触发
        
        # 加仓类型和次数
        self.add_position_types = [AddPositionType.EQUAL]  # 加仓类型序列
        self.max_add_count = 3              # 最大加仓次数
        
        # 加仓间距设置(递增)
        self.add_intervals = [2.0, 3.5, 5.5, 8.0]  # 加仓间距百分比
        
        # 风险控制
        self.max_total_loss_percent = 12.0   # 最大总亏损百分比
        self.max_investment_ratio = 0.20     # 最大投入资金比例 (20%)
        self.enable_emergency_stop = True    # 启用紧急停止
        
        # 斐波那契信号过滤
        self.enable_confluence_filter = True    # 启用汇聚过滤
        self.min_confluence_count = 2           # 最小汇聚数量
        self.enable_trend_filter = True         # 启用趋势过滤
        self.enable_breakout_confirmation = True  # 启用突破确认
        self.min_signal_strength = 0.6         # 最小信号强度
        
        # 立即开仓功能
        self.immediate_open_enabled = False   # 立即开仓功能开关
        self.immediate_check_interval = 30    # 立即开仓检测间隔(秒)
        
        # 紧急加仓保护
        self.enable_emergency_add = True      # 启用紧急加仓
        self.emergency_distance_threshold = 65.0  # 紧急距离阈值(%)
        
        # 冷却时间设置
        self.order_cooldown = 35             # 下单冷却时间(秒)
        self.position_check_interval = 15    # 持仓检查间隔(秒)
        
        # 交易所设置（使用自定义设置）
        self.exchange_name = "okx"          # 默认交易所
        
        # 数据获取设置
        self.kline_limit = 200              # K线数据获取数量
        self.price_precision = 4            # 价格精度
        self.quantity_precision = 8         # 数量精度
        
        # 日志和监控
        self.enable_detailed_logging = True  # 启用详细日志
        self.enable_performance_monitoring = True  # 启用性能监控
        
        # 模拟交易设置
        self.is_simulation = False          # 是否为模拟交易
        self.simulation_balance = 10000.0   # 模拟账户余额
        
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证斐波那契参数
            if self.period <= 0 or self.period > 200:
                logger.error("斐波那契周期必须在1-200之间")
                return False
            
            if self.swing_detection_period <= 0 or self.swing_detection_period > 100:
                logger.error("摆动点检测周期必须在1-100之间")
                return False
            
            if not (0.1 <= self.level_tolerance <= 2.0):
                logger.error("水平位容差必须在0.1-2.0%之间")
                return False
            
            if not (0.5 <= self.min_swing_size <= 10.0):
                logger.error("最小摆动幅度必须在0.5-10.0%之间")
                return False
            
            if self.breakout_confirmation_periods < 1 or self.breakout_confirmation_periods > 10:
                logger.error("突破确认周期必须在1-10之间")
                return False
            
            # 验证斐波那契水平位
            if not self.retracement_levels or len(self.retracement_levels) == 0:
                logger.error("回撤位列表不能为空")
                return False
            
            for level in self.retracement_levels:
                if not (0.0 <= level <= 1.0):
                    logger.error(f"回撤位{level}必须在0.0-1.0之间")
                    return False
            
            for level in self.extension_levels:
                if not (1.0 <= level <= 5.0):
                    logger.error(f"延伸位{level}必须在1.0-5.0之间")
                    return False
            
            # 验证自定义交易参数
            if not self.custom_symbol or "/" not in self.custom_symbol:
                logger.error("自定义交易对格式无效")
                return False
            
            if self.custom_leverage <= 0 or self.custom_leverage > 100:
                logger.error("自定义杠杆倍数必须在1-100之间")
                return False
            
            if self.initial_margin <= 0:
                logger.error("初始保证金必须大于0")
                return False
            
            # 验证风险控制参数
            if self.take_profit_percent <= 0:
                logger.error("止盈百分比必须大于0")
                return False
            
            if self.stop_loss_percent <= 0:
                logger.error("止损百分比必须大于0")
                return False
            
            if self.max_total_loss_percent <= 0:
                logger.error("最大总亏损百分比必须大于0")
                return False
            
            if self.max_investment_ratio <= 0 or self.max_investment_ratio > 1:
                logger.error("最大投入资金比例必须在0-1之间")
                return False
            
            # 验证加仓参数
            if self.max_add_count < 0:
                logger.error("最大加仓次数不能为负数")
                return False
            
            if len(self.add_intervals) < self.max_add_count:
                logger.error("加仓间距设置数量不足")
                return False
            
            # 验证信号过滤参数
            if self.min_confluence_count < 1 or self.min_confluence_count > 10:
                logger.error("最小汇聚数量必须在1-10之间")
                return False
            
            if not (0.0 <= self.min_signal_strength <= 1.0):
                logger.error("最小信号强度必须在0.0-1.0之间")
                return False
            
            logger.info("✅ 斐波那契策略配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 斐波那契策略配置验证失败: {e}")
            return False
    
    def get_required_data_count(self) -> int:
        """获取所需的最小数据点数"""
        # 斐波那契需要的最小数据点数
        return max(self.period, 50) + 20  # 额外20个点用于摆动点检测
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            # 斐波那契参数
            "period": self.period,
            "swing_detection_period": self.swing_detection_period,
            "breakout_confirmation_periods": self.breakout_confirmation_periods,
            "level_tolerance": self.level_tolerance,
            "min_swing_size": self.min_swing_size,
            "retracement_levels": self.retracement_levels,
            "extension_levels": self.extension_levels,
            
            # 自定义交易参数
            "custom_symbol": self.custom_symbol,
            "custom_leverage": self.custom_leverage,
            "initial_margin": self.initial_margin,
            "take_profit_percent": self.take_profit_percent,
            "stop_loss_percent": self.stop_loss_percent,
            
            # 风险控制
            "max_total_loss_percent": self.max_total_loss_percent,
            "max_investment_ratio": self.max_investment_ratio,
            "max_add_count": self.max_add_count,
            
            # 信号过滤
            "enable_confluence_filter": self.enable_confluence_filter,
            "min_confluence_count": self.min_confluence_count,
            "enable_trend_filter": self.enable_trend_filter,
            "enable_breakout_confirmation": self.enable_breakout_confirmation,
            "min_signal_strength": self.min_signal_strength,
            
            # 立即开仓
            "immediate_open_enabled": self.immediate_open_enabled,
            "immediate_check_interval": self.immediate_check_interval,
            
            # 交易设置
            "exchange_name": self.exchange_name,
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"未知的配置参数: {key}")
    
    def __str__(self) -> str:
        """配置信息的字符串表示"""
        return f"""斐波那契策略配置:
        斐波那契参数: 周期={self.period}, 摆动检测={self.swing_detection_period}, 确认周期={self.breakout_confirmation_periods}
        自定义交易: 交易对={self.custom_symbol}, 杠杆={self.custom_leverage}x, 保证金={self.initial_margin}
        风险控制: 止盈={self.take_profit_percent}%, 止损={self.stop_loss_percent}%
        加仓设置: 最大次数={self.max_add_count}, 最大亏损={self.max_total_loss_percent}%
        立即开仓: {'启用' if self.immediate_open_enabled else '禁用'}
        交易所: {self.exchange_name}"""

class FibonacciCalculator:
    """斐波那契回撤计算器 - 核心计算逻辑"""

    def __init__(self, period: int = 50, swing_detection_period: int = 20):
        self.period = period
        self.swing_detection_period = swing_detection_period

    def calculate_fibonacci(self, highs: List[float], lows: List[float], closes: List[float],
                           min_periods: int = None) -> Optional[FibonacciData]:
        """
        计算斐波那契回撤（支持部分数据计算）

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期，默认为period

        Returns:
            Optional[FibonacciData]: 斐波那契数据（包含可靠性评估）
        """
        # 设置最小计算周期
        if min_periods is None:
            min_periods = max(self.period, 50)  # 至少需要period个数据点

        data_count = len(closes)
        required_count = self.period + 30  # 额外数据用于摆动点检测

        # 数据完全不足
        if data_count < min_periods or len(highs) != data_count or len(lows) != data_count:
            logger.warning(f"❌ 斐波那契数据严重不足，需要至少{min_periods}个，当前{data_count}个")
            return None

        try:
            # 计算可靠性评分
            if data_count >= required_count:
                reliability = 1.0  # 完全可靠
                calculation_period = required_count
                logger.info(f"✅ 斐波那契数据充足: {data_count}/{required_count} (可靠性: 100%)")
            else:
                reliability = data_count / required_count  # 部分可靠
                calculation_period = data_count
                logger.warning(f"⚠️ 斐波那契数据不足: {data_count}/{required_count} (可靠性: {reliability*100:.1f}%)")

            # 使用实际可用的数据
            calc_highs = highs[-calculation_period:] if calculation_period < data_count else highs
            calc_lows = lows[-calculation_period:] if calculation_period < data_count else lows
            calc_closes = closes[-calculation_period:] if calculation_period < data_count else closes

            # 转换为pandas Series进行计算
            high_series = pd.Series(calc_highs)
            low_series = pd.Series(calc_lows)
            close_series = pd.Series(calc_closes)

            # 检测摆动高点和低点
            swing_high, swing_low = self._detect_swing_points(high_series, low_series, close_series)

            if swing_high is None or swing_low is None:
                logger.warning("❌ 无法检测到有效的摆动点")
                return None

            # 确定趋势方向
            trend_direction = self._determine_trend_direction(swing_high, swing_low, close_series.iloc[-1])

            # 计算斐波那契回撤位
            retracement_levels = self._calculate_retracement_levels(swing_high, swing_low)

            # 计算斐波那契延伸位
            extension_levels = self._calculate_extension_levels(swing_high, swing_low, trend_direction)

            # 获取当前价格
            current_price = close_series.iloc[-1]

            # 找到最近的斐波那契位
            nearest_level = self._find_nearest_level(current_price, retracement_levels + extension_levels)

            # 分析斐波那契信号
            signal = self._analyze_fibonacci_signal(current_price, retracement_levels, extension_levels,
                                                  trend_direction, close_series)

            # 计算信号强度
            signal_strength = self._calculate_signal_strength(signal, nearest_level, current_price)

            # 计算汇聚数量
            confluence_count = self._calculate_confluence_count(current_price, retracement_levels + extension_levels)

            # 记录详细信息
            logger.info(f"📊 斐波那契计算完成: 摆动高={swing_high:.4f}, 摆动低={swing_low:.4f}")
            logger.info(f"📊 趋势方向: {trend_direction}, 当前价格: {current_price:.4f}")
            logger.info(f"📊 信号: {signal.value}, 强度: {signal_strength:.3f}")
            nearest_level_text = f"{nearest_level.price:.4f}" if nearest_level else "None"
            logger.info(f"📊 汇聚数量: {confluence_count}, 最近水平位: {nearest_level_text}")
            logger.info(f"📊 数据统计: 使用{len(calc_closes)}个数据点, 可靠性={reliability:.2f}")

            return FibonacciData(
                swing_high=round(swing_high, 4),
                swing_low=round(swing_low, 4),
                trend_direction=trend_direction,
                retracement_levels=retracement_levels,
                extension_levels=extension_levels,
                current_price=round(current_price, 4),
                nearest_level=nearest_level,
                signal=signal,
                signal_strength=round(signal_strength, 3),
                confluence_count=confluence_count,
                reliability=reliability,
                data_count=len(calc_closes),
                required_count=required_count,
                calculation_period=len(calc_closes)
            )

        except Exception as e:
            logger.error(f"计算斐波那契异常: {e}")
            return None

    def _detect_swing_points(self, highs: pd.Series, lows: pd.Series, closes: pd.Series) -> Tuple[Optional[float], Optional[float]]:
        """
        检测摆动高点和低点

        Args:
            highs: 最高价序列
            lows: 最低价序列
            closes: 收盘价序列

        Returns:
            Tuple[Optional[float], Optional[float]]: (摆动高点, 摆动低点)
        """
        try:
            if len(highs) < self.swing_detection_period * 2:
                return None, None

            # 使用滚动窗口检测局部极值
            window = self.swing_detection_period

            # 检测摆动高点（局部最高点）
            swing_highs = []
            for i in range(window, len(highs) - window):
                if highs.iloc[i] == highs.iloc[i-window:i+window+1].max():
                    swing_highs.append((i, highs.iloc[i]))

            # 检测摆动低点（局部最低点）
            swing_lows = []
            for i in range(window, len(lows) - window):
                if lows.iloc[i] == lows.iloc[i-window:i+window+1].min():
                    swing_lows.append((i, lows.iloc[i]))

            if not swing_highs or not swing_lows:
                # 如果没有检测到摆动点，使用整体最高最低点
                swing_high = highs.max()
                swing_low = lows.min()
            else:
                # 选择最近的显著摆动点
                swing_high = max(swing_highs, key=lambda x: x[1])[1]  # 最高的摆动高点
                swing_low = min(swing_lows, key=lambda x: x[1])[1]    # 最低的摆动低点

            # 验证摆动幅度是否足够
            swing_range = swing_high - swing_low
            price_range = (swing_high + swing_low) / 2
            swing_percentage = (swing_range / price_range) * 100

            if swing_percentage < 2.0:  # 最小摆动幅度2%
                logger.warning(f"摆动幅度过小: {swing_percentage:.2f}%")
                return None, None

            logger.info(f"检测到摆动点: 高={swing_high:.4f}, 低={swing_low:.4f}, 幅度={swing_percentage:.2f}%")
            return swing_high, swing_low

        except Exception as e:
            logger.error(f"检测摆动点异常: {e}")
            return None, None

    def _determine_trend_direction(self, swing_high: float, swing_low: float, current_price: float) -> str:
        """
        确定趋势方向

        Args:
            swing_high: 摆动高点
            swing_low: 摆动低点
            current_price: 当前价格

        Returns:
            str: 趋势方向
        """
        try:
            # 计算价格在摆动范围中的位置
            swing_range = swing_high - swing_low
            price_position = (current_price - swing_low) / swing_range

            if price_position > 0.7:
                return "up"      # 上升趋势
            elif price_position < 0.3:
                return "down"    # 下降趋势
            else:
                return "sideways"  # 横盘整理

        except Exception as e:
            logger.error(f"确定趋势方向异常: {e}")
            return "sideways"

    def _calculate_retracement_levels(self, swing_high: float, swing_low: float) -> List[FibonacciLevel]:
        """
        计算斐波那契回撤位

        Args:
            swing_high: 摆动高点
            swing_low: 摆动低点

        Returns:
            List[FibonacciLevel]: 回撤位列表
        """
        try:
            retracement_levels = []
            swing_range = swing_high - swing_low

            # 标准斐波那契回撤比例
            standard_levels = [0.236, 0.382, 0.500, 0.618, 0.786]

            for level in standard_levels:
                # 从高点向低点回撤
                price = swing_high - (swing_range * level)

                # 计算水平位强度（基于标准比例的重要性）
                if level in [0.382, 0.618]:
                    strength = 1.0  # 黄金比例，最强
                elif level == 0.500:
                    strength = 0.9  # 50%回撤，次强
                elif level in [0.236, 0.786]:
                    strength = 0.7  # 其他重要比例
                else:
                    strength = 0.5  # 一般比例

                fib_level = FibonacciLevel(
                    level=level,
                    price=round(price, 4),
                    level_type="retracement",
                    strength=strength
                )
                retracement_levels.append(fib_level)

            # 按价格排序
            retracement_levels.sort(key=lambda x: x.price, reverse=True)

            logger.info(f"计算回撤位: {len(retracement_levels)}个水平位")
            return retracement_levels

        except Exception as e:
            logger.error(f"计算回撤位异常: {e}")
            return []

    def _calculate_extension_levels(self, swing_high: float, swing_low: float, trend_direction: str) -> List[FibonacciLevel]:
        """
        计算斐波那契延伸位

        Args:
            swing_high: 摆动高点
            swing_low: 摆动低点
            trend_direction: 趋势方向

        Returns:
            List[FibonacciLevel]: 延伸位列表
        """
        try:
            extension_levels = []
            swing_range = swing_high - swing_low

            # 标准斐波那契延伸比例
            standard_extensions = [1.272, 1.414, 1.618, 2.000, 2.618]

            for level in standard_extensions:
                if trend_direction == "up":
                    # 上升趋势，从高点向上延伸
                    price = swing_high + (swing_range * (level - 1))
                else:
                    # 下降趋势，从低点向下延伸
                    price = swing_low - (swing_range * (level - 1))

                # 计算水平位强度
                if level == 1.618:
                    strength = 1.0  # 黄金延伸比例，最强
                elif level in [1.272, 2.618]:
                    strength = 0.8  # 重要延伸比例
                elif level in [1.414, 2.000]:
                    strength = 0.6  # 次要延伸比例
                else:
                    strength = 0.4  # 一般延伸比例

                fib_level = FibonacciLevel(
                    level=level,
                    price=round(price, 4),
                    level_type="extension",
                    strength=strength
                )
                extension_levels.append(fib_level)

            # 按价格排序
            if trend_direction == "up":
                extension_levels.sort(key=lambda x: x.price)
            else:
                extension_levels.sort(key=lambda x: x.price, reverse=True)

            logger.info(f"计算延伸位: {len(extension_levels)}个水平位")
            return extension_levels

        except Exception as e:
            logger.error(f"计算延伸位异常: {e}")
            return []

    def _find_nearest_level(self, current_price: float, all_levels: List[FibonacciLevel]) -> Optional[FibonacciLevel]:
        """
        找到最近的斐波那契水平位

        Args:
            current_price: 当前价格
            all_levels: 所有斐波那契水平位

        Returns:
            Optional[FibonacciLevel]: 最近的水平位
        """
        try:
            if not all_levels:
                return None

            # 计算每个水平位与当前价格的距离
            distances = []
            for level in all_levels:
                distance = abs(current_price - level.price)
                distances.append((distance, level))

            # 找到距离最小的水平位
            distances.sort(key=lambda x: x[0])
            nearest_level = distances[0][1]

            logger.debug(f"最近水平位: {nearest_level.level} @ {nearest_level.price:.4f}")
            return nearest_level

        except Exception as e:
            logger.error(f"查找最近水平位异常: {e}")
            return None

    def _analyze_fibonacci_signal(self, current_price: float, retracement_levels: List[FibonacciLevel],
                                 extension_levels: List[FibonacciLevel], trend_direction: str,
                                 closes: pd.Series) -> FibonacciSignal:
        """
        分析斐波那契信号

        Args:
            current_price: 当前价格
            retracement_levels: 回撤位列表
            extension_levels: 延伸位列表
            trend_direction: 趋势方向
            closes: 收盘价序列

        Returns:
            FibonacciSignal: 斐波那契信号类型
        """
        try:
            if len(closes) < 3:
                return FibonacciSignal.NO_SIGNAL

            # 检查价格是否接近重要斐波那契位
            tolerance = 0.5  # 0.5%容差

            # 检查回撤位信号
            for level in retracement_levels:
                price_diff_percent = abs(current_price - level.price) / level.price * 100

                if price_diff_percent <= tolerance:
                    # 价格接近回撤位
                    if self._is_bouncing_from_level(closes, level.price, "support"):
                        logger.info(f"🟢 支撑反弹信号: 价格在{level.level*100:.1f}%回撤位({level.price:.4f})获得支撑")
                        return FibonacciSignal.SUPPORT_BOUNCE
                    elif self._is_rejecting_from_level(closes, level.price, "resistance"):
                        logger.info(f"🔴 阻力回落信号: 价格在{level.level*100:.1f}%回撤位({level.price:.4f})遇到阻力")
                        return FibonacciSignal.RESISTANCE_REJECTION

            # 检查突破信号
            breakthrough_level = self._check_breakthrough(current_price, closes, retracement_levels + extension_levels)
            if breakthrough_level:
                if closes.iloc[-1] > closes.iloc[-3]:  # 价格上升
                    logger.info(f"🚀 向上突破信号: 突破{breakthrough_level.level}位({breakthrough_level.price:.4f})")
                    return FibonacciSignal.BREAKOUT_BULLISH
                else:  # 价格下降
                    logger.info(f"💥 向下突破信号: 跌破{breakthrough_level.level}位({breakthrough_level.price:.4f})")
                    return FibonacciSignal.BREAKOUT_BEARISH

            # 检查回撤入场信号
            if trend_direction in ["up", "down"]:
                entry_level = self._check_retracement_entry(current_price, retracement_levels, trend_direction)
                if entry_level:
                    logger.info(f"📈 回撤入场信号: 在{entry_level.level*100:.1f}%位({entry_level.price:.4f})入场")
                    return FibonacciSignal.RETRACEMENT_ENTRY

            # 检查延伸目标信号
            target_level = self._check_extension_target(current_price, extension_levels)
            if target_level:
                logger.info(f"🎯 延伸目标信号: 达到{target_level.level}延伸位({target_level.price:.4f})")
                return FibonacciSignal.EXTENSION_TARGET

            # 检查汇聚信号
            if self._check_confluence_signal(current_price, retracement_levels + extension_levels):
                logger.info(f"🔄 汇聚信号: 多个斐波那契位在当前价格附近汇聚")
                return FibonacciSignal.CONFLUENCE_SIGNAL

            # 检查趋势延续信号
            if self._check_trend_continuation(closes, trend_direction):
                logger.debug(f"📊 趋势延续信号: {trend_direction}趋势继续")
                return FibonacciSignal.TREND_CONTINUATION

            # 其他情况
            logger.debug(f"⚪ 中性信号: 当前价格{current_price:.4f}")
            return FibonacciSignal.NEUTRAL

        except Exception as e:
            logger.error(f"分析斐波那契信号异常: {e}")
            return FibonacciSignal.NO_SIGNAL

    def _is_bouncing_from_level(self, closes: pd.Series, level_price: float, level_type: str) -> bool:
        """
        检查价格是否从水平位反弹

        Args:
            closes: 收盘价序列
            level_price: 水平位价格
            level_type: 水平位类型

        Returns:
            bool: 是否反弹
        """
        try:
            if len(closes) < 3:
                return False

            recent_prices = closes.tail(3).tolist()

            if level_type == "support":
                # 检查支撑反弹：价格先接近支撑位，然后反弹
                return (recent_prices[0] > level_price and
                       recent_prices[1] <= level_price * 1.005 and  # 接近支撑位
                       recent_prices[2] > recent_prices[1])         # 开始反弹
            else:  # resistance
                # 检查阻力回落：价格先接近阻力位，然后回落
                return (recent_prices[0] < level_price and
                       recent_prices[1] >= level_price * 0.995 and  # 接近阻力位
                       recent_prices[2] < recent_prices[1])         # 开始回落

        except Exception as e:
            logger.error(f"检查反弹异常: {e}")
            return False

    def _is_rejecting_from_level(self, closes: pd.Series, level_price: float, level_type: str) -> bool:
        """
        检查价格是否被水平位拒绝

        Args:
            closes: 收盘价序列
            level_price: 水平位价格
            level_type: 水平位类型

        Returns:
            bool: 是否被拒绝
        """
        try:
            if len(closes) < 3:
                return False

            recent_prices = closes.tail(3).tolist()

            if level_type == "resistance":
                # 检查阻力拒绝：价格接近阻力位后被拒绝
                return (recent_prices[1] >= level_price * 0.995 and  # 接近阻力位
                       recent_prices[2] < recent_prices[1] * 0.998)  # 被拒绝下跌
            else:  # support
                # 检查支撑拒绝：价格接近支撑位后被拒绝
                return (recent_prices[1] <= level_price * 1.005 and  # 接近支撑位
                       recent_prices[2] > recent_prices[1] * 1.002)  # 被拒绝上涨

        except Exception as e:
            logger.error(f"检查拒绝异常: {e}")
            return False

    def _check_breakthrough(self, current_price: float, closes: pd.Series,
                           all_levels: List[FibonacciLevel]) -> Optional[FibonacciLevel]:
        """
        检查突破信号

        Args:
            current_price: 当前价格
            closes: 收盘价序列
            all_levels: 所有斐波那契水平位

        Returns:
            Optional[FibonacciLevel]: 突破的水平位
        """
        try:
            if len(closes) < 3:
                return None

            prev_price = closes.iloc[-2]
            tolerance = 0.3  # 0.3%容差

            for level in all_levels:
                # 检查是否发生突破
                if ((prev_price <= level.price * (1 + tolerance/100) and
                     current_price > level.price * (1 + tolerance/100)) or
                    (prev_price >= level.price * (1 - tolerance/100) and
                     current_price < level.price * (1 - tolerance/100))):
                    return level

            return None

        except Exception as e:
            logger.error(f"检查突破异常: {e}")
            return None

    def _check_retracement_entry(self, current_price: float, retracement_levels: List[FibonacciLevel],
                                trend_direction: str) -> Optional[FibonacciLevel]:
        """
        检查回撤入场信号

        Args:
            current_price: 当前价格
            retracement_levels: 回撤位列表
            trend_direction: 趋势方向

        Returns:
            Optional[FibonacciLevel]: 入场的回撤位
        """
        try:
            tolerance = 1.0  # 1%容差

            for level in retracement_levels:
                price_diff_percent = abs(current_price - level.price) / level.price * 100

                if price_diff_percent <= tolerance:
                    # 在关键回撤位附近
                    if level.level in [0.382, 0.618]:  # 黄金比例
                        return level
                    elif level.level == 0.500:  # 50%回撤
                        return level

            return None

        except Exception as e:
            logger.error(f"检查回撤入场异常: {e}")
            return None

    def _check_extension_target(self, current_price: float, extension_levels: List[FibonacciLevel]) -> Optional[FibonacciLevel]:
        """
        检查延伸目标信号

        Args:
            current_price: 当前价格
            extension_levels: 延伸位列表

        Returns:
            Optional[FibonacciLevel]: 达到的延伸位
        """
        try:
            tolerance = 0.8  # 0.8%容差

            for level in extension_levels:
                price_diff_percent = abs(current_price - level.price) / level.price * 100

                if price_diff_percent <= tolerance:
                    return level

            return None

        except Exception as e:
            logger.error(f"检查延伸目标异常: {e}")
            return None

    def _check_confluence_signal(self, current_price: float, all_levels: List[FibonacciLevel]) -> bool:
        """
        检查汇聚信号

        Args:
            current_price: 当前价格
            all_levels: 所有斐波那契水平位

        Returns:
            bool: 是否有汇聚信号
        """
        try:
            tolerance = 1.5  # 1.5%容差
            nearby_levels = 0

            for level in all_levels:
                price_diff_percent = abs(current_price - level.price) / level.price * 100
                if price_diff_percent <= tolerance:
                    nearby_levels += 1

            return nearby_levels >= 2  # 至少2个水平位汇聚

        except Exception as e:
            logger.error(f"检查汇聚信号异常: {e}")
            return False

    def _check_trend_continuation(self, closes: pd.Series, trend_direction: str) -> bool:
        """
        检查趋势延续信号

        Args:
            closes: 收盘价序列
            trend_direction: 趋势方向

        Returns:
            bool: 是否有趋势延续信号
        """
        try:
            if len(closes) < 5:
                return False

            recent_closes = closes.tail(5)

            if trend_direction == "up":
                # 上升趋势：最近价格整体上升
                return recent_closes.iloc[-1] > recent_closes.iloc[0]
            elif trend_direction == "down":
                # 下降趋势：最近价格整体下降
                return recent_closes.iloc[-1] < recent_closes.iloc[0]
            else:
                return False

        except Exception as e:
            logger.error(f"检查趋势延续异常: {e}")
            return False

    def _calculate_signal_strength(self, signal: FibonacciSignal, nearest_level: Optional[FibonacciLevel],
                                  current_price: float) -> float:
        """
        计算信号强度

        Args:
            signal: 斐波那契信号
            nearest_level: 最近的水平位
            current_price: 当前价格

        Returns:
            float: 信号强度 (0.0-1.0)
        """
        try:
            base_strength = 0.5

            # 根据信号类型调整基础强度
            if signal in [FibonacciSignal.SUPPORT_BOUNCE, FibonacciSignal.RESISTANCE_REJECTION]:
                base_strength = 0.8
            elif signal in [FibonacciSignal.BREAKOUT_BULLISH, FibonacciSignal.BREAKOUT_BEARISH]:
                base_strength = 0.9
            elif signal == FibonacciSignal.RETRACEMENT_ENTRY:
                base_strength = 0.7
            elif signal == FibonacciSignal.CONFLUENCE_SIGNAL:
                base_strength = 0.85

            # 根据最近水平位的强度调整
            if nearest_level:
                base_strength *= nearest_level.strength

            return min(1.0, base_strength)

        except Exception as e:
            logger.error(f"计算信号强度异常: {e}")
            return 0.5

    def _calculate_confluence_count(self, current_price: float, all_levels: List[FibonacciLevel]) -> int:
        """
        计算汇聚数量

        Args:
            current_price: 当前价格
            all_levels: 所有斐波那契水平位

        Returns:
            int: 汇聚数量
        """
        try:
            tolerance = 1.5  # 1.5%容差
            confluence_count = 0

            for level in all_levels:
                price_diff_percent = abs(current_price - level.price) / level.price * 100
                if price_diff_percent <= tolerance:
                    confluence_count += 1

            return confluence_count

        except Exception as e:
            logger.error(f"计算汇聚数量异常: {e}")
            return 0

    def calculate_fibonacci_signal_direction(self, fibonacci_data: FibonacciData) -> str:
        """
        基于斐波那契数据计算开仓方向

        Args:
            fibonacci_data: 斐波那契数据

        Returns:
            str: 开仓方向 ("long", "short", "hold")
        """
        try:
            # 支撑反弹信号 - 做多
            if fibonacci_data.signal == FibonacciSignal.SUPPORT_BOUNCE:
                logger.info("🚀 斐波那契支撑反弹做多信号")
                return "long"

            # 阻力回落信号 - 做空
            elif fibonacci_data.signal == FibonacciSignal.RESISTANCE_REJECTION:
                logger.info("💥 斐波那契阻力回落做空信号")
                return "short"

            # 向上突破信号 - 做多
            elif fibonacci_data.signal == FibonacciSignal.BREAKOUT_BULLISH:
                if fibonacci_data.signal_strength > 0.7:  # 需要足够的突破强度
                    logger.info("📈 斐波那契向上突破做多信号")
                    return "long"
                else:
                    logger.info("⚠️ 斐波那契向上突破但强度不足")
                    return "hold"

            # 向下突破信号 - 做空
            elif fibonacci_data.signal == FibonacciSignal.BREAKOUT_BEARISH:
                if fibonacci_data.signal_strength > 0.7:  # 需要足够的突破强度
                    logger.info("📉 斐波那契向下突破做空信号")
                    return "short"
                else:
                    logger.info("⚠️ 斐波那契向下突破但强度不足")
                    return "hold"

            # 回撤入场信号
            elif fibonacci_data.signal == FibonacciSignal.RETRACEMENT_ENTRY:
                if fibonacci_data.trend_direction == "up":
                    logger.info("📊 斐波那契回撤入场做多信号")
                    return "long"
                elif fibonacci_data.trend_direction == "down":
                    logger.info("📊 斐波那契回撤入场做空信号")
                    return "short"
                else:
                    logger.debug("📊 斐波那契回撤入场但趋势不明确")
                    return "hold"

            # 汇聚信号
            elif fibonacci_data.signal == FibonacciSignal.CONFLUENCE_SIGNAL:
                if fibonacci_data.confluence_count >= 3:  # 强汇聚
                    if fibonacci_data.trend_direction == "up":
                        logger.info("🔄 斐波那契强汇聚做多信号")
                        return "long"
                    elif fibonacci_data.trend_direction == "down":
                        logger.info("🔄 斐波那契强汇聚做空信号")
                        return "short"
                else:
                    logger.debug("🔄 斐波那契汇聚但强度不足")
                    return "hold"

            # 其他情况保持观望
            else:
                logger.debug(f"⏸️ 保持观望: 斐波那契信号={fibonacci_data.signal.value}")
                return "hold"

        except Exception as e:
            logger.error(f"计算斐波那契开仓方向异常: {e}")
            return "hold"

    def detect_fibonacci_signal(self, highs: List[float], lows: List[float], closes: List[float],
                               min_periods: int = None) -> Tuple[bool, str, float]:
        """
        专门检测斐波那契信号

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            min_periods: 最小计算周期

        Returns:
            Tuple[bool, str, float]: (是否有信号, 信号类型, 信号强度)
        """
        try:
            if len(closes) < self.period or len(highs) < self.period or len(lows) < self.period:
                return False, "hold", 0.0

            # 计算斐波那契数据
            fibonacci_data = self.calculate_fibonacci(highs, lows, closes, min_periods)
            if not fibonacci_data:
                return False, "hold", 0.0

            # 计算开仓方向
            direction = self.calculate_fibonacci_signal_direction(fibonacci_data)

            if direction != "hold":
                # 获取信号强度
                signal_strength = fibonacci_data.signal_strength

                # 结合汇聚数量调整强度
                if fibonacci_data.confluence_count >= 2:
                    signal_strength *= 1.2  # 汇聚增强信号

                # 结合最近水平位强度调整
                if fibonacci_data.nearest_level and fibonacci_data.nearest_level.strength > 0.8:
                    signal_strength *= 1.1  # 强水平位增强信号

                # 结合可靠性调整强度
                signal_strength *= fibonacci_data.reliability

                # 限制在0-1之间
                signal_strength = min(1.0, signal_strength)

                return True, direction, signal_strength

            return False, "hold", 0.0

        except Exception as e:
            logger.error(f"检测斐波那契信号异常: {e}")
            return False, "hold", 0.0

    def is_signal_confirmed(self, highs: List[float], lows: List[float], closes: List[float],
                           signal_type: str, confirmation_periods: int = 3) -> bool:
        """
        确认斐波那契信号的有效性

        Args:
            highs: 最高价列表
            lows: 最低价列表
            closes: 收盘价列表
            signal_type: 信号类型
            confirmation_periods: 确认周期数

        Returns:
            bool: 信号是否确认
        """
        try:
            if (len(closes) < confirmation_periods + 1 or
                len(highs) < confirmation_periods + 1 or
                len(lows) < confirmation_periods + 1):
                return False

            confirmed_count = 0

            # 检查最近几个周期的信号一致性
            for i in range(confirmation_periods):
                if i == 0:
                    check_highs = highs
                    check_lows = lows
                    check_closes = closes
                else:
                    check_highs = highs[:-(i)]
                    check_lows = lows[:-(i)]
                    check_closes = closes[:-(i)]

                has_signal, detected_type, strength = self.detect_fibonacci_signal(
                    check_highs, check_lows, check_closes)

                if has_signal and detected_type == signal_type:
                    confirmed_count += 1

            # 需要至少一半的周期确认信号
            required_confirmations = max(1, confirmation_periods // 2)
            is_confirmed = confirmed_count >= required_confirmations

            if is_confirmed:
                logger.info(f"✅ 斐波那契信号确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")
            else:
                logger.warning(f"⚠️ 斐波那契信号未确认: {signal_type}, 确认次数: {confirmed_count}/{confirmation_periods}")

            return is_confirmed

        except Exception as e:
            logger.error(f"确认斐波那契信号异常: {e}")
            return False

class FibonacciStrategy:
    """斐波那契回撤策略主类"""

    def __init__(self, config: FibonacciConfig, exchange_manager=None):
        self.config = config
        self.exchange_manager = exchange_manager
        self.calculator = FibonacciCalculator(
            period=config.period,
            swing_detection_period=config.swing_detection_period
        )

        # 策略状态
        self.is_running = False
        self.last_signal_time = None
        self.last_order_time = None
        self.current_position = None
        self.add_position_records: List[AddPositionRecord] = []

        # 性能监控
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0

        # 异步任务管理
        self.monitoring_task = None
        self.immediate_open_task = None

        logger.info(f"📊 斐波那契策略初始化完成: {config}")

    async def start(self):
        """启动斐波那契策略"""
        try:
            if self.is_running:
                logger.warning("⚠️ 斐波那契策略已在运行中")
                return

            # 验证配置
            if not self.config.validate():
                logger.error("❌ 斐波那契策略配置验证失败")
                return

            # 验证交易所连接
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            self.is_running = True
            logger.info("🚀 斐波那契策略启动成功")

            # 启动立即开仓功能（如果启用）
            if self.config.immediate_open_enabled:
                await self._start_immediate_open_monitor()

            # 启动持仓监控
            self.monitoring_task = asyncio.create_task(self._start_position_monitor())

        except Exception as e:
            logger.error(f"❌ 斐波那契策略启动失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止斐波那契策略"""
        try:
            self.is_running = False

            # 停止异步任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.immediate_open_task:
                self.immediate_open_task.cancel()
                try:
                    await self.immediate_open_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 斐波那契策略已停止")

        except Exception as e:
            logger.error(f"❌ 斐波那契策略停止失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _start_immediate_open_monitor(self):
        """启动立即开仓监控"""
        try:
            logger.info(f"🔍 启动斐波那契立即开仓监控，检测间隔: {self.config.immediate_check_interval}秒")

            async def check_immediate_open():
                while self.is_running and self.config.immediate_open_enabled:
                    try:
                        await self._check_immediate_open_opportunity()
                        await asyncio.sleep(self.config.immediate_check_interval)
                    except Exception as e:
                        logger.error(f"❌ 立即开仓检查异常: {e}")
                        await asyncio.sleep(5)  # 错误时短暂等待

            self.immediate_open_task = asyncio.create_task(check_immediate_open())

        except Exception as e:
            logger.error(f"❌ 启动立即开仓监控失败: {e}")

    @monitor_task(TaskType.OPENING_CHECK)
    async def _check_immediate_open_opportunity(self):
        """检查立即开仓机会"""
        try:
            # 检查是否有持仓
            if await self._has_position():
                logger.debug("已有持仓，跳过开仓检查")
                return

            # 检查冷却时间
            if not self._check_order_cooldown():
                logger.debug("冷却时间未到，跳过开仓检查")
                return

            # 获取当前价格数据
            price_data = await self._get_price_data()
            if (not price_data or
                len(price_data['closes']) < self.config.get_required_data_count() or
                len(price_data['highs']) < self.config.get_required_data_count() or
                len(price_data['lows']) < self.config.get_required_data_count()):
                logger.warning("⚠️ 价格数据不足，无法进行斐波那契分析")
                return

            # 检测斐波那契信号
            has_signal, signal_type, signal_strength = self.calculator.detect_fibonacci_signal(
                price_data['highs'], price_data['lows'], price_data['closes'])

            if not has_signal:
                logger.debug("未检测到斐波那契信号")
                return

            # 信号确认（如果启用）
            if self.config.breakout_confirmation_periods > 1:
                if not self.calculator.is_signal_confirmed(
                    price_data['highs'], price_data['lows'], price_data['closes'],
                    signal_type, self.config.breakout_confirmation_periods):
                    logger.warning(f"⚠️ 斐波那契信号未确认: {signal_type}")
                    return

            # 信号过滤
            if not self._filter_signal(price_data, signal_type, signal_strength):
                logger.warning(f"⚠️ 斐波那契信号被过滤: {signal_type}")
                return

            logger.info(f"🚀 准备执行斐波那契开仓: 方向={signal_type}, 强度={signal_strength:.3f}")
            await self._execute_immediate_opening(signal_type, signal_strength)

        except Exception as e:
            logger.error(f"❌ 检查立即开仓机会异常: {e}")

    def _filter_signal(self, price_data: Dict, signal_type: str, signal_strength: float) -> bool:
        """
        过滤斐波那契信号

        Args:
            price_data: 价格数据字典
            signal_type: 信号类型
            signal_strength: 信号强度

        Returns:
            bool: 信号是否通过过滤
        """
        try:
            # 计算斐波那契数据用于过滤
            fibonacci_data = self.calculator.calculate_fibonacci(
                price_data['highs'], price_data['lows'], price_data['closes'])
            if not fibonacci_data:
                return False

            # 信号强度过滤
            if signal_strength < self.config.min_signal_strength:
                logger.debug(f"信号强度不足: {signal_strength:.3f} < {self.config.min_signal_strength}")
                return False

            # 汇聚过滤
            if self.config.enable_confluence_filter:
                if fibonacci_data.confluence_count < self.config.min_confluence_count:
                    logger.debug(f"汇聚数量不足: {fibonacci_data.confluence_count} < {self.config.min_confluence_count}")
                    return False

            # 趋势过滤
            if self.config.enable_trend_filter:
                if fibonacci_data.trend_direction == "sideways":
                    logger.debug("趋势不明确，跳过交易")
                    return False

            return True

        except Exception as e:
            logger.error(f"过滤斐波那契信号异常: {e}")
            return False

    async def _execute_immediate_opening(self, direction: str, signal_strength: float):
        """执行立即开仓"""
        try:
            logger.info(f"🚀 [斐波那契立即开仓] 执行开仓: 方向={direction}, 强度={signal_strength:.3f}")

            # 获取当前价格
            current_price = await self._get_current_price()
            if not current_price:
                logger.error("❌ 无法获取当前价格")
                return

            # 计算开仓数量
            quantity = self._calculate_position_size(current_price)
            if quantity <= 0:
                logger.error("❌ 计算的开仓数量无效")
                return

            # 计算止损价格
            stop_loss_price = self._calculate_stop_loss_price(current_price, direction)

            # 记录开仓信息
            logger.info(f"📊 [斐波那契开仓详情] 价格: {current_price:.4f}, 数量: {quantity}, 止损: {stop_loss_price:.4f}")
            logger.info(f"📊 [斐波那契信号详情] 方向: {direction}, 强度: {signal_strength:.3f}")
            logger.info(f"📊 [自定义设置] 交易对: {self.config.custom_symbol}, 杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [斐波那契参数] 周期: {self.config.period}, 摆动检测: {self.config.swing_detection_period}, 确认周期: {self.config.breakout_confirmation_periods}")

            # 执行开仓
            order_result = await self._place_market_order(direction, quantity, current_price)
            if order_result:
                logger.info(f"✅ [斐波那契立即开仓] 开仓成功: {direction}仓位，价格: {current_price:.4f}，数量: {quantity}")

                # 设置止损订单
                await self._place_stop_loss_order(direction, quantity, stop_loss_price)

                # 更新策略状态
                self.last_order_time = datetime.now()
                self.last_signal_time = datetime.now()
                self.total_trades += 1

                # 记录信号强度用于后续分析
                if not hasattr(self, 'signal_history'):
                    self.signal_history = []
                self.signal_history.append({
                    'timestamp': datetime.now(),
                    'direction': direction,
                    'signal_strength': signal_strength,
                    'price': current_price,
                    'quantity': quantity,
                    'custom_symbol': self.config.custom_symbol,
                    'custom_leverage': self.config.custom_leverage,
                    'fibonacci_period': self.config.period,
                    'swing_detection_period': self.config.swing_detection_period,
                    'confirmation_periods': self.config.breakout_confirmation_periods
                })

                # 禁用立即开仓功能（开仓后自动禁用）
                self.config.immediate_open_enabled = False
                logger.info("🔒 [斐波那契立即开仓] 开仓后自动禁用立即开仓功能")

            else:
                logger.error("❌ [斐波那契立即开仓] 开仓失败")

        except Exception as e:
            logger.error(f"❌ 执行立即开仓异常: {e}")

    def _calculate_stop_loss_price(self, entry_price: float, direction: str) -> float:
        """计算止损价格"""
        try:
            stop_loss_ratio = self.config.stop_loss_percent / 100.0

            if direction == "long":
                # 多仓止损价格 = 入场价格 * (1 - 止损比例)
                stop_loss_price = entry_price * (1 - stop_loss_ratio)
            else:
                # 空仓止损价格 = 入场价格 * (1 + 止损比例)
                stop_loss_price = entry_price * (1 + stop_loss_ratio)

            logger.info(f"💡 [止损计算] 入场价: {entry_price:.4f}, 方向: {direction}, 止损价: {stop_loss_price:.4f}")
            return round(stop_loss_price, self.config.price_precision)

        except Exception as e:
            logger.error(f"❌ 计算止损价格异常: {e}")
            return entry_price * 0.958 if direction == "long" else entry_price * 1.042  # 默认4.2%止损

    async def _place_stop_loss_order(self, direction: str, quantity: float, stop_loss_price: float):
        """设置止损订单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return

            # 确定止损订单方向（与开仓方向相反）
            stop_side = "sell" if direction == "long" else "buy"

            logger.info(f"🛡️ [止损订单] 设置止损: 方向={stop_side}, 数量={quantity}, 止损价={stop_loss_price:.4f}")
            logger.info(f"🛡️ [止损订单] 交易对: {self.config.custom_symbol}")

            # 根据交易所类型选择止损订单方式
            exchange_name = self.config.exchange_name.lower()

            if exchange_name == "okx":
                await self._place_okx_algo_stop_loss_order(stop_side, quantity, stop_loss_price)
            elif exchange_name == "gate":
                await self._place_gate_stop_loss_order(stop_side, quantity, stop_loss_price)
            else:
                # 默认使用限价单作为止损
                await self._place_limit_stop_loss_order(stop_side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ 设置止损订单异常: {e}")

    async def _place_okx_algo_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用OKX策略委托接口设置止损订单"""
        try:
            if hasattr(self.exchange_manager.exchange, 'place_algo_order'):
                # 使用OKX策略委托接口
                order_result = await self.exchange_manager.exchange.place_algo_order(
                    symbol=self.config.custom_symbol,
                    side=side,
                    order_type="conditional",
                    quantity=quantity,
                    trigger_price=stop_loss_price,
                    order_price=-1,  # 市价执行
                    reduce_only=True
                )

                if order_result:
                    logger.info(f"✅ [OKX策略委托] 止损订单设置成功: {order_result.order_id}")
                else:
                    logger.warning("⚠️ [OKX策略委托] 止损订单设置失败，尝试限价单")
                    await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)
            else:
                logger.warning("⚠️ OKX交易所不支持策略委托，使用限价单")
                await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ OKX策略委托止损失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_gate_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用Gate.io止损订单"""
        try:
            # Gate.io的止损订单实现
            logger.info(f"🔧 [Gate.io止损] 设置止损订单: {side}, {quantity}, {stop_loss_price}")

            # 暂时使用限价单作为备选
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

        except Exception as e:
            logger.error(f"❌ Gate.io止损订单失败: {e}")
            await self._place_limit_stop_loss_order(side, quantity, stop_loss_price)

    async def _place_limit_stop_loss_order(self, side: str, quantity: float, stop_loss_price: float):
        """使用限价单作为止损（备选方案）"""
        try:
            logger.info(f"📋 [限价止损] 设置限价止损单: {side}, {quantity}, {stop_loss_price}")

            # 这里可以实现限价单止损逻辑
            # 注意：限价单不是真正的止损，需要持续监控价格

        except Exception as e:
            logger.error(f"❌ 限价止损订单失败: {e}")

    async def _start_position_monitor(self):
        """启动持仓监控"""
        try:
            logger.info("👁️ 启动斐波那契持仓监控")

            while self.is_running:
                try:
                    await self._check_position_status()
                    await asyncio.sleep(self.config.position_check_interval)
                except Exception as e:
                    logger.error(f"❌ 持仓监控异常: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ 持仓监控启动失败: {e}")

    async def _check_position_status(self):
        """检查持仓状态"""
        try:
            # 这里实现持仓状态检查逻辑
            # 包括止盈止损检查、加仓条件检查等
            pass

        except Exception as e:
            logger.error(f"❌ 检查持仓状态异常: {e}")

    # 辅助方法
    async def _has_position(self) -> bool:
        """检查是否有持仓"""
        try:
            if not self.exchange_manager:
                return False

            # 这里实现持仓检查逻辑
            return False  # 临时返回

        except Exception as e:
            logger.error(f"❌ 检查持仓异常: {e}")
            return False

    def _check_order_cooldown(self) -> bool:
        """检查下单冷却时间"""
        try:
            if not self.last_order_time:
                return True

            elapsed = (datetime.now() - self.last_order_time).total_seconds()
            return elapsed >= self.config.order_cooldown

        except Exception as e:
            logger.error(f"❌ 检查冷却时间异常: {e}")
            return True

    async def _get_price_data(self) -> Dict:
        """获取价格数据"""
        try:
            if not self.exchange_manager:
                return {}

            # 这里实现价格数据获取逻辑
            return {
                'highs': [],
                'lows': [],
                'closes': []
            }  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取价格数据异常: {e}")
            return {}

    async def _get_current_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange_manager:
                return None

            # 这里实现当前价格获取逻辑
            return None  # 临时返回

        except Exception as e:
            logger.error(f"❌ 获取当前价格异常: {e}")
            return None

    def _calculate_position_size(self, price: float) -> float:
        """计算开仓数量"""
        try:
            # 基于初始保证金和自定义杠杆计算开仓数量
            position_value = self.config.initial_margin * self.config.custom_leverage
            quantity = position_value / price

            # 应用精度
            quantity = round(quantity, self.config.quantity_precision)

            logger.info(f"💰 [仓位计算] 价格: {price:.4f}, 保证金: {self.config.initial_margin}, 杠杆: {self.config.custom_leverage}x, 数量: {quantity}")
            return quantity

        except Exception as e:
            logger.error(f"❌ 计算开仓数量异常: {e}")
            return 0.0

    async def _place_market_order(self, direction: str, quantity: float, price: float) -> bool:
        """下市价单"""
        try:
            if not self.exchange_manager:
                logger.error("❌ 交易所管理器未设置")
                return False

            logger.info(f"📈 [市价下单] 方向: {direction}, 数量: {quantity}, 参考价格: {price:.4f}")
            logger.info(f"📊 [交易对] 使用自定义交易对: {self.config.custom_symbol}")
            logger.info(f"📊 [杠杆] 使用自定义杠杆: {self.config.custom_leverage}x")
            logger.info(f"📊 [斐波那契设置] 周期: {self.config.period}, 摆动检测: {self.config.swing_detection_period}, 确认周期: {self.config.breakout_confirmation_periods}")

            # 这里实现市价下单逻辑
            return True  # 临时返回

        except Exception as e:
            logger.error(f"❌ 市价下单异常: {e}")
            return False

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "is_running": self.is_running,
            "config": self.config.to_dict(),
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "success_rate": self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            "total_pnl": self.total_pnl,
            "add_position_count": len(self.add_position_records),
            "last_signal_time": self.last_signal_time.isoformat() if self.last_signal_time else None,
            "last_order_time": self.last_order_time.isoformat() if self.last_order_time else None,
            "custom_symbol": self.config.custom_symbol,
            "custom_leverage": self.config.custom_leverage,
            "fibonacci_period": self.config.period,
            "swing_detection_period": self.config.swing_detection_period,
            "confirmation_periods": self.config.breakout_confirmation_periods,
        }
