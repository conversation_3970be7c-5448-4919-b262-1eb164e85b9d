#!/usr/bin/env python3
"""
全面BUG检测引擎
主动发现所有潜在BUG，包括修复中引入的新BUG
"""

import ast
import os
import re
import sys
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict

class ComprehensiveBugDetector:
    """全面BUG检测器"""
    
    def __init__(self):
        self.bugs = []
        self.warnings = []
        self.critical_issues = []
        self.method_calls = defaultdict(list)
        self.method_definitions = defaultdict(list)
        self.async_methods = set()
        self.sync_methods = set()
        
    def detect_all_bugs(self) -> Dict[str, Any]:
        """检测所有BUG"""
        print("🔍 启动全面BUG检测引擎")
        print("=" * 80)
        
        # 检测1：异步一致性BUG
        self._detect_async_consistency_bugs()
        
        # 检测2：方法调用BUG
        self._detect_method_call_bugs()
        
        # 检测3：重复代码BUG
        self._detect_duplicate_code_bugs()
        
        # 检测4：命名不一致BUG
        self._detect_naming_inconsistency_bugs()
        
        # 检测5：导入和依赖BUG
        self._detect_import_dependency_bugs()
        
        # 检测6：逻辑错误BUG
        self._detect_logic_error_bugs()
        
        # 检测7：并发安全BUG
        self._detect_concurrency_bugs()
        
        # 检测8：资源泄漏BUG
        self._detect_resource_leak_bugs()
        
        return self._generate_bug_report()
    
    def _detect_async_consistency_bugs(self):
        """检测异步一致性BUG"""
        print("🔍 检测异步一致性BUG...")
        
        files_to_check = [
            'strategies/bollinger_strategy.py',
            'exchanges/okx_exchange.py',
            'gui/main_window.py',
            'core/trading_controller.py',
            'monitoring/position_monitor.py',
            'monitoring/price_monitor.py'
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                self._check_file_async_consistency(file_path)
    
    def _check_file_async_consistency(self, file_path: str):
        """检查单个文件的异步一致性"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 收集方法定义
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    method_name = node.name
                    is_async = isinstance(node, ast.AsyncFunctionDef)
                    
                    if is_async:
                        self.async_methods.add(f"{file_path}:{method_name}")
                    else:
                        self.sync_methods.add(f"{file_path}:{method_name}")
                    
                    # 检查方法内的await调用
                    for child in ast.walk(node):
                        if isinstance(child, ast.Await):
                            if not is_async:
                                self.bugs.append({
                                    'type': 'async_consistency',
                                    'severity': 'critical',
                                    'file': file_path,
                                    'method': method_name,
                                    'line': getattr(child, 'lineno', 'unknown'),
                                    'message': f"在非async方法'{method_name}'中使用了await"
                                })
                        
                        # 检查方法调用
                        if isinstance(child, ast.Call):
                            if hasattr(child.func, 'attr'):
                                called_method = child.func.attr
                                self.method_calls[f"{file_path}:{method_name}"].append(called_method)
        
        except Exception as e:
            self.warnings.append(f"解析文件{file_path}失败: {e}")
    
    def _detect_method_call_bugs(self):
        """检测方法调用BUG"""
        print("🔍 检测方法调用BUG...")
        
        # 检查我刚刚修改的方法是否被正确调用
        modified_methods = [
            'execute_auto_opening',
            'analyze_opening_opportunity_sync',
            'calculate_bollinger_bands',
            'should_add_position',
            'get_current_bollinger_data',
            '_record_opening_position',
            'add_position'
        ]
        
        for method in modified_methods:
            self._check_method_usage(method)
    
    def _check_method_usage(self, method_name: str):
        """检查特定方法的使用情况"""
        # 搜索所有文件中对该方法的调用
        search_pattern = rf'\b{method_name}\s*\('
        
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            lines = content.split('\n')
                            
                            for i, line in enumerate(lines, 1):
                                if re.search(search_pattern, line):
                                    # 检查是否使用了await
                                    if 'await' not in line and method_name in [
                                        'calculate_bollinger_bands', 'should_add_position',
                                        'get_current_bollinger_data', '_record_opening_position',
                                        'add_position'
                                    ]:
                                        self.bugs.append({
                                            'type': 'missing_await',
                                            'severity': 'critical',
                                            'file': file_path,
                                            'line': i,
                                            'method': method_name,
                                            'message': f"调用异步方法'{method_name}'时缺少await"
                                        })
                    except Exception:
                        continue
    
    def _detect_duplicate_code_bugs(self):
        """检测重复代码BUG"""
        print("🔍 检测重复代码BUG...")
        
        # 检查布林带策略文件中的重复
        bollinger_file = 'strategies/bollinger_strategy.py'
        if os.path.exists(bollinger_file):
            with open(bollinger_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有真正的重复代码
            duplicate_patterns = [
                (r'def execute_auto_opening\(.*?\):', r'def execute_auto_opening_async\(.*?\):'),
                (r'def analyze_opening_opportunity\(.*?\):', r'def analyze_opening_opportunity_sync\(.*?\):'),
                (r'def add_position\(.*?\):', r'def execute_add_position\(.*?\):')
            ]
            
            for pattern1, pattern2 in duplicate_patterns:
                matches1 = re.findall(pattern1, content, re.DOTALL)
                matches2 = re.findall(pattern2, content, re.DOTALL)
                
                if len(matches1) > 0 and len(matches2) > 0:
                    # 检查是否是真正的重复（不是包装器）
                    if 'logger.warning' not in content or '已废弃' not in content:
                        self.bugs.append({
                            'type': 'duplicate_code',
                            'severity': 'medium',
                            'file': bollinger_file,
                            'message': f"发现重复方法实现: {pattern1} 和 {pattern2}"
                        })
    
    def _detect_naming_inconsistency_bugs(self):
        """检测命名不一致BUG"""
        print("🔍 检测命名不一致BUG...")
        
        # 检查类名一致性
        class_name_issues = []
        
        # 搜索所有对BollingerStrategy的引用
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                            # 检查错误的类名引用
                            if 'BollingerStrategy' in content and 'BollingerBandStrategy' not in content:
                                lines = content.split('\n')
                                for i, line in enumerate(lines, 1):
                                    if 'BollingerStrategy' in line and 'BollingerBandStrategy' not in line:
                                        class_name_issues.append({
                                            'file': file_path,
                                            'line': i,
                                            'content': line.strip()
                                        })
                    except Exception:
                        continue
        
        if class_name_issues:
            self.bugs.append({
                'type': 'naming_inconsistency',
                'severity': 'medium',
                'message': f"发现{len(class_name_issues)}处类名不一致问题",
                'details': class_name_issues
            })
    
    def _detect_import_dependency_bugs(self):
        """检测导入和依赖BUG"""
        print("🔍 检测导入和依赖BUG...")
        
        # 检查关键文件的导入
        key_files = [
            'strategies/bollinger_strategy.py',
            'exchanges/okx_exchange.py',
            'concurrent_safety_verification.py',
            'comprehensive_trading_flow_audit.py'
        ]
        
        for file_path in key_files:
            if os.path.exists(file_path):
                self._check_file_imports(file_path)
    
    def _check_file_imports(self, file_path: str):
        """检查单个文件的导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有未使用的导入
            lines = content.split('\n')
            imports = []
            
            for line in lines:
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    imports.append(line.strip())
            
            # 检查time导入（我刚刚添加的）
            if 'import time' in content:
                if 'time.time()' not in content and 'time.' not in content:
                    self.warnings.append(f"{file_path}: 导入了time但未使用")
        
        except Exception as e:
            self.warnings.append(f"检查{file_path}导入失败: {e}")
    
    def _detect_logic_error_bugs(self):
        """检测逻辑错误BUG"""
        print("🔍 检测逻辑错误BUG...")
        
        # 检查我修改的包装器方法是否正确
        bollinger_file = 'strategies/bollinger_strategy.py'
        if os.path.exists(bollinger_file):
            with open(bollinger_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查execute_auto_opening包装器
            if 'execute_auto_opening_async(current_price, direction)' in content:
                # 检查参数是否匹配
                if 'def execute_auto_opening_async(self, current_price: float, direction: str, exchange=None' in content:
                    self.bugs.append({
                        'type': 'parameter_mismatch',
                        'severity': 'critical',
                        'file': bollinger_file,
                        'message': "execute_auto_opening包装器调用参数不匹配，缺少exchange参数"
                    })
    
    def _detect_concurrency_bugs(self):
        """检测并发安全BUG"""
        print("🔍 检测并发安全BUG...")
        
        # 检查是否有直接访问共享数据的代码
        bollinger_file = 'strategies/bollinger_strategy.py'
        if os.path.exists(bollinger_file):
            with open(bollinger_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            dangerous_patterns = [
                r'self\.price_history\.',
                r'self\.add_records\.',
                r'self\.bb_history\.'
            ]
            
            for i, line in enumerate(lines, 1):
                for pattern in dangerous_patterns:
                    if re.search(pattern, line):
                        # 检查是否在锁保护的上下文中
                        context_start = max(0, i-10)
                        context_end = min(len(lines), i+5)
                        context = '\n'.join(lines[context_start:context_end])
                        
                        if 'async with self._' not in context and 'lock' not in context:
                            self.bugs.append({
                                'type': 'concurrency_unsafe',
                                'severity': 'critical',
                                'file': bollinger_file,
                                'line': i,
                                'message': f"直接访问共享数据结构，可能不安全: {line.strip()}"
                            })
    
    def _detect_resource_leak_bugs(self):
        """检测资源泄漏BUG"""
        print("🔍 检测资源泄漏BUG...")
        
        # 检查异步资源是否正确清理
        files_to_check = [
            'exchanges/okx_exchange.py',
            'gui/main_window.py'
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有未关闭的连接
                if 'aiohttp' in content and 'session.close()' not in content:
                    self.warnings.append(f"{file_path}: 可能存在HTTP会话未关闭的问题")
    
    def _generate_bug_report(self) -> Dict[str, Any]:
        """生成BUG报告"""
        print("=" * 80)
        print("📋 生成全面BUG检测报告")
        
        critical_bugs = [bug for bug in self.bugs if bug.get('severity') == 'critical']
        medium_bugs = [bug for bug in self.bugs if bug.get('severity') == 'medium']
        
        report = {
            'total_bugs': len(self.bugs),
            'critical_bugs': len(critical_bugs),
            'medium_bugs': len(medium_bugs),
            'warnings': len(self.warnings),
            'bugs': self.bugs,
            'warnings_list': self.warnings
        }
        
        # 打印报告
        print(f"🎯 BUG检测总结:")
        print(f"   🚨 关键BUG: {len(critical_bugs)} 个")
        print(f"   ⚠️  中等BUG: {len(medium_bugs)} 个")
        print(f"   💡 警告: {len(self.warnings)} 个")
        
        if critical_bugs:
            print("\n🚨 关键BUG详情:")
            for i, bug in enumerate(critical_bugs, 1):
                print(f"   {i}. [{bug['type']}] {bug['message']}")
                if 'file' in bug:
                    print(f"      文件: {bug['file']}")
                if 'line' in bug:
                    print(f"      行号: {bug['line']}")
        
        if medium_bugs:
            print("\n⚠️  中等BUG详情:")
            for i, bug in enumerate(medium_bugs, 1):
                print(f"   {i}. [{bug['type']}] {bug['message']}")
        
        if self.warnings:
            print("\n💡 警告详情:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        return report

def main():
    """主函数"""
    detector = ComprehensiveBugDetector()
    report = detector.detect_all_bugs()
    
    if report['critical_bugs'] > 0:
        print("\n❌ 发现关键BUG，需要立即修复")
        return False
    elif report['medium_bugs'] > 0:
        print("\n⚠️  发现中等BUG，建议修复")
        return True
    else:
        print("\n✅ 未发现关键BUG")
        return True

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ BUG检测异常: {e}")
        sys.exit(1)
