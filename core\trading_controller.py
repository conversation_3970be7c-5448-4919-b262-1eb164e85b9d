"""
主交易控制器
整合所有模块，实现完整的MACD智能加仓交易流程
严格遵守异步编程原则和交易所完全分离
"""
import asyncio
import time
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from enum import Enum
import logging

from exchanges import ExchangeFactory, BaseExchange
from indicators import AsyncMACDCalculator, MACDSignal
from strategies.opening_strategy import AsyncOpeningStrategy, PositionConfig, OpeningResult
from strategies.add_position_strategy import AsyncAddPositionStrategy, AddPositionConfig, AddPositionType
from monitoring.position_monitor import AsyncPositionMonitor, PositionAlert, PositionRisk
from monitoring.price_monitor import AsyncPriceMonitor, PriceAlert, PriceDirection
from config import TradingConfig

logger = logging.getLogger(__name__)

class TradingState(Enum):
    """交易状态"""
    IDLE = "idle"                    # 空闲
    ANALYZING = "analyzing"          # 分析中
    OPENING = "opening"              # 开仓中
    MONITORING = "monitoring"        # 监控中
    ADDING = "adding"               # 加仓中
    CLOSING = "closing"             # 平仓中
    ERROR = "error"                 # 错误状态

@dataclass
class TradingSession:
    """交易会话"""
    session_id: str
    symbol: str
    start_time: int
    state: TradingState
    initial_margin: float
    leverage: int
    total_add_times: int = 0
    total_pnl: float = 0.0
    metadata: Dict[str, Any] = None

class AsyncTradingController:
    """
    异步主交易控制器
    整合MACD分析、开仓策略、持仓监控、加仓策略、价格监控
    """
    
    def __init__(self, config: TradingConfig):
        """
        初始化交易控制器
        
        Args:
            config: 交易配置
        """
        self.config = config
        self.exchange: Optional[BaseExchange] = None
        
        # 核心组件
        self.macd_calculator: Optional[AsyncMACDCalculator] = None
        self.opening_strategy: Optional[AsyncOpeningStrategy] = None
        self.add_position_strategy: Optional[AsyncAddPositionStrategy] = None
        self.position_monitor: Optional[AsyncPositionMonitor] = None
        self.price_monitor: Optional[AsyncPriceMonitor] = None
        
        # 交易状态
        self.current_session: Optional[TradingSession] = None
        self.trading_state = TradingState.IDLE
        self._is_running = False
        self._main_task: Optional[asyncio.Task] = None

        # 并发安全锁
        self._state_lock = asyncio.Lock()  # 保护交易状态
        self._session_lock = asyncio.Lock()  # 保护会话数据
        
        logger.info("异步交易控制器初始化完成")
    
    async def initialize(self) -> bool:
        """
        初始化所有组件
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化交易控制器组件...")
            
            # 验证配置
            self.config.validate_config()
            
            # 创建交易所连接
            self.exchange = await ExchangeFactory.create_exchange(
                exchange_name=self.config.EXCHANGE,
                api_key=self.config.API_KEY,
                api_secret=self.config.API_SECRET,
                passphrase=self.config.PASSPHRASE,
                sandbox=self.config.SANDBOX,
                auto_connect=True
            )
            
            # 初始化MACD计算器
            self.macd_calculator = AsyncMACDCalculator(
                fast_period=self.config.MACD_FAST,
                slow_period=self.config.MACD_SLOW,
                signal_period=self.config.MACD_SIGNAL
            )
            
            # 初始化开仓策略
            self.opening_strategy = AsyncOpeningStrategy(
                exchange=self.exchange,
                macd_calculator=self.macd_calculator
            )
            
            # 初始化持仓监控器
            self.position_monitor = AsyncPositionMonitor(self.exchange)
            
            # 初始化加仓策略
            self.add_position_strategy = AsyncAddPositionStrategy(
                exchange=self.exchange,
                position_monitor=self.position_monitor
            )
            
            # 初始化价格监控器
            self.price_monitor = AsyncPriceMonitor(self.exchange)
            
            # 注册回调函数
            await self._setup_callbacks()
            
            logger.info("交易控制器组件初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化交易控制器失败: {e}")
            return False
    
    async def _setup_callbacks(self) -> None:
        """设置回调函数"""
        try:
            # 注册持仓预警回调
            self.position_monitor.add_alert_callback(self._handle_position_alert)
            
            # 注册价格预警回调
            self.price_monitor.add_alert_callback(self._handle_price_alert)
            
            logger.info("回调函数设置完成")
            
        except Exception as e:
            logger.error(f"设置回调函数异常: {e}")
    
    async def _handle_position_alert(self, alert: PositionAlert) -> None:
        """
        处理持仓预警
        
        Args:
            alert: 持仓预警
        """
        try:
            logger.info(f"收到持仓预警: {alert.message}")
            
            if alert.alert_type == 'add_position_trigger' and alert.should_add_position:
                # 触发加仓条件，但加仓策略会自动处理
                logger.info(f"持仓预警触发加仓条件: {alert.symbol}")
                
            elif alert.alert_type == 'liquidation_warning':
                # 强平风险预警
                logger.warning(f"强平风险预警: {alert.message}")
                # 可以在这里添加紧急处理逻辑
                
        except Exception as e:
            logger.error(f"处理持仓预警异常: {e}")
    
    async def _handle_price_alert(self, alert: PriceAlert) -> None:
        """
        处理价格预警
        
        Args:
            alert: 价格预警
        """
        try:
            logger.info(f"收到价格预警: {alert.message}")
            
            # 可以根据价格预警类型执行不同的处理逻辑
            if alert.alert_type.value == 'volatility_high':
                logger.warning(f"高波动率预警: {alert.symbol}")
            elif alert.alert_type.value == 'trend_change':
                logger.info(f"趋势变化预警: {alert.symbol}")
                
        except Exception as e:
            logger.error(f"处理价格预警异常: {e}")
    
    async def start_trading_session(self, symbol: str, initial_margin: float, leverage: int) -> bool:
        """
        开始交易会话
        
        Args:
            symbol: 交易对
            initial_margin: 初始保证金
            leverage: 杠杆倍数
            
        Returns:
            bool: 是否成功开始
        """
        try:
            if self._is_running:
                logger.warning("交易会话已在运行中")
                return False
            
            # 创建交易会话
            session_id = f"{symbol}_{int(time.time())}"
            self.current_session = TradingSession(
                session_id=session_id,
                symbol=symbol,
                start_time=int(time.time()),
                state=TradingState.IDLE,
                initial_margin=initial_margin,
                leverage=leverage,
                metadata={}
            )
            
            self._is_running = True
            await self.update_trading_state(TradingState.ANALYZING)
            
            # 启动主交易循环
            self._main_task = asyncio.create_task(self._main_trading_loop())
            
            logger.info(f"交易会话已开始: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"开始交易会话异常: {e}")
            return False
    
    async def _main_trading_loop(self) -> None:
        """主交易循环"""
        try:
            logger.info("主交易循环开始")
            
            while self._is_running and self.current_session:
                try:
                    current_state = await self.get_trading_state()
                    if current_state == TradingState.ANALYZING:
                        await self._analyze_and_open()
                    elif current_state == TradingState.MONITORING:
                        await self._monitor_position()
                    elif current_state == TradingState.ERROR:
                        logger.error("交易状态错误，停止交易")
                        break
                    
                    # 短暂休眠避免过度占用CPU
                    await asyncio.sleep(1)
                    
                except asyncio.CancelledError:
                    logger.info("主交易循环被取消")
                    break
                except Exception as e:
                    logger.error(f"主交易循环异常: {e}")
                    await self.update_trading_state(TradingState.ERROR)
                    await asyncio.sleep(5)  # 错误后等待5秒
            
            logger.info("主交易循环结束")
            
        except Exception as e:
            logger.error(f"主交易循环严重异常: {e}")
        finally:
            await self._cleanup()
    
    async def _analyze_and_open(self) -> None:
        """分析市场并开仓"""
        try:
            if not self.current_session:
                return
            
            # 创建持仓配置
            position_config = PositionConfig(
                symbol=self.current_session.symbol,
                initial_margin=self.current_session.initial_margin,
                leverage=self.current_session.leverage,
                timeframe=self.config.TIMEFRAME,
                min_signal_strength=0.3
            )
            
            # 分析开仓机会
            signal = await self.opening_strategy.analyze_opening_opportunity(position_config)
            
            if signal and signal.signal_type in ['buy', 'sell']:
                logger.info(f"发现开仓机会: {signal.signal_type}, 强度: {signal.strength}")
                
                # 执行开仓
                await self.update_trading_state(TradingState.OPENING)
                result = await self.opening_strategy.execute_opening(position_config, signal)

                if result.success:
                    logger.info(f"开仓成功: {result.reason}")

                    # 切换到监控状态
                    await self.update_trading_state(TradingState.MONITORING)

                    # 启动监控
                    await self._start_monitoring()

                else:
                    logger.warning(f"开仓失败: {result.reason}")
                    await self.update_trading_state(TradingState.ANALYZING)
            else:
                # 没有开仓信号，继续分析
                await asyncio.sleep(self.config.PRICE_CHECK_INTERVAL)
                
        except Exception as e:
            logger.error(f"分析和开仓异常: {e}")
            self.trading_state = TradingState.ERROR
    
    async def _start_monitoring(self) -> None:
        """启动监控"""
        try:
            if not self.current_session:
                return
            
            symbol = self.current_session.symbol
            
            # 启动持仓监控
            await self.position_monitor.start_monitoring(
                symbols=[symbol],
                alert_points=self.config.ALERT_POINTS,
                check_interval=self.config.POSITION_CHECK_INTERVAL
            )
            
            # 启动价格监控
            await self.price_monitor.start_monitoring(
                symbols=[symbol],
                check_interval=self.config.PRICE_CHECK_INTERVAL
            )
            
            # 激活加仓策略
            self.add_position_strategy.activate()
            
            logger.info("监控系统已启动")
            
        except Exception as e:
            logger.error(f"启动监控异常: {e}")
    
    async def _monitor_position(self) -> None:
        """监控持仓状态"""
        try:
            if not self.current_session:
                return
            
            # 检查是否还有持仓
            positions = await self.exchange.get_positions(self.current_session.symbol)
            
            if not positions:
                logger.info("没有持仓，交易会话结束")
                await self.stop_trading_session()
                return
            
            # 更新会话信息
            position = positions[0]
            await self.update_session_data(
                total_pnl=position.unrealized_pnl,
                total_add_times=self.add_position_strategy.get_add_times(
                    self.current_session.symbol
                )
            )
            
            # 等待下次检查
            await asyncio.sleep(self.config.POSITION_CHECK_INTERVAL)
            
        except Exception as e:
            logger.error(f"监控持仓异常: {e}")
    
    async def stop_trading_session(self) -> None:
        """停止交易会话"""
        try:
            self._is_running = False
            
            if self._main_task and not self._main_task.done():
                self._main_task.cancel()
                try:
                    await self._main_task
                except asyncio.CancelledError:
                    pass
            
            await self._cleanup()
            
            if self.current_session:
                logger.info(f"交易会话已停止: {self.current_session.session_id}")
                self.current_session = None
            
            self.trading_state = TradingState.IDLE
            
        except Exception as e:
            logger.error(f"停止交易会话异常: {e}")
    
    async def _cleanup(self) -> None:
        """清理资源"""
        try:
            # 停止监控
            if self.position_monitor:
                await self.position_monitor.stop_monitoring()
            
            if self.price_monitor:
                await self.price_monitor.stop_monitoring()
            
            # 停用加仓策略
            if self.add_position_strategy:
                self.add_position_strategy.deactivate()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源异常: {e}")
    
    async def manual_close_position(self, symbol: str) -> bool:
        """
        手动平仓
        
        Args:
            symbol: 交易对
            
        Returns:
            bool: 平仓是否成功
        """
        try:
            if self.opening_strategy:
                position_config = PositionConfig(
                    symbol=symbol,
                    initial_margin=0,  # 平仓时不需要
                    leverage=1,
                    timeframe=self.config.TIMEFRAME
                )
                return await self.opening_strategy.close_position(position_config)
            return False
            
        except Exception as e:
            logger.error(f"手动平仓异常: {e}")
            return False
    
    def get_trading_status(self) -> Dict[str, Any]:
        """获取交易状态"""
        status = {
            'is_running': self._is_running,
            'trading_state': self.trading_state.value,
            'current_session': None
        }
        
        if self.current_session:
            status['current_session'] = {
                'session_id': self.current_session.session_id,
                'symbol': self.current_session.symbol,
                'start_time': self.current_session.start_time,
                'initial_margin': self.current_session.initial_margin,
                'leverage': self.current_session.leverage,
                'total_add_times': self.current_session.total_add_times,
                'total_pnl': self.current_session.total_pnl
            }
        
        return status
    
    async def shutdown(self) -> None:
        """关闭交易控制器"""
        try:
            await self.stop_trading_session()

            if self.exchange:
                await self.exchange.disconnect()

            logger.info("交易控制器已关闭")
        except Exception as e:
            logger.error(f"关闭交易控制器时发生异常: {e}")

    # ==================== 线程安全的状态管理方法 ====================

    async def update_trading_state(self, new_state: TradingState) -> None:
        """线程安全地更新交易状态"""
        async with self._state_lock:
            old_state = self.trading_state
            self.trading_state = new_state
            logger.debug(f"交易状态变更: {old_state.value} -> {new_state.value}")

    async def update_session_data(self, **kwargs) -> None:
        """线程安全地更新会话数据"""
        async with self._session_lock:
            if self.current_session:
                for key, value in kwargs.items():
                    if hasattr(self.current_session, key):
                        setattr(self.current_session, key, value)
                        logger.debug(f"会话数据更新: {key} = {value}")

    async def get_trading_state(self) -> TradingState:
        """线程安全地获取交易状态"""
        async with self._state_lock:
            return self.trading_state

    async def get_session_data(self) -> Optional[TradingSession]:
        """线程安全地获取会话数据副本"""
        async with self._session_lock:
            if self.current_session:
                # 返回会话数据的副本，避免外部修改
                return TradingSession(
                    session_id=self.current_session.session_id,
                    symbol=self.current_session.symbol,
                    start_time=self.current_session.start_time,
                    state=self.current_session.state,
                    initial_margin=self.current_session.initial_margin,
                    leverage=self.current_session.leverage,
                    total_add_times=self.current_session.total_add_times,
                    total_pnl=self.current_session.total_pnl,
                    metadata=self.current_session.metadata.copy() if self.current_session.metadata else None
                )
            return None
