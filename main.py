"""
MACD智能加仓交易系统主程序
基于OKX和Gate.io交易所的异步交易系统
"""
import asyncio
import logging
import signal
import sys
from typing import Optional

from config import TradingConfig
from core import AsyncTradingController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class TradingApp:
    """交易应用主类"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.controller: Optional[AsyncTradingController] = None
        self._shutdown_event = asyncio.Event()
    
    async def initialize(self) -> bool:
        """初始化应用"""
        try:
            logger.info("=== MACD智能加仓交易系统启动 ===")
            logger.info(f"交易所: {self.config.EXCHANGE}")
            logger.info(f"交易对: {self.config.SYMBOL}")
            logger.info(f"杠杆: {self.config.LEVERAGE}x")
            logger.info(f"初始保证金: {self.config.INITIAL_MARGIN} USDT")
            logger.info(f"预警点数: {self.config.ALERT_POINTS}")
            logger.info(f"加仓类型: {self.config.ADD_POSITION_TYPE}")
            
            # 验证配置
            self.config.validate_config()
            
            # 创建交易控制器
            self.controller = AsyncTradingController(self.config)
            
            # 初始化控制器
            success = await self.controller.initialize()
            if not success:
                logger.error("交易控制器初始化失败")
                return False
            
            logger.info("交易系统初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    async def start_trading(self) -> None:
        """开始交易"""
        try:
            if not self.controller:
                logger.error("交易控制器未初始化")
                return
            
            logger.info("开始交易会话...")
            
            # 开始交易会话
            success = await self.controller.start_trading_session(
                symbol=self.config.SYMBOL,
                initial_margin=self.config.INITIAL_MARGIN,
                leverage=self.config.LEVERAGE
            )
            
            if success:
                logger.info("交易会话已开始")
                
                # 等待交易完成或用户中断
                await self._wait_for_completion()
                
            else:
                logger.error("启动交易会话失败")
                
        except Exception as e:
            logger.error(f"交易过程异常: {e}")
    
    async def _wait_for_completion(self) -> None:
        """等待交易完成"""
        try:
            logger.info("交易系统运行中... (按 Ctrl+C 停止)")
            
            # 定期显示状态
            while not self._shutdown_event.is_set():
                try:
                    # 等待5秒或shutdown事件
                    await asyncio.wait_for(self._shutdown_event.wait(), timeout=30.0)
                    break
                except asyncio.TimeoutError:
                    # 显示交易状态
                    await self._show_status()
                    
        except Exception as e:
            logger.error(f"等待交易完成异常: {e}")
    
    async def _show_status(self) -> None:
        """显示交易状态"""
        try:
            if not self.controller:
                return
            
            status = self.controller.get_trading_status()
            
            logger.info("=== 交易状态 ===")
            logger.info(f"运行状态: {'运行中' if status['is_running'] else '已停止'}")
            logger.info(f"交易状态: {status['trading_state']}")
            
            if status['current_session']:
                session = status['current_session']
                logger.info(f"交易对: {session['symbol']}")
                logger.info(f"初始保证金: {session['initial_margin']} USDT")
                logger.info(f"杠杆: {session['leverage']}x")
                logger.info(f"加仓次数: {session['total_add_times']}")
                logger.info(f"未实现盈亏: {session['total_pnl']:.4f} USDT")
            
            logger.info("================")
            
        except Exception as e:
            logger.error(f"显示状态异常: {e}")
    
    async def shutdown(self) -> None:
        """关闭应用"""
        try:
            logger.info("正在关闭交易系统...")
            
            self._shutdown_event.set()
            
            if self.controller:
                await self.controller.shutdown()
            
            logger.info("交易系统已关闭")
            
        except Exception as e:
            logger.error(f"关闭异常: {e}")
    
    def setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备关闭...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """主函数"""
    app = TradingApp()
    
    try:
        # 设置信号处理器
        app.setup_signal_handlers()
        
        # 初始化应用
        if not await app.initialize():
            logger.error("应用初始化失败")
            return
        
        # 开始交易
        await app.start_trading()
        
    except KeyboardInterrupt:
        logger.info("用户中断")
    except Exception as e:
        logger.error(f"主程序异常: {e}")
    finally:
        await app.shutdown()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
    finally:
        logger.info("程序结束")
