#!/usr/bin/env python3
"""
简单验证插针策略集成
"""

import sys
import os

def main():
    print("🔍 验证插针策略集成")
    
    # 检查文件存在
    files = [
        "indicators/pinbar_calculator.py",
        "strategies/pinbar_strategy.py"
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
    
    # 检查导入
    try:
        from indicators.pinbar_calculator import PinBarCalculator
        from strategies.pinbar_strategy import PinBarStrategy, PinBarConfig
        print("✅ 导入成功")
        
        # 测试创建
        config = PinBarConfig()
        strategy = PinBarStrategy(config)
        calculator = PinBarCalculator()
        
        print(f"✅ 创建成功: {config.custom_symbol}")
        print("🎉 插针策略集成验证通过")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
