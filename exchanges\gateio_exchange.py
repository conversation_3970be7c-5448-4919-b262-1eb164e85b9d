"""
Gate.io交易所异步适配器
严格遵守交易所完全分离原则
"""
import asyncio
import aiohttp
import hmac
import hashlib
import json
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging
import websockets

from .base_exchange import (
    BaseExchange, KlineData, PositionInfo, OrderInfo, 
    OrderSide, OrderType, PositionSide
)

logger = logging.getLogger(__name__)

class GateIOExchange(BaseExchange):
    """Gate.io交易所异步适配器"""
    
    def __init__(self, api_key: str, api_secret: str, passphrase: Optional[str] = None, sandbox: bool = True):
        """
        初始化Gate.io交易所连接
        
        Args:
            api_key: API密钥
            api_secret: API密钥
            passphrase: API密码短语（Gate.io不需要）
            sandbox: 是否使用沙盒环境
        """
        super().__init__(api_key, api_secret, passphrase, sandbox)
        
        # Gate.io API端点
        if sandbox:
            self.base_url = "https://fx-api-testnet.gateio.ws"
            self.ws_url = "wss://fx-ws-testnet.gateio.ws/v4/ws"
        else:
            self.base_url = "https://api.gateio.ws"
            self.ws_url = "wss://fx-ws.gateio.ws/v4/ws"
        
        self._price_callbacks = {}
        self._position_callbacks = []
        
    def _generate_signature(self, method: str, url: str, query_string: str, payload: str, timestamp: str) -> str:
        """生成Gate.io API签名"""
        body_hash = hashlib.sha512(payload.encode('utf-8')).hexdigest()
        sign_string = f"{method}\n{url}\n{query_string}\n{body_hash}\n{timestamp}"
        
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha512
        ).hexdigest()
        
        return signature
    
    def _get_headers(self, method: str, url: str, query_string: str = "", payload: str = "") -> Dict[str, str]:
        """获取请求头"""
        timestamp = str(int(time.time()))
        signature = self._generate_signature(method, url, query_string, payload, timestamp)
        
        return {
            'KEY': self.api_key,
            'SIGN': signature,
            'Timestamp': timestamp,
            'Content-Type': 'application/json'
        }
    
    async def connect(self) -> bool:
        """建立异步连接"""
        try:
            self._session = aiohttp.ClientSession()
            
            # 测试连接
            url = "/api/v4/futures/usdt/accounts"
            headers = self._get_headers('GET', url)
            
            async with self._session.get(
                f"{self.base_url}{url}",
                headers=headers
            ) as response:
                if response.status == 200:
                    self._is_connected = True
                    logger.info("Gate.io连接成功")
                    return True
                else:
                    logger.error(f"Gate.io连接失败: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Gate.io连接异常: {e}")
            return False
    
    async def disconnect(self) -> None:
        """断开连接"""
        if self._session:
            await self._session.close()
        if self._ws_connection:
            await self._ws_connection.close()
        self._is_connected = False
        logger.info("Gate.io连接已断开")
    
    def get_supported_timeframes(self) -> List[str]:
        """获取Gate.io支持的时间周期列表"""
        # Gate.io支持的时间周期可能比OKX少，15d和1M需要验证
        return ["1m", "5m", "10m", "15m", "30m", "1h", "4h", "1d"]

    async def get_klines(self, symbol: str, timeframe: str, limit: int = 100) -> List[KlineData]:
        """获取K线数据"""
        try:
            # 验证时间周期
            if not self.validate_timeframe(timeframe):
                logger.error(f"Gate.io不支持的时间周期: {timeframe}")
                # 对于不支持的长周期，尝试使用1日替代
                if timeframe in ["15d", "1M"]:
                    logger.warning(f"Gate.io不支持 {timeframe}，使用1日周期替代")
                    timeframe = "1d"
                else:
                    return []

            # 转换时间周期格式 - Gate.io API格式映射
            tf_map = {
                '1m': '1m',     # 1分钟
                '5m': '5m',     # 5分钟
                '10m': '10m',   # 10分钟
                '15m': '15m',   # 15分钟
                '30m': '30m',   # 30分钟
                '1h': '1h',     # 1小时
                '4h': '4h',     # 4小时
                '1d': '1d',     # 1日
                '15d': '1d',    # 15日用1日替代
                '1M': '1d'      # 1月用1日替代
            }
            gate_timeframe = tf_map.get(timeframe, '30m')

            logger.debug(f"Gate.io时间周期转换: {timeframe} -> {gate_timeframe}")
            
            # 转换交易对格式
            gate_symbol = symbol.replace('/', '_')
            
            url = f"/api/v4/futures/usdt/candlesticks"
            params = {
                'contract': gate_symbol,
                'interval': gate_timeframe,
                'limit': limit
            }
            
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            headers = self._get_headers('GET', url, query_string)
            
            async with self._session.get(
                f"{self.base_url}{url}",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    klines = []
                    for item in data:
                        klines.append(KlineData(
                            timestamp=int(item['t']) * 1000,  # Gate.io返回秒级时间戳
                            open=float(item['o']),
                            high=float(item['h']),
                            low=float(item['l']),
                            close=float(item['c']),
                            volume=float(item['v'])
                        ))
                    return klines
                else:
                    logger.error(f"获取K线数据失败: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取K线数据异常: {e}")
            return []
    
    async def get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        try:
            gate_symbol = symbol.replace('/', '_')
            
            url = f"/api/v4/futures/usdt/tickers"
            params = {'contract': gate_symbol}
            query_string = f"contract={gate_symbol}"
            headers = self._get_headers('GET', url, query_string)
            
            async with self._session.get(
                f"{self.base_url}{url}",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data:
                        return float(data[0]['last'])
                    else:
                        logger.error("获取价格数据为空")
                        return 0.0
                else:
                    logger.error(f"获取价格失败: {response.status}")
                    return 0.0
                    
        except Exception as e:
            logger.error(f"获取价格异常: {e}")
            return 0.0
    
    async def get_positions(self, symbol: Optional[str] = None) -> List[PositionInfo]:
        """获取持仓信息"""
        try:
            url = "/api/v4/futures/usdt/positions"
            query_string = ""
            if symbol:
                gate_symbol = symbol.replace('/', '_')
                query_string = f"contract={gate_symbol}"
            
            headers = self._get_headers('GET', url, query_string)
            params = {'contract': gate_symbol} if symbol else {}
            
            async with self._session.get(
                f"{self.base_url}{url}",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    positions = []
                    for item in data:
                        if float(item['size']) != 0:  # 只返回有持仓的
                            positions.append(PositionInfo(
                                symbol=item['contract'].replace('_', '/'),
                                side=PositionSide.LONG if float(item['size']) > 0 else PositionSide.SHORT,
                                size=abs(float(item['size'])),
                                entry_price=float(item['entry_price']) if item['entry_price'] else 0.0,
                                mark_price=float(item['mark_price']) if item['mark_price'] else 0.0,
                                unrealized_pnl=float(item['unrealised_pnl']) if item['unrealised_pnl'] else 0.0,
                                liquidation_price=float(item['liq_price']) if item['liq_price'] else 0.0,
                                margin=float(item['margin']) if item['margin'] else 0.0,
                                leverage=int(float(item['leverage'])) if item['leverage'] else 1
                            ))
                    return positions
                else:
                    logger.error(f"获取持仓失败: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取持仓异常: {e}")
            return []

    async def place_market_order(self, symbol: str, side: OrderSide, amount: float,
                                position_side: Optional[PositionSide] = None) -> OrderInfo:
        """下市价单"""
        try:
            gate_symbol = symbol.replace('/', '_')

            # Gate.io使用正负数表示多空方向
            size = amount if side == OrderSide.BUY else -amount

            order_data = {
                'contract': gate_symbol,
                'size': int(size),  # Gate.io期货数量为整数
                'price': '0',  # 市价单价格为0
                'tif': 'ioc'  # 立即成交或取消
            }

            url = "/api/v4/futures/usdt/orders"
            payload = json.dumps(order_data)
            headers = self._get_headers('POST', url, "", payload)

            async with self._session.post(
                f"{self.base_url}{url}",
                headers=headers,
                data=payload
            ) as response:
                if response.status == 201:
                    data = await response.json()
                    return OrderInfo(
                        order_id=str(data['id']),
                        symbol=symbol,
                        side=side,
                        type=OrderType.MARKET,
                        amount=amount,
                        price=None,
                        status=data['status'],
                        filled=0.0,
                        remaining=amount,
                        timestamp=int(time.time() * 1000)
                    )
                else:
                    error_text = await response.text()
                    logger.error(f"下单失败: {response.status} - {error_text}")
                    raise Exception(f"下单失败: {response.status} - {error_text}")

        except Exception as e:
            logger.error(f"下单异常: {e}")
            raise e

    async def place_limit_order(self, symbol: str, side: OrderSide, amount: float,
                               price: float, position_side: Optional[PositionSide] = None) -> OrderInfo:
        """下限价单"""
        try:
            gate_symbol = symbol.replace('/', '_')

            # Gate.io使用正负数表示多空方向
            size = amount if side == OrderSide.BUY else -amount

            order_data = {
                'contract': gate_symbol,
                'size': int(size),
                'price': str(price),
                'tif': 'gtc'  # 撤单前有效
            }

            url = "/api/v4/futures/usdt/orders"
            payload = json.dumps(order_data)
            headers = self._get_headers('POST', url, "", payload)

            async with self._session.post(
                f"{self.base_url}{url}",
                headers=headers,
                data=payload
            ) as response:
                if response.status == 201:
                    data = await response.json()
                    return OrderInfo(
                        order_id=str(data['id']),
                        symbol=symbol,
                        side=side,
                        type=OrderType.LIMIT,
                        amount=amount,
                        price=price,
                        status=data['status'],
                        filled=0.0,
                        remaining=amount,
                        timestamp=int(time.time() * 1000)
                    )
                else:
                    error_text = await response.text()
                    logger.error(f"下限价单失败: {response.status} - {error_text}")
                    raise Exception(f"下限价单失败: {response.status} - {error_text}")

        except Exception as e:
            logger.error(f"下限价单异常: {e}")
            raise e

    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """取消订单"""
        try:
            url = f"/api/v4/futures/usdt/orders/{order_id}"
            headers = self._get_headers('DELETE', url)

            async with self._session.delete(
                f"{self.base_url}{url}",
                headers=headers
            ) as response:
                return response.status == 200

        except Exception as e:
            logger.error(f"取消订单异常: {e}")
            return False

    async def get_order_status(self, symbol: str, order_id: str) -> OrderInfo:
        """获取订单状态"""
        try:
            url = f"/api/v4/futures/usdt/orders/{order_id}"
            headers = self._get_headers('GET', url)

            async with self._session.get(
                f"{self.base_url}{url}",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()

                    # 判断订单方向
                    size = int(data['size'])
                    side = OrderSide.BUY if size > 0 else OrderSide.SELL
                    amount = abs(size)

                    return OrderInfo(
                        order_id=str(data['id']),
                        symbol=symbol,
                        side=side,
                        type=OrderType.MARKET if data['price'] == '0' else OrderType.LIMIT,
                        amount=amount,
                        price=float(data['price']) if data['price'] != '0' else None,
                        status=data['status'],
                        filled=abs(int(data['size']) - int(data['left'])),
                        remaining=abs(int(data['left'])),
                        timestamp=int(data['create_time']) * 1000
                    )
                else:
                    error_text = await response.text()
                    logger.error(f"获取订单状态失败: {response.status} - {error_text}")
                    raise Exception(f"获取订单状态失败: {response.status} - {error_text}")

        except Exception as e:
            logger.error(f"获取订单状态异常: {e}")
            raise e

    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆"""
        try:
            gate_symbol = symbol.replace('/', '_')

            order_data = {
                'contract': gate_symbol,
                'leverage': str(leverage)
            }

            url = "/api/v4/futures/usdt/positions"
            payload = json.dumps(order_data)
            headers = self._get_headers('POST', url, "", payload)

            async with self._session.post(
                f"{self.base_url}{url}",
                headers=headers,
                data=payload
            ) as response:
                return response.status == 200

        except Exception as e:
            logger.error(f"设置杠杆异常: {e}")
            return False

    async def get_account_balance(self) -> Dict[str, float]:
        """获取账户余额"""
        try:
            url = "/api/v4/futures/usdt/accounts"
            headers = self._get_headers('GET', url)

            async with self._session.get(
                f"{self.base_url}{url}",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    balances = {}
                    balances['USDT'] = float(data['available']) if data['available'] else 0.0
                    return balances
                else:
                    logger.error(f"获取账户余额失败: {response.status}")
                    return {}

        except Exception as e:
            logger.error(f"获取账户余额异常: {e}")
            return {}

    async def subscribe_price_stream(self, symbol: str, callback) -> None:
        """订阅价格流"""
        try:
            gate_symbol = symbol.replace('/', '_')

            async def price_handler():
                async with websockets.connect(self.ws_url) as websocket:
                    # 订阅ticker数据
                    subscribe_msg = {
                        "time": int(time.time()),
                        "channel": "futures.tickers",
                        "event": "subscribe",
                        "payload": [gate_symbol]
                    }
                    await websocket.send(json.dumps(subscribe_msg))

                    async for message in websocket:
                        data = json.loads(message)
                        if data.get('event') == 'update' and 'result' in data:
                            result = data['result']
                            if 'last' in result:
                                price = float(result['last'])
                                await callback(symbol, price)

            # 启动价格监听任务
            task = asyncio.create_task(price_handler())
            self.add_background_task(task)

        except Exception as e:
            logger.error(f"订阅价格流异常: {e}")

    async def subscribe_position_stream(self, callback) -> None:
        """订阅持仓流"""
        try:
            async def position_handler():
                # Gate.io需要认证的WebSocket连接
                timestamp = str(int(time.time()))
                sign_string = f"channel=futures.positions&event=subscribe&time={timestamp}"
                signature = hmac.new(
                    self.api_secret.encode('utf-8'),
                    sign_string.encode('utf-8'),
                    hashlib.sha512
                ).hexdigest()

                async with websockets.connect(self.ws_url) as websocket:
                    # 认证
                    auth_msg = {
                        "time": int(timestamp),
                        "channel": "futures.login",
                        "event": "api",
                        "payload": {
                            "api_key": self.api_key,
                            "timestamp": timestamp,
                            "signature": signature
                        }
                    }
                    await websocket.send(json.dumps(auth_msg))

                    # 订阅持仓数据
                    subscribe_msg = {
                        "time": int(time.time()),
                        "channel": "futures.positions",
                        "event": "subscribe",
                        "payload": ["!all"]
                    }
                    await websocket.send(json.dumps(subscribe_msg))

                    async for message in websocket:
                        data = json.loads(message)
                        if data.get('event') == 'update' and 'result' in data:
                            positions = []
                            for item in data['result']:
                                if float(item['size']) != 0:
                                    position = PositionInfo(
                                        symbol=item['contract'].replace('_', '/'),
                                        side=PositionSide.LONG if float(item['size']) > 0 else PositionSide.SHORT,
                                        size=abs(float(item['size'])),
                                        entry_price=float(item['entry_price']) if item['entry_price'] else 0.0,
                                        mark_price=float(item['mark_price']) if item['mark_price'] else 0.0,
                                        unrealized_pnl=float(item['unrealised_pnl']) if item['unrealised_pnl'] else 0.0,
                                        liquidation_price=float(item['liq_price']) if item['liq_price'] else 0.0,
                                        margin=float(item['margin']) if item['margin'] else 0.0,
                                        leverage=int(float(item['leverage'])) if item['leverage'] else 1
                                    )
                                    positions.append(position)

                            if positions:
                                await callback(positions)

            # 启动持仓监听任务
            task = asyncio.create_task(position_handler())
            self.add_background_task(task)

        except Exception as e:
            logger.error(f"订阅持仓流异常: {e}")

    def normalize_symbol(self, symbol: str) -> str:
        """
        标准化Gate.io交易对符号格式

        Args:
            symbol: 原始交易对符号

        Returns:
            str: Gate.io格式的交易对符号 (如: BTC_USDT)
        """
        try:
            symbol = symbol.upper().strip()

            # 如果已经是Gate.io格式，直接返回
            if '_' in symbol and '-' not in symbol:
                return symbol

            # 处理不同格式的输入
            if '/' in symbol:
                # BTC/USDT -> BTC_USDT
                return symbol.replace('/', '_')
            elif '-SWAP' in symbol:
                # BTC-USDT-SWAP -> BTC_USDT (OKX格式转换)
                base_quote = symbol.replace('-SWAP', '').replace('-', '_')
                return base_quote
            elif '-' in symbol and '-SWAP' not in symbol:
                # BTC-USDT -> BTC_USDT (OKX现货格式转换)
                return symbol.replace('-', '_')

            # 如果无法识别格式，假设是基础货币，添加USDT
            if symbol and not any(sep in symbol for sep in ['/', '-', '_']):
                return f"{symbol}_USDT"

            return symbol

        except Exception as e:
            logger.error(f"Gate.io符号格式化失败: {e}")
            return symbol

    def validate_symbol_format(self, symbol: str) -> bool:
        """
        验证Gate.io交易对格式

        Args:
            symbol: 交易对符号

        Returns:
            bool: 格式是否正确
        """
        try:
            if not super().validate_symbol_format(symbol):
                return False

            symbol = symbol.upper().strip()

            # Gate.io期货格式: BASE_QUOTE
            if '_' in symbol:
                parts = symbol.split('_')
                return len(parts) == 2 and all(len(part) >= 2 for part in parts)

            return False

        except Exception as e:
            logger.error(f"Gate.io符号格式验证失败: {e}")
            return False

    async def get_position_stop_loss_price(self, symbol: str) -> dict:
        """
        获取Gate.io持仓的止损价格

        Args:
            symbol: 交易对符号

        Returns:
            dict: 止损价格信息
        """
        try:
            from datetime import datetime

            if not self.is_connected:
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': datetime.now(),
                    'error': 'Gate.io交易所未连接'
                }

            # 标准化交易对格式
            normalized_symbol = self.normalize_symbol(symbol)

            # 获取持仓信息
            try:
                positions = self.client.futures_get_positions(settle='usdt', contract=normalized_symbol)

                if not positions:
                    return {
                        'success': False,
                        'stop_loss_price': 0.0,
                        'timestamp': datetime.now(),
                        'error': 'Gate.io未返回持仓数据'
                    }

                # 查找有效持仓
                for position in positions:
                    if position.get('size') and int(position.get('size', 0)) != 0:
                        # 获取止损价格（Gate.io可能在订单中设置止损）
                        # 先尝试从持仓信息获取
                        stop_loss_price = position.get('stop_loss_price', '0')

                        if stop_loss_price and float(stop_loss_price) > 0:
                            return {
                                'success': True,
                                'stop_loss_price': float(stop_loss_price),
                                'timestamp': datetime.now(),
                                'error': None
                            }
                        else:
                            # 如果持仓信息中没有止损价，根据持仓信息计算
                            entry_price = float(position.get('entry_price', 0))
                            position_size = int(position.get('size', 0))

                            if entry_price > 0:
                                # 假设5%的止损比例（可以从配置获取）
                                stop_loss_ratio = 0.05
                                if position_size > 0:  # 多仓
                                    calculated_stop_loss = entry_price * (1 - stop_loss_ratio)
                                else:  # 空仓
                                    calculated_stop_loss = entry_price * (1 + stop_loss_ratio)

                                return {
                                    'success': True,
                                    'stop_loss_price': calculated_stop_loss,
                                    'timestamp': datetime.now(),
                                    'error': None,
                                    'calculated': True  # 标记为计算值
                                }

                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': datetime.now(),
                    'error': 'Gate.io未找到有效持仓'
                }

            except Exception as api_error:
                logger.error(f"Gate.io API调用失败: {api_error}")
                return {
                    'success': False,
                    'stop_loss_price': 0.0,
                    'timestamp': datetime.now(),
                    'error': f'Gate.io API调用失败: {str(api_error)}'
                }

        except Exception as e:
            logger.error(f"Gate.io获取止损价格失败: {e}")
            return {
                'success': False,
                'stop_loss_price': 0.0,
                'timestamp': datetime.now(),
                'error': f'Gate.io获取止损价格异常: {str(e)}'
            }

    async def place_order(self, symbol: str, side: OrderSide, order_type: str,
                         quantity: float, price: Optional[float] = None) -> Optional[OrderInfo]:
        """
        下单

        Args:
            symbol: 交易对符号
            side: 订单方向
            order_type: 订单类型 ("market", "limit", "stop_market")
            quantity: 订单数量
            price: 订单价格（限价单和止损单需要）

        Returns:
            Optional[OrderInfo]: 订单信息，失败时返回None
        """
        try:
            if not self.is_connected:
                logger.error("Gate.io交易所未连接")
                return None

            # 标准化交易对格式
            normalized_symbol = self.normalize_symbol(symbol)

            # 构建订单参数
            order_data = {
                "currency_pair": normalized_symbol,
                "side": "buy" if side == OrderSide.BUY else "sell",
                "type": order_type,
                "amount": str(quantity)
            }

            # 添加价格参数
            if order_type in ["limit", "stop_market"] and price:
                if order_type == "limit":
                    order_data["price"] = str(price)
                elif order_type == "stop_market":
                    order_data["stop_price"] = str(price)

            # 发送订单请求
            body = json.dumps(order_data)
            headers = self._get_headers('POST', '/api/v4/spot/orders', body)

            async with self._session.post(
                f"{self.base_url}/api/v4/spot/orders",
                headers=headers,
                data=body
            ) as response:
                if response.status == 201:  # Gate.io创建订单返回201
                    result = await response.json()

                    # 创建OrderInfo对象
                    order_info = OrderInfo(
                        order_id=result.get('id', ''),
                        symbol=symbol,
                        side=side,
                        type=OrderType.MARKET if order_type == "market" else OrderType.LIMIT,
                        amount=quantity,
                        price=price or 0.0,
                        status=result.get('status', 'submitted'),
                        filled=float(result.get('filled_amount', 0)),
                        remaining=quantity - float(result.get('filled_amount', 0)),
                        timestamp=int(datetime.now().timestamp())
                    )

                    logger.info(f"Gate.io订单提交成功: {order_info.order_id}")
                    return order_info
                else:
                    logger.error(f"Gate.io API调用失败: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"Gate.io下单异常: {e}")
            return None
