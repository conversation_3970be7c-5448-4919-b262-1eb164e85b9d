"""
BIT交易系统改进实施脚本
运行单元测试、性能监控、日志优化和GUI交互性检测
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_section(title):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-" * 40)

def run_command(command, description):
    """运行命令"""
    print(f"▶️ {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 成功")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print("❌ 失败")
            if result.stderr:
                print("错误:")
                print(result.stderr)
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def main():
    """主函数"""
    print_header("BIT交易系统短期改进实施")
    
    # 检查Python环境
    print_section("环境检查")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 任务1: 运行单元测试
    print_header("任务1: 单元测试 - 提升测试覆盖率到60%以上")
    
    print_section("1.1 运行核心模块测试")
    success1 = run_command("python -m pytest tests/test_core.py -v", "测试核心模块")
    
    print_section("1.2 运行交易所模块测试")
    success2 = run_command("python -m pytest tests/test_exchanges.py -v", "测试交易所模块")
    
    print_section("1.3 运行指标模块测试")
    success3 = run_command("python -m pytest tests/test_indicators.py -v", "测试指标模块")
    
    print_section("1.4 运行策略模块测试")
    success4 = run_command("python -m pytest tests/test_strategies.py -v", "测试策略模块")
    
    print_section("1.5 运行监控模块测试")
    success5 = run_command("python -m pytest tests/test_monitoring.py -v", "测试监控模块")
    
    print_section("1.6 运行GUI交互测试")
    success6 = run_command("python -m pytest tests/test_gui_interaction.py -v", "测试GUI交互")
    
    print_section("1.7 运行所有测试并生成报告")
    success7 = run_command("python tests/run_tests.py", "运行完整测试套件")
    
    # 任务2: 性能监控
    print_header("任务2: 性能监控 - 添加性能指标收集")
    
    print_section("2.1 测试性能监控模块")
    success8 = run_command("python -c \"from utils.performance_monitor import performance_monitor; print('性能监控模块导入成功')\"", 
                          "测试性能监控模块导入")
    
    print_section("2.2 启动性能监控演示")
    demo_code = '''
import asyncio
import time
from utils.performance_monitor import performance_monitor, monitor_performance

@monitor_performance("demo_function")
async def demo_function():
    await asyncio.sleep(0.1)
    return "完成"

async def main():
    await performance_monitor.start_monitoring()
    
    # 运行一些演示任务
    for i in range(5):
        result = await demo_function()
        performance_monitor.increment_counter("demo_calls")
        performance_monitor.set_gauge("demo_iteration", i)
    
    # 收集系统指标
    metrics = performance_monitor.collect_system_metrics()
    if metrics:
        print(f"CPU使用率: {metrics.cpu_percent:.1f}%")
        print(f"内存使用率: {metrics.memory_percent:.1f}%")
    
    # 获取性能摘要
    summary = performance_monitor.get_metrics_summary()
    print("性能指标摘要:")
    for key, value in summary.items():
        if key != 'timestamp':
            print(f"  {key}: {value}")
    
    await performance_monitor.stop_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    with open("temp_performance_demo.py", "w", encoding="utf-8") as f:
        f.write(demo_code)
    
    success9 = run_command("python temp_performance_demo.py", "运行性能监控演示")
    
    # 清理临时文件
    try:
        os.remove("temp_performance_demo.py")
    except:
        pass
    
    # 任务3: 日志优化
    print_header("任务3: 日志优化 - 优化日志级别和格式")
    
    print_section("3.1 测试日志配置模块")
    success10 = run_command("python -c \"from utils.logging_config import setup_logging; setup_logging(); print('日志配置模块测试成功')\"", 
                           "测试日志配置模块")
    
    print_section("3.2 测试日志功能")
    log_demo_code = '''
import logging
from utils.logging_config import setup_logging, logging_config

# 设置日志配置
setup_logging(log_level="INFO", enable_structured=False)

# 获取不同模块的日志记录器
core_logger = logging.getLogger("core")
strategies_logger = logging.getLogger("strategies")
exchanges_logger = logging.getLogger("exchanges")

# 测试日志记录
core_logger.info("核心模块日志测试")
strategies_logger.warning("策略模块警告测试")
exchanges_logger.error("交易所模块错误测试")

# 获取日志统计
stats = logging_config.get_log_stats()
print("日志统计信息:")
for key, value in stats.items():
    print(f"  {key}: {value}")

print("日志配置测试完成")
'''
    
    with open("temp_logging_demo.py", "w", encoding="utf-8") as f:
        f.write(log_demo_code)
    
    success11 = run_command("python temp_logging_demo.py", "运行日志功能演示")
    
    # 清理临时文件
    try:
        os.remove("temp_logging_demo.py")
    except:
        pass
    
    # 任务4: GUI交互性检测
    print_header("任务4: GUI交互性检测 - 检测代码层和GUI界面的交互性")
    
    print_section("4.1 运行GUI交互性检测")
    success12 = run_command("python utils/gui_interaction_checker.py", "检测GUI交互性")
    
    # 生成总结报告
    print_header("改进实施总结报告")
    
    tasks = [
        ("核心模块测试", success1),
        ("交易所模块测试", success2),
        ("指标模块测试", success3),
        ("策略模块测试", success4),
        ("监控模块测试", success5),
        ("GUI交互测试", success6),
        ("完整测试套件", success7),
        ("性能监控模块", success8),
        ("性能监控演示", success9),
        ("日志配置模块", success10),
        ("日志功能演示", success11),
        ("GUI交互性检测", success12)
    ]
    
    successful_tasks = sum(1 for _, success in tasks if success)
    total_tasks = len(tasks)
    
    print(f"\n📊 任务完成情况: {successful_tasks}/{total_tasks}")
    print("\n详细结果:")
    
    for task_name, success in tasks:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {task_name}: {status}")
    
    success_rate = successful_tasks / total_tasks * 100
    print(f"\n🎯 成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 改进实施基本成功!")
    elif success_rate >= 60:
        print("⚠️ 改进实施部分成功，需要关注失败的任务")
    else:
        print("❌ 改进实施需要进一步调试")
    
    print("\n📋 后续建议:")
    print("1. 检查失败的任务并修复相关问题")
    print("2. 定期运行测试套件确保代码质量")
    print("3. 监控性能指标并优化瓶颈")
    print("4. 持续改进日志记录和GUI交互性")
    
    print("\n🔗 相关文件:")
    print("- 测试报告: tests/test.log")
    print("- 性能日志: logs/performance.log")
    print("- 系统日志: logs/bit_trading.log")
    print("- 错误日志: logs/errors.log")

if __name__ == "__main__":
    main()
